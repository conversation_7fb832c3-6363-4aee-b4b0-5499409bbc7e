<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>美食调料搭配表</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            display: flex;
            gap: 40px; /* 增加间距 */
            margin-bottom: 20px;
            padding: 20px;
        }
        .text-section {
            flex: 1;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .text-section h2 {
            margin-bottom: 10px;
            color: #333;
        }
        textarea {
            width: 100%;
            height: 300px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            resize: vertical;
        }
        #processBtn {
            display: block;
            width: 200px;
            margin: 20px auto;
            padding: 10px 20px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        #processBtn:hover {
            background-color: #45a049;
        }
        .options-section {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            /**margin-bottom: 20px;**/
            text-align: center;
        }
        .options-section select {
            padding: 8px;
            font-size: 16px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-left: 10px;
            width: 200px;
        }
        .options-section label {
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="options-section">
        <label for="countrySelect">选择国家：</label>
        <select id="countrySelect">
            <option value="US">美国 (US)</option>
            <option value="CA">加拿大 (CA)</option>
            <option value="MX">墨西哥 (MX)</option>
            <option value="UK">英国 (UK)</option>
            <option value="AU">澳大利亚 (AU)</option>
            <option value="JP">日本 (JP)</option>
            <option value="DE">德国 (DE)</option>
            <option value="FR">法国 (FR)</option>
            <option value="IT">意大利 (IT)</option>
            <option value="BR">巴西 (BR)</option>
            <option value="AR">阿根廷 (AR)</option>
            <option value="VN">越南 (VN)</option>
            <option value="CL">智利 (CL)</option>
            <option value="IL">以色列 (IL)</option>
            <option value="KZ">哈萨克斯坦 (KZ)</option>
			<option value="AZ">阿塞拜疆 (AZ)</option>
			<option value="SA">沙特阿拉伯 (SA)</option>
			<option value="QA">卡塔尔 (QA)</option>
			<option value="PY">巴拉圭 (PY)</option>
			<option value="UA">乌克兰 (UA)</option>
			<option value="TR">土耳其 (TR)</option>
			<option value="ID">印度尼西亚 (ID)</option>
			<option value="KR">韩国 (KR)</option>
			<option value="IN">印度 (IN)</option>
			<option value="KG">吉尔吉斯斯坦 (KG)</option>
			<option value="MY">马来西亚 (MY)</option>
        </select>
    </div>
    <div class="container">
        <div class="text-section">
            <h2 id="dataTitle">调料 (0)</h2>
            <textarea id="dataInput" placeholder="请输入调料...&#x0a;&#x0a;测试数据示例：&#x0a;4111111111111111|12/25|123|John Doe|123 Main St|New York|NY|10001"></textarea>
        </div>
        <div class="text-section">
            <h2 id="emailTitle">油箱 (0)</h2>
            <textarea id="emailInput" placeholder="请输入油箱...&#x0a;&#x0a;普通格式示例：&#x0a;<EMAIL>----password123&#x0a;&#x0a;Gmail特殊格式示例：&#x0a;<EMAIL>----https://api.online-disposablemail.com/api/latest/code?orderId=1950110802194665473&#x0a;&#x0a;Gmail带额外字段示例：&#x0a;<EMAIL>----https://api.online-disposablemail.com/api/latest/code?orderId=1950110802194665473----123h123lk"></textarea>
        </div>
    </div>
    <button id="processBtn">处理数据</button>

    <script>
        function generateRandomPassword() {
            const upperChars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
            const lowerChars = 'abcdefghijklmnopqrstuvwxyz';
            const numbers = '0123456789';
            
            // 确保至少包含一个大写字母、一个小写字母和一个数字
            let password = '';
            password += upperChars.charAt(Math.floor(Math.random() * upperChars.length)); // 一个大写字母
            password += lowerChars.charAt(Math.floor(Math.random() * lowerChars.length)); // 一个小写字母
            password += numbers.charAt(Math.floor(Math.random() * numbers.length)); // 一个数字
            
            // 生成剩余的5个字符
            const allChars = upperChars + lowerChars + numbers;
            for (let i = 0; i < 5; i++) {
                password += allChars.charAt(Math.floor(Math.random() * allChars.length));
            }
            
            // 打乱密码字符顺序
            return password.split('').sort(() => Math.random() - 0.5).join('');
        }

        function generateCompanyName(countryCode = 'US') {
            const companyNames = {
                US: ['Quantum', 'Zenith', 'Solar', 'Iron', 'Sapphire', 'Blue', 'Silver', 'Phoenix', 'Maple', 'Apex',
                     'Stellar', 'Crimson', 'Golden', 'Horizon', 'Crystal', 'Nova', 'Alpha', 'Delta', 'Omega'],
                BR: ['Petrobras', 'Vale', 'Itau', 'Bradesco', 'Ambev', 'JBS', 'Embraer', 'Gerdau', 'Ultrapar', 'Suzano',
                     'Natura', 'Magazine Luiza', 'Localiza', 'WEG', 'Klabin'],
                AR: ['YPF', 'Tenaris', 'Grupo Clarin', 'Arcor', 'Techint', 'Aluar', 'Molinos Rio', 'Telecom Argentina',
                     'Banco Macro', 'Pampa Energia', 'Cresud', 'IRSA', 'Central Puerto', 'Loma Negra', 'Transportadora Gas'],
                VN: ['Vingroup', 'Vietcombank', 'FPT Corporation', 'Viettel', 'BIDV', 'Techcombank', 'VinFast', 'Masan Group',
                     'Hoa Phat Group', 'Vinhomes', 'VietinBank', 'Military Bank', 'Sacombank', 'ACB Bank', 'TPBank'],
                CL: ['Codelco', 'Falabella', 'Cencosud', 'Banco de Chile', 'Entel', 'Copec', 'LAN Airlines', 'Antofagasta',
                     'SQM', 'CAP', 'Banco Santander Chile', 'BCI', 'Colbun', 'Enel Chile', 'Aguas Andinas'],
                IL: ['Teva Pharmaceutical', 'Check Point', 'Nice Systems', 'Elbit Systems', 'Israel Aerospace', 'Bank Hapoalim',
                     'Bank Leumi', 'Bezeq', 'Rafael Advanced', 'Mizrahi Tefahot', 'Amdocs', 'Cellcom', 'Partner Communications',
                     'Israel Chemicals', 'Tower Semiconductor']
            };

            const middleWords = ['Rise', 'Core', 'Flare', 'Clad', 'Peak', 'Wave', 'Bridge', 'Trail', 'Tech', 'Grove',
                               'Synergy', 'Edge', 'Stream', 'Point', 'Nexus', 'Pulse', 'Vista'];
            const suffixes = ['Technologies', 'Industries', 'Solutions', 'Enterprises', 'Systems', 'Corporation', 'Innovations',
                            'Manufacturing', 'Labs', 'Ventures'];

            // 如果有该国家的特定公司名称，随机选择一个
            if (companyNames[countryCode]) {
                const specificNames = companyNames[countryCode];
                return specificNames[Math.floor(Math.random() * specificNames.length)];
            }

            // 否则使用通用的生成方式
            const prefixes = companyNames['US'];
            const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
            const middle = middleWords[Math.floor(Math.random() * middleWords.length)];
            const suffix = suffixes[Math.floor(Math.random() * suffixes.length)];

            return `${prefix}${middle} ${suffix}`;
        }

        // 地址数据库和智能填充函数
        function getAddressData() {
            return {
                US: {
                    states: {
                        'CA': { name: 'California', cities: ['Los Angeles', 'San Francisco', 'San Diego'], zips: ['90210', '94102', '92101'] },
                        'NY': { name: 'New York', cities: ['New York', 'Buffalo', 'Albany'], zips: ['10001', '14201', '12201'] },
                        'TX': { name: 'Texas', cities: ['Houston', 'Dallas', 'Austin', 'Brownsville', 'Hidalgo'], zips: ['77001', '75201', '78701', '78526', '78557'] },
                        'FL': { name: 'Florida', cities: ['Miami', 'Orlando', 'Tampa'], zips: ['33101', '32801', '33601'] },
                        'DE': { name: 'Delaware', cities: ['Wilmington', 'Dover', 'New Castle'], zips: ['19801', '19901', '19720'] }
                    }
                },
                CA: {
                    states: {
                        'ON': { name: 'Ontario', cities: ['Toronto', 'Ottawa', 'Hamilton'], zips: ['M5V', 'K1A', 'L8P'] },
                        'BC': { name: 'British Columbia', cities: ['Vancouver', 'Victoria', 'Surrey'], zips: ['V6B', 'V8W', 'V3T'] },
                        'QC': { name: 'Quebec', cities: ['Montreal', 'Quebec City', 'Laval'], zips: ['H3A', 'G1A', 'H7A'] }
                    }
                },
                MX: {
                    states: {
                        'DF': { name: 'Distrito Federal', cities: ['Mexico City', 'Ciudad de Mexico', 'Distrito Federal'], zips: ['01000', '09850'] },
                        'MX': { name: 'Mexico', cities: ['Toluca', 'Ecatepec', 'Mexico City'], zips: ['50000', '55000', '64660'] },
                        'JAL': { name: 'Jalisco', cities: ['Guadalajara', 'Zapopan'], zips: ['44100', '45000'] }
                    }
                },
                UK: {
                    states: {
                        'ENG': { name: 'England', cities: ['London', 'Manchester', 'Birmingham'], zips: ['SW1A', 'M1', 'B1'] },
                        'SCT': { name: 'Scotland', cities: ['Edinburgh', 'Glasgow', 'Aberdeen'], zips: ['EH1', 'G1', 'AB10'] },
                        'WLS': { name: 'Wales', cities: ['Cardiff', 'Swansea', 'Newport'], zips: ['CF10', 'SA1', 'NP19'] }
                    }
                },
                AU: {
                    states: {
                        'NSW': { name: 'New South Wales', cities: ['Sydney', 'Newcastle', 'Wollongong'], zips: ['2000', '2300', '2500'] },
                        'VIC': { name: 'Victoria', cities: ['Melbourne', 'Geelong', 'Ballarat'], zips: ['3000', '3220', '3350'] },
                        'QLD': { name: 'Queensland', cities: ['Brisbane', 'Gold Coast', 'Cairns'], zips: ['4000', '4217', '4870'] }
                    }
                },
                JP: {
                    states: {
                        'TK': { name: 'Tokyo', cities: ['Tokyo', 'Shibuya', 'Shinjuku'], zips: ['100-0001', '150-0002', '160-0022'] },
                        'OS': { name: 'Osaka', cities: ['Osaka', 'Sakai', 'Higashiosaka'], zips: ['530-0001', '590-0078', '577-0011'] },
                        'KY': { name: 'Kyoto', cities: ['Kyoto', 'Uji', 'Kameoka'], zips: ['600-8216', '611-0021', '621-0005'] }
                    }
                },
                DE: {
                    states: {
                        'BY': { name: 'Bayern', cities: ['Munich', 'Nuremberg', 'Augsburg'], zips: ['80331', '90402', '86150'] },
                        'NW': { name: 'Nordrhein-Westfalen', cities: ['Cologne', 'Dusseldorf', 'Dortmund'], zips: ['50667', '40213', '44135'] },
                        'BE': { name: 'Berlin', cities: ['Berlin', 'Charlottenburg', 'Kreuzberg'], zips: ['10115', '10585', '10997'] }
                    }
                },
                FR: {
                    states: {
                        'IDF': { name: 'Ile-de-France', cities: ['Paris', 'Versailles', 'Boulogne'], zips: ['75001', '78000', '92100'] },
                        'PACA': { name: 'Provence-Alpes-Cote', cities: ['Marseille', 'Nice', 'Toulon'], zips: ['13001', '06000', '83000'] },
                        'ARA': { name: 'Auvergne-Rhone-Alpes', cities: ['Lyon', 'Grenoble', 'Saint-Etienne'], zips: ['69001', '38000', '42000'] }
                    }
                },
                IT: {
                    states: {
                        'LZ': { name: 'Lazio', cities: ['Rome', 'Latina', 'Viterbo'], zips: ['00118', '04100', '01100'] },
                        'LM': { name: 'Lombardia', cities: ['Milan', 'Bergamo', 'Brescia'], zips: ['20121', '24122', '25121'] },
                        'CM': { name: 'Campania', cities: ['Naples', 'Salerno', 'Caserta'], zips: ['80121', '84121', '81100'] }
                    }
                },
                KZ: {
                    states: {
                        'AL': { name: 'Almaty', cities: ['Almaty', 'Shymkent', 'Taraz'], zips: ['050000', '160000', '080000'] },
                        'AS': { name: 'Astana', cities: ['Nur-Sultan', 'Karaganda', 'Pavlodar'], zips: ['010000', '100000', '140000'] },
                        'AT': { name: 'Atyrau', cities: ['Atyrau', 'Aktau', 'Aktobe'], zips: ['060000', '130000', '030000'] }
                    }
                },
                AZ: {
                    states: {
                        'BA': { name: 'Baku', cities: ['Baku', 'Sumgayit', 'Ganja'], zips: ['AZ1000', 'AZ1001', 'AZ1002', 'AZ1003', 'AZ1004'] },
                        'GA': { name: 'Ganja', cities: ['Ganja', 'Mingachevir', 'Goygol'], zips: ['AZ2000', 'AZ2001', 'AZ2002', 'AZ2003', 'AZ2004'] },
                        'SH': { name: 'Sheki', cities: ['Sheki', 'Qakh', 'Zagatala'], zips: ['AZ5500', 'AZ5501', 'AZ5502', 'AZ5503', 'AZ5504'] }
                    }
                },
                SA: {
                    states: {
                        'RI': { name: 'Riyadh', cities: ['Riyadh', 'Al Kharj', 'Dawadmi'], zips: ['11564', '11942', '11911'] },
                        'MA': { name: 'Makkah', cities: ['Jeddah', 'Mecca', 'Taif'], zips: ['21589', '21955', '26513'] },
                        'EP': { name: 'Eastern Province', cities: ['Dammam', 'Dhahran', 'Khobar'], zips: ['31952', '31261', '31952'] }
                    }
                },
                QA: {
                    states: {
                        'DO': { name: 'Doha', cities: ['Doha', 'Al Rayyan', 'Al Wakrah'], zips: ['00974', '00973', '00972'] },
                        'NO': { name: 'North', cities: ['Al Khor', 'Madinat ash Shamal', 'Al Ruwais'], zips: ['00971', '00970', '00969'] },
                        'SO': { name: 'South', cities: ['Mesaieed', 'Al Wakrah', 'Umm Salal'], zips: ['00968', '00967', '00966'] }
                    }
                },
                PY: {
                    states: {
                        'AS': { name: 'Asuncion', cities: ['Asuncion', 'San Lorenzo', 'Lambare'], zips: ['1001', '2160', '2170'] },
                        'CE': { name: 'Central', cities: ['Aregua', 'Capiata', 'Fernando de la Mora'], zips: ['2000', '2001', '2002'] },
                        'AL': { name: 'Alto Parana', cities: ['Ciudad del Este', 'Hernandarias', 'Presidente Franco'], zips: ['7000', '7001', '7002'] }
                    }
                },
                UA: {
                    states: {
                        'KY': { name: 'Kyiv', cities: ['Kyiv', 'Brovary', 'Bila Tserkva'], zips: ['01001', '07400', '09100'] },
                        'LV': { name: 'Lviv', cities: ['Lviv', 'Drohobych', 'Chervonograd'], zips: ['79000', '82100', '80100'] },
                        'OD': { name: 'Odesa', cities: ['Odesa', 'Chornomorsk', 'Yuzhnoukrainsk'], zips: ['65000', '68000', '55000'] }
                    }
                },
                TR: {
                    states: {
                        'IS': { name: 'Istanbul', cities: ['Istanbul', 'Kadikoy', 'Besiktas'], zips: ['34000', '34710', '34357'] },
                        'AN': { name: 'Ankara', cities: ['Ankara', 'Sincan', 'Etimesgut'], zips: ['06000', '06930', '06790'] },
                        'IZ': { name: 'Izmir', cities: ['Izmir', 'Bornova', 'Konak'], zips: ['35000', '35040', '35250'] }
                    }
                },
                ID: {
                    states: {
                        'JK': { name: 'Jakarta', cities: ['Jakarta', 'Bekasi', 'Tangerang'], zips: ['10110', '17112', '15111'] },
                        'JB': { name: 'Jawa Barat', cities: ['Bandung', 'Bogor', 'Depok'], zips: ['40111', '16111', '16412'] },
                        'JT': { name: 'Jawa Tengah', cities: ['Semarang', 'Solo', 'Yogyakarta'], zips: ['50241', '57126', '55511'] }
                    }
                },
                KR: {
                    states: {
                        'SE': { name: 'Seoul', cities: ['Seoul', 'Gangnam', 'Hongdae'], zips: ['04524', '06292', '04039'] },
                        'BS': { name: 'Busan', cities: ['Busan', 'Haeundae', 'Seomyeon'], zips: ['48058', '48099', '47285'] },
                        'DG': { name: 'Daegu', cities: ['Daegu', 'Suseong', 'Jung-gu'], zips: ['41566', '42259', '41911'] }
                    }
                },
                IN: {
                    states: {
                        'MH': { name: 'Maharashtra', cities: ['Mumbai', 'Pune', 'Nagpur'], zips: ['400001', '411001', '440001'] },
                        'DL': { name: 'Delhi', cities: ['New Delhi', 'Delhi', 'Gurgaon'], zips: ['110001', '110006', '122001'] },
                        'KA': { name: 'Karnataka', cities: ['Bangalore', 'Mysore', 'Hubli'], zips: ['560001', '570001', '580001'] }
                    }
                },
                KG: {
                    states: {
                        'BI': { name: 'Bishkek', cities: ['Bishkek', 'Kant', 'Tokmok'], zips: ['720000', '720100', '720200'] },
                        'OS': { name: 'Osh', cities: ['Osh', 'Jalal-Abad', 'Kara-Suu'], zips: ['714000', '715600', '714500'] },
                        'CH': { name: 'Chuy', cities: ['Tokmok', 'Kemin', 'Sokuluk'], zips: ['720300', '720400', '720500'] }
                    }
                },
                MY: {
                    states: {
                        'KL': { name: 'Kuala Lumpur', cities: ['Kuala Lumpur', 'Petaling Jaya', 'Shah Alam'], zips: ['50000', '46000', '40000'] },
                        'JH': { name: 'Johor', cities: ['Johor Bahru', 'Skudai', 'Kulai'], zips: ['80000', '81300', '81000'] },
                        'SL': { name: 'Selangor', cities: ['Klang', 'Subang Jaya', 'Ampang'], zips: ['41000', '47500', '68000'] }
                    }
                },
                BR: {
                    states: {
                        'SP': { name: 'Sao Paulo', cities: ['Sao Paulo', 'Campinas', 'Santos'], zips: ['01000-000', '13000-000', '11000-000'] },
                        'RJ': { name: 'Rio de Janeiro', cities: ['Rio de Janeiro', 'Niteroi', 'Petropolis'], zips: ['20000-000', '24000-000', '25000-000'] },
                        'MG': { name: 'Minas Gerais', cities: ['Belo Horizonte', 'Uberlandia', 'Juiz de Fora'], zips: ['30000-000', '38000-000', '36000-000'] }
                    }
                },
                AR: {
                    states: {
                        'BA': { name: 'Buenos Aires', cities: ['Buenos Aires', 'La Plata', 'Mar del Plata'], zips: ['C1000', 'B1900', 'B7600'] },
                        'CB': { name: 'Cordoba', cities: ['Cordoba', 'Villa Carlos Paz', 'Rio Cuarto'], zips: ['X5000', 'X5152', 'X5800'] },
                        'SF': { name: 'Santa Fe', cities: ['Santa Fe', 'Rosario', 'Rafaela'], zips: ['S3000', 'S2000', 'S2300'] }
                    }
                },
                VN: {
                    states: {
                        'HN': { name: 'Ha Noi', cities: ['Hanoi', 'Hai Phong', 'Nam Dinh'], zips: ['100000', '180000', '420000'] },
                        'HCM': { name: 'Ho Chi Minh', cities: ['Ho Chi Minh City', 'Thu Duc', 'Bien Hoa'], zips: ['700000', '71000', '810000'] },
                        'DN': { name: 'Da Nang', cities: ['Da Nang', 'Hoi An', 'Hue'], zips: ['550000', '560000', '530000'] }
                    }
                },
                CL: {
                    states: {
                        'RM': { name: 'Region Metropolitana', cities: ['Santiago', 'Puente Alto', 'Maipu'], zips: ['8320000', '8150000', '9250000'] },
                        'VS': { name: 'Valparaiso', cities: ['Valparaiso', 'Vina del Mar', 'Quilpue'], zips: ['2340000', '2520000', '2430000'] },
                        'BB': { name: 'Bio Bio', cities: ['Concepcion', 'Talcahuano', 'Chiguayante'], zips: ['4030000', '4260000', '4100000'] }
                    }
                },
                IL: {
                    states: {
                        'TA': { name: 'Tel Aviv', cities: ['Tel Aviv', 'Ramat Gan', 'Petah Tikva'], zips: ['61000', '52000', '49000'] },
                        'JM': { name: 'Jerusalem', cities: ['Jerusalem', 'Bethlehem', 'Ramallah'], zips: ['91000', '90000', '99000'] },
                        'HF': { name: 'Haifa', cities: ['Haifa', 'Nazareth', 'Acre'], zips: ['31000', '16000', '24000'] }
                    }
                }
            };
        }

        function generateNameByCountry(countryCode) {
            const nameData = {
                US: {
                    firstNames: ['John', 'Michael', 'Sarah', 'David', 'Jennifer', 'Robert', 'Lisa', 'William', 'Mary', 'James', 'Patricia', 'Christopher', 'Linda', 'Daniel', 'Elizabeth'],
                    lastNames: ['Smith', 'Johnson', 'Williams', 'Brown', 'Davis', 'Miller', 'Wilson', 'Moore', 'Taylor', 'Anderson', 'Thomas', 'Jackson', 'White', 'Harris', 'Martin']
                },
                CA: {
                    firstNames: ['James', 'Mary', 'Robert', 'Patricia', 'John', 'Jennifer', 'Michael', 'Linda', 'David', 'Elizabeth', 'William', 'Barbara', 'Richard', 'Susan', 'Joseph'],
                    lastNames: ['Smith', 'Brown', 'Tremblay', 'Martin', 'Roy', 'Wilson', 'MacDonald', 'Johnson', 'Thompson', 'Anderson', 'Clark', 'Scott', 'Campbell', 'Stewart', 'Roberts']
                },
                MX: {
                    firstNames: ['Carlos', 'Maria', 'Jose', 'Ana', 'Luis', 'Carmen', 'Francisco', 'Rosa', 'Antonio', 'Teresa', 'Manuel', 'Lucia', 'Miguel', 'Isabel', 'Juan'],
                    lastNames: ['Rodriguez', 'Garcia', 'Martinez', 'Lopez', 'Hernandez', 'Gonzalez', 'Perez', 'Sanchez', 'Ramirez', 'Cruz', 'Flores', 'Gomez', 'Morales', 'Vazquez', 'Jimenez']
                },
                UK: {
                    firstNames: ['Oliver', 'Amelia', 'George', 'Isla', 'Noah', 'Ava', 'Arthur', 'Mia', 'Muhammad', 'Grace', 'Leo', 'Sophia', 'Harry', 'Freya', 'Oscar'],
                    lastNames: ['Smith', 'Jones', 'Taylor', 'Williams', 'Brown', 'Davies', 'Evans', 'Wilson', 'Thomas', 'Roberts', 'Johnson', 'Lewis', 'Walker', 'Robinson', 'Wood']
                },
                AU: {
                    firstNames: ['Oliver', 'Charlotte', 'William', 'Isla', 'Jack', 'Amelia', 'Noah', 'Mia', 'James', 'Grace', 'Lucas', 'Ava', 'Henry', 'Chloe', 'Mason'],
                    lastNames: ['Smith', 'Jones', 'Williams', 'Brown', 'Wilson', 'Taylor', 'Johnson', 'White', 'Martin', 'Anderson', 'Thompson', 'Nguyen', 'Thomas', 'Walker', 'Harris']
                },
                JP: {
                    firstNames: ['Hiroshi', 'Yuki', 'Takeshi', 'Akiko', 'Kenji', 'Yoko', 'Satoshi', 'Michiko', 'Masahiro', 'Tomoko', 'Kazuo', 'Naoko', 'Yoshiaki', 'Kumiko', 'Taro'],
                    lastNames: ['Sato', 'Suzuki', 'Takahashi', 'Tanaka', 'Watanabe', 'Ito', 'Yamamoto', 'Nakamura', 'Kobayashi', 'Kato', 'Yoshida', 'Yamada', 'Sasaki', 'Yamaguchi', 'Saito']
                },
                DE: {
                    firstNames: ['Ben', 'Emma', 'Paul', 'Mia', 'Leon', 'Sofia', 'Finn', 'Lina', 'Noah', 'Emilia', 'Louis', 'Hannah', 'Henry', 'Lea', 'Felix'],
                    lastNames: ['Muller', 'Schmidt', 'Schneider', 'Fischer', 'Weber', 'Meyer', 'Wagner', 'Becker', 'Schulz', 'Hoffmann', 'Schafer', 'Koch', 'Bauer', 'Richter', 'Klein']
                },
                FR: {
                    firstNames: ['Gabriel', 'Emma', 'Leo', 'Jade', 'Raphael', 'Louise', 'Arthur', 'Alice', 'Louis', 'Chloe', 'Lucas', 'Lina', 'Adam', 'Rose', 'Hugo'],
                    lastNames: ['Martin', 'Bernard', 'Thomas', 'Petit', 'Robert', 'Richard', 'Durand', 'Dubois', 'Moreau', 'Laurent', 'Simon', 'Michel', 'Lefebvre', 'Leroy', 'Roux']
                },
                IT: {
                    firstNames: ['Francesco', 'Sofia', 'Alessandro', 'Giulia', 'Lorenzo', 'Aurora', 'Leonardo', 'Alice', 'Mattia', 'Ginevra', 'Andrea', 'Emma', 'Gabriele', 'Giorgia', 'Riccardo'],
                    lastNames: ['Rossi', 'Russo', 'Ferrari', 'Esposito', 'Bianchi', 'Romano', 'Colombo', 'Ricci', 'Marino', 'Greco', 'Bruno', 'Gallo', 'Conti', 'De Luca', 'Mancini']
                },
                KZ: {
                    firstNames: ['Aidos', 'Aigerim', 'Arman', 'Amina', 'Dias', 'Dinara', 'Erlan', 'Elmira', 'Nurlan', 'Nazira', 'Serik', 'Saule', 'Timur', 'Togzhan', 'Yerlan'],
                    lastNames: ['Nazarbayev', 'Tokayev', 'Masimov', 'Sagintayev', 'Utemuratov', 'Kulibayev', 'Abayev', 'Dossayev', 'Yessimov', 'Karimov', 'Suleimenov', 'Bekturganov', 'Zhaksylykov', 'Mynbayev', 'Shukeyev']
                },
                AZ: {
                    firstNames: ['Elvin', 'Aysel', 'Rashad', 'Leyla', 'Farid', 'Nigar', 'Samir', 'Gulnar', 'Orkhan', 'Sevil', 'Tural', 'Mehriban', 'Kamran', 'Sevinj', 'Vugar'],
                    lastNames: ['Mammadov', 'Aliyeva', 'Hasanov', 'Ismayilova', 'Guliyev', 'Huseynov', 'Ahmadov', 'Babayev', 'Karimov', 'Musayev', 'Rzayev', 'Tagiyev', 'Veliyev', 'Zeynalov', 'Qasimov']
                },
                SA: {
                    firstNames: ['Mohammed', 'Fatima', 'Ahmed', 'Aisha', 'Abdullah', 'Khadija', 'Omar', 'Maryam', 'Ali', 'Zainab', 'Hassan', 'Nour', 'Khalid', 'Sara', 'Youssef'],
                    lastNames: ['Al-Saud', 'Al-Rashid', 'Al-Otaibi', 'Al-Dosari', 'Al-Harbi', 'Al-Ghamdi', 'Al-Zahrani', 'Al-Maliki', 'Al-Shehri', 'Al-Qahtani', 'Al-Mutairi', 'Al-Subai', 'Al-Shamrani', 'Al-Dawsari', 'Al-Anzi']
                },
                QA: {
                    firstNames: ['Mohammed', 'Fatima', 'Ahmed', 'Aisha', 'Abdullah', 'Mariam', 'Omar', 'Noor', 'Ali', 'Sara', 'Hassan', 'Layla', 'Khalid', 'Amina', 'Youssef'],
                    lastNames: ['Al-Thani', 'Al-Kuwari', 'Al-Ansari', 'Al-Marri', 'Al-Sulaiti', 'Al-Mannai', 'Al-Mohannadi', 'Al-Naimi', 'Al-Attiyah', 'Al-Dosari', 'Al-Malki', 'Al-Emadi', 'Al-Kaabi', 'Al-Hajri', 'Al-Misnad']
                },
                PY: {
                    firstNames: ['Carlos', 'Maria', 'Jose', 'Ana', 'Luis', 'Carmen', 'Juan', 'Rosa', 'Miguel', 'Teresa', 'Antonio', 'Lucia', 'Francisco', 'Isabel', 'Manuel'],
                    lastNames: ['Gonzalez', 'Rodriguez', 'Lopez', 'Martinez', 'Perez', 'Garcia', 'Sanchez', 'Ramirez', 'Torres', 'Flores', 'Rivera', 'Gomez', 'Diaz', 'Hernandez', 'Jimenez']
                },
                UA: {
                    firstNames: ['Oleksandr', 'Oksana', 'Andriy', 'Iryna', 'Sergiy', 'Tetyana', 'Volodymyr', 'Nataliya', 'Yuriy', 'Olena', 'Ivan', 'Svitlana', 'Mykola', 'Lyudmyla', 'Viktor'],
                    lastNames: ['Petrenko', 'Kovalenko', 'Bondarenko', 'Tkachenko', 'Kovalchuk', 'Kravchenko', 'Shevchenko', 'Polishchuk', 'Marchenko', 'Lysenko', 'Savchenko', 'Rudenko', 'Melnyk', 'Boyko', 'Moroz']
                },
                TR: {
                    firstNames: ['Mehmet', 'Ayse', 'Mustafa', 'Fatma', 'Ahmet', 'Emine', 'Ali', 'Hatice', 'Hasan', 'Zeynep', 'Huseyin', 'Elif', 'Ibrahim', 'Merve', 'Ismail'],
                    lastNames: ['Yilmaz', 'Kaya', 'Demir', 'Sahin', 'Celik', 'Yildiz', 'Yildirim', 'Ozturk', 'Aydin', 'Ozdemir', 'Arslan', 'Dogan', 'Kilic', 'Aslan', 'Cetin']
                },
                ID: {
                    firstNames: ['Budi', 'Sari', 'Ahmad', 'Dewi', 'Andi', 'Rina', 'Dedi', 'Maya', 'Rudi', 'Indira', 'Agus', 'Lestari', 'Bambang', 'Wati', 'Hendra'],
                    lastNames: ['Santoso', 'Wijaya', 'Kurniawan', 'Sari', 'Pratama', 'Lestari', 'Putra', 'Dewi', 'Utomo', 'Handayani', 'Setiawan', 'Rahayu', 'Susanto', 'Indrawati', 'Permana']
                },
                KR: {
                    firstNames: ['Min-jun', 'So-young', 'Jae-hyun', 'Ji-woo', 'Seung-ho', 'Ye-jin', 'Dong-hyun', 'Hye-jin', 'Jun-seo', 'Soo-jin', 'Hyun-woo', 'Min-jung', 'Sang-min', 'Eun-jung', 'Jin-woo'],
                    lastNames: ['Kim', 'Lee', 'Park', 'Choi', 'Jung', 'Kang', 'Cho', 'Yoon', 'Jang', 'Lim', 'Han', 'Oh', 'Seo', 'Shin', 'Kwon']
                },
                IN: {
                    firstNames: ['Raj', 'Priya', 'Amit', 'Sunita', 'Suresh', 'Kavita', 'Ravi', 'Meera', 'Anil', 'Pooja', 'Vikash', 'Rekha', 'Manoj', 'Geeta', 'Sanjay'],
                    lastNames: ['Sharma', 'Verma', 'Singh', 'Kumar', 'Gupta', 'Agarwal', 'Jain', 'Bansal', 'Srivastava', 'Yadav', 'Mishra', 'Tiwari', 'Chauhan', 'Joshi', 'Saxena']
                },
                KG: {
                    firstNames: ['Azamat', 'Aigul', 'Bektur', 'Cholpon', 'Dastan', 'Elnura', 'Erkin', 'Gulnara', 'Maksat', 'Nazgul', 'Omurbek', 'Perizat', 'Ruslan', 'Saltanat', 'Tilek'],
                    lastNames: ['Abdyldaev', 'Bakirov', 'Djumabaev', 'Esenbaev', 'Isaev', 'Kadyrbekov', 'Mamytov', 'Nazarbaev', 'Osmonov', 'Ryskulov', 'Sydykov', 'Toktosunov', 'Usenov', 'Zulpukarov', 'Akmatov']
                },
                MY: {
                    firstNames: ['Ahmad', 'Siti', 'Muhammad', 'Nur', 'Ali', 'Fatimah', 'Hassan', 'Aishah', 'Ibrahim', 'Khadijah', 'Omar', 'Zainab', 'Yusof', 'Mariam', 'Ismail'],
                    lastNames: ['Abdullah', 'Ahmad', 'Mohamed', 'Ibrahim', 'Ismail', 'Hassan', 'Ali', 'Omar', 'Yusof', 'Mahmud', 'Rahman', 'Hamid', 'Mansor', 'Othman', 'Bakar']
                },
                BR: {
                    firstNames: ['Carlos', 'Maria', 'Jose', 'Ana', 'Luis', 'Carmen', 'Antonio', 'Rosa', 'Francisco', 'Teresa', 'Manuel', 'Lucia', 'Miguel', 'Isabel', 'Juan'],
                    lastNames: ['Silva', 'Santos', 'Oliveira', 'Souza', 'Rodrigues', 'Ferreira', 'Alves', 'Pereira', 'Lima', 'Gomes', 'Ribeiro', 'Carvalho', 'Almeida', 'Lopes', 'Soares']
                },
                AR: {
                    firstNames: ['Carlos', 'Maria', 'Jose', 'Ana', 'Luis', 'Carmen', 'Juan', 'Rosa', 'Miguel', 'Teresa', 'Antonio', 'Lucia', 'Francisco', 'Isabel', 'Manuel'],
                    lastNames: ['Rodriguez', 'Garcia', 'Martinez', 'Lopez', 'Hernandez', 'Gonzalez', 'Perez', 'Sanchez', 'Ramirez', 'Cruz', 'Flores', 'Gomez', 'Morales', 'Vazquez', 'Jimenez']
                },
                VN: {
                    firstNames: ['Nguyen', 'Tran', 'Le', 'Pham', 'Hoang', 'Huynh', 'Vo', 'Vu', 'Dang', 'Bui', 'Do', 'Ho', 'Ngo', 'Duong', 'Ly'],
                    lastNames: ['Van', 'Thi', 'Duc', 'Minh', 'Hoang', 'Quang', 'Anh', 'Thanh', 'Huy', 'Linh', 'Mai', 'Lan', 'Hong', 'Phong', 'Dung']
                },
                CL: {
                    firstNames: ['Carlos', 'Maria', 'Jose', 'Ana', 'Luis', 'Carmen', 'Juan', 'Rosa', 'Miguel', 'Teresa', 'Antonio', 'Lucia', 'Francisco', 'Isabel', 'Manuel'],
                    lastNames: ['Rodriguez', 'Garcia', 'Martinez', 'Lopez', 'Hernandez', 'Gonzalez', 'Perez', 'Sanchez', 'Ramirez', 'Cruz', 'Flores', 'Gomez', 'Morales', 'Vazquez', 'Silva']
                },
                IL: {
                    firstNames: ['David', 'Sarah', 'Michael', 'Rachel', 'Daniel', 'Rebecca', 'Jonathan', 'Miriam', 'Benjamin', 'Leah', 'Aaron', 'Esther', 'Jacob', 'Ruth', 'Samuel'],
                    lastNames: ['Cohen', 'Levy', 'Miller', 'Goldberg', 'Rosen', 'Katz', 'Friedman', 'Klein', 'Schwartz', 'Green', 'Weiss', 'Brown', 'Davis', 'Stern', 'Wolf']
                }
            };

            const countryNameData = nameData[countryCode] || nameData['US'];
            const firstName = countryNameData.firstNames[Math.floor(Math.random() * countryNameData.firstNames.length)];
            const lastName = countryNameData.lastNames[Math.floor(Math.random() * countryNameData.lastNames.length)];

            return `${firstName} ${lastName}`;
        }

        function getZipCodePattern(countryCode) {
            // 定义不同国家的邮编格式
            const patterns = {
                'US': { regex: /^\d{5}(-\d{4})?$/, length: [5, 9], format: 'numeric' },
                'CA': { regex: /^[A-Z]\d[A-Z]\s?\d[A-Z]\d$/, length: [6, 7], format: 'alphanumeric' },
                'MX': { regex: /^\d{5}$/, length: [5], format: 'numeric' },
                'UK': { regex: /^[A-Z]{1,2}\d[A-Z\d]?\s?\d[A-Z]{2}$/, length: [6, 8], format: 'alphanumeric' },
                'AU': { regex: /^\d{4}$/, length: [4], format: 'numeric' },
                'JP': { regex: /^\d{3}-?\d{4}$/, length: [7, 8], format: 'numeric' },
                'DE': { regex: /^\d{5}$/, length: [5], format: 'numeric' },
                'FR': { regex: /^\d{5}$/, length: [5], format: 'numeric' },
                'IT': { regex: /^\d{5}$/, length: [5], format: 'numeric' },
                'KZ': { regex: /^\d{6}$/, length: [6], format: 'numeric' },
                'AZ': { regex: /^AZ\d{4}$/, length: [6], format: 'alphanumeric' },
                'SA': { regex: /^\d{5}$/, length: [5], format: 'numeric' },
                'QA': { regex: /^\d{5}$/, length: [5], format: 'numeric' },
                'PY': { regex: /^\d{4}$/, length: [4], format: 'numeric' },
                'UA': { regex: /^\d{5}$/, length: [5], format: 'numeric' },
                'TR': { regex: /^\d{5}$/, length: [5], format: 'numeric' },
                'ID': { regex: /^\d{5}$/, length: [5], format: 'numeric' },
                'KR': { regex: /^\d{5}$/, length: [5], format: 'numeric' },
                'IN': { regex: /^\d{6}$/, length: [6], format: 'numeric' },
                'KG': { regex: /^\d{6}$/, length: [6], format: 'numeric' },
                'MY': { regex: /^\d{5}$/, length: [5], format: 'numeric' },
                'BR': { regex: /^\d{5}-?\d{3}$/, length: [8, 9], format: 'numeric' },
                'AR': { regex: /^[A-Z]\d{4}$/, length: [5], format: 'alphanumeric' },
                'VN': { regex: /^\d{6}$/, length: [6], format: 'numeric' },
                'CL': { regex: /^\d{7}$/, length: [7], format: 'numeric' },
                'IL': { regex: /^\d{5}$/, length: [5], format: 'numeric' }
            };

            return patterns[countryCode] || patterns['US']; // 默认使用美国格式
        }

        function isValidZipCode(zipCode, countryCode = 'US') {
            // 检查邮编是否有效
            if (!zipCode || zipCode.trim() === '') return false;

            zipCode = zipCode.trim();

            // 检查特定的无效值
            if (zipCode === '1111' || zipCode === '0000' || zipCode === '00000') return false;

            // 检查是否包含明显不是邮编的字符串
            if (zipCode.toLowerCase().includes('st.lorenzen') ||
                zipCode.toLowerCase().includes('lorenzen')) return false;

            // 检查是否全是相同数字
            if (/^1{3,}$/.test(zipCode) || /^0{3,}$/.test(zipCode)) return false;

            // 获取该国家的邮编格式
            const pattern = getZipCodePattern(countryCode);

            // 检查长度是否符合该国家的标准
            if (!pattern.length.includes(zipCode.replace(/[\s\-]/g, '').length)) return false;

            // 检查格式是否符合该国家的标准
            if (!pattern.regex.test(zipCode.toUpperCase())) return false;

            // 检查是否包含明显不合理的字符组合（如连续的字母超过3个）
            if (/[a-zA-Z]{4,}/.test(zipCode)) return false;

            return true;
        }

        function generateValidZipCode(countryCode = 'US') {
            // 根据国家生成有效的邮编
            const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';

            switch (countryCode) {
                case 'US':
                case 'MX':
                case 'DE':
                case 'FR':
                case 'IT':
                case 'SA':
                case 'QA':
                case 'TR':
                case 'ID':
                case 'KR':
                case 'MY':
                    // 5位数字邮编
                    return (Math.floor(Math.random() * 90000) + 10000).toString();

                case 'CA':
                    // 加拿大格式：A1A 1A1
                    return letters[Math.floor(Math.random() * letters.length)] +
                           (Math.floor(Math.random() * 10)).toString() +
                           letters[Math.floor(Math.random() * letters.length)] +
                           ' ' +
                           (Math.floor(Math.random() * 10)).toString() +
                           letters[Math.floor(Math.random() * letters.length)] +
                           (Math.floor(Math.random() * 10)).toString();

                case 'UK':
                    // 英国格式：A1 1AA
                    return letters[Math.floor(Math.random() * letters.length)] +
                           (Math.floor(Math.random() * 10)).toString() +
                           ' ' +
                           (Math.floor(Math.random() * 10)).toString() +
                           letters[Math.floor(Math.random() * letters.length)] +
                           letters[Math.floor(Math.random() * letters.length)];

                case 'AU':
                case 'PY':
                    // 4位数字邮编
                    return (Math.floor(Math.random() * 9000) + 1000).toString();

                case 'AZ':
                    // 阿塞拜疆格式：AZ1000
                    return 'AZ' + (Math.floor(Math.random() * 9000) + 1000).toString();

                case 'JP':
                    // 日本格式：123-4567
                    return (Math.floor(Math.random() * 900) + 100).toString() + '-' +
                           (Math.floor(Math.random() * 9000) + 1000).toString();

                case 'KZ':
                case 'IN':
                case 'KG':
                    // 6位数字邮编
                    return (Math.floor(Math.random() * 900000) + 100000).toString();

                case 'UA':
                    // 乌克兰5位数字邮编
                    return (Math.floor(Math.random() * 90000) + 10000).toString();

                case 'BR':
                    // 巴西格式：12345-678
                    return (Math.floor(Math.random() * 90000) + 10000).toString() + '-' +
                           (Math.floor(Math.random() * 900) + 100).toString();

                case 'AR':
                    // 阿根廷格式：C1234
                    return letters[Math.floor(Math.random() * letters.length)] +
                           (Math.floor(Math.random() * 9000) + 1000).toString();

                case 'VN':
                    // 越南6位数字邮编
                    return (Math.floor(Math.random() * 900000) + 100000).toString();

                case 'CL':
                    // 智利7位数字邮编
                    return (Math.floor(Math.random() * 9000000) + 1000000).toString();

                case 'IL':
                    // 以色列5位数字邮编
                    return (Math.floor(Math.random() * 90000) + 10000).toString();

                default:
                    // 默认使用美国格式
                    return (Math.floor(Math.random() * 90000) + 10000).toString();
            }
        }

        function isAddressDataValid(address, city, state, zipCode, countryCode = 'US') {
            // 检查地址是否有效
            if (!address || address.length < 5 ||
                address.toLowerCase() === 'bakubaki' ||
                address.toLowerCase().includes('unknown') ||
                address.toLowerCase().includes('st.lorenzen') ||
                /^[a-z]{1,10}$/i.test(address.trim())) return false;

            // 检查城市是否有效
            if (!city || city.length < 2 ||
                city.toLowerCase() === 'unknown' ||
                city.toLowerCase().includes('st.lorenzen') ||
                /^[a-z]{1,5}$/i.test(city.trim())) return false;

            // 检查州是否有效
            if (!state || state === 'NULL' || state.length < 2 ||
                state.toLowerCase() === 'unknown' ||
                state.toLowerCase() === 'null' ||
                state.toLowerCase().includes('st.lorenzen')) return false;

            // 使用专门的邮编验证函数，传入国家代码
            if (!isValidZipCode(zipCode, countryCode)) return false;

            return true;
        }

        function generateRandomAddress(countryCode) {
            const streetNames = {
                US: ['Main Street', 'Oak Avenue', 'Park Road', 'First Street', 'Second Avenue', 'Elm Street', 'Washington Boulevard', 'Lincoln Drive', 'Maple Lane', 'Cedar Way'],
                CA: ['Main Street', 'King Street', 'Queen Street', 'Yonge Street', 'Bay Street', 'College Street', 'Dundas Street', 'Bloor Street', 'Richmond Street', 'Adelaide Street'],
                MX: ['Avenida Principal', 'Calle Central', 'Boulevard Norte', 'Avenida Sur', 'Calle Primera', 'Avenida Juarez', 'Calle Hidalgo', 'Boulevard Reforma', 'Avenida Revolucion', 'Calle Morelos'],
                UK: ['High Street', 'Church Lane', 'Victoria Road', 'Mill Lane', 'School Lane', 'The Green', 'Main Street', 'New Road', 'Manor Road', 'Kings Road'],
                AU: ['Collins Street', 'Bourke Street', 'Flinders Street', 'Elizabeth Street', 'Swanston Street', 'Queen Street', 'King Street', 'Spencer Street', 'Russell Street', 'William Street'],
                JP: ['Shibuya', 'Ginza', 'Harajuku', 'Akihabara', 'Shinjuku', 'Roppongi', 'Asakusa', 'Ikebukuro', 'Ueno', 'Odaiba'],
                DE: ['Hauptstrasse', 'Bahnhofstrasse', 'Kirchgasse', 'Schulstrasse', 'Gartenstrasse', 'Bergstrasse', 'Dorfstrasse', 'Lindenstrasse', 'Rosenstrasse', 'Friedrichstrasse'],
                FR: ['Rue de la Paix', 'Avenue des Champs', 'Rue Saint-Honore', 'Boulevard Saint-Germain', 'Rue de Rivoli', 'Avenue Montaigne', 'Rue du Faubourg', 'Boulevard Haussmann', 'Rue de la Republique', 'Avenue Victor Hugo'],
                IT: ['Via Roma', 'Corso Italia', 'Via Nazionale', 'Piazza Garibaldi', 'Via Dante', 'Corso Vittorio', 'Via Mazzini', 'Piazza Venezia', 'Via del Corso', 'Largo Argentina'],
                KZ: ['Abay Avenue', 'Dostyk Avenue', 'Nazarbayev Avenue', 'Republic Avenue', 'Satpayev Street', 'Furmanov Street', 'Bogenbay Batyr Street', 'Tole Bi Street', 'Kabanbay Batyr Avenue', 'Al-Farabi Avenue'],
                AZ: ['Nizami Street', 'Azadliq Avenue', 'Baku Boulevard', 'Heydar Aliyev Avenue', 'Fountain Square', 'Sahil Street', 'Neftchilar Avenue', 'Uzeyir Hajibeyov Street', 'Bulbul Avenue', 'Rashid Behbudov Street'],
                SA: ['King Fahd Road', 'Prince Sultan Street', 'Olaya Street', 'Tahlia Street', 'King Abdul Aziz Road', 'Makkah Road', 'Madinah Road', 'Riyadh Street', 'Al Malaz Street', 'Al Wurud Street'],
                QA: ['Corniche Road', 'Al Rayyan Road', 'Salwa Road', 'C Ring Road', 'D Ring Road', 'Al Waab Street', 'Grand Hamad Street', 'Al Sadd Street', 'Najma Street', 'Al Markhiya Street'],
                PY: ['Avenida Mariscal Lopez', 'Calle Palma', 'Avenida Espana', 'Calle Chile', 'Avenida Brasil', 'Calle Peru', 'Avenida Venezuela', 'Calle Colon', 'Avenida Artigas', 'Calle Independencia'],
                UA: ['Khreshchatyk Street', 'Independence Square', 'Lvivska Street', 'Shevchenko Boulevard', 'Pushkinska Street', 'Prorizna Street', 'Velyka Vasylkivska Street', 'Antonovycha Street', 'Tarasa Shevchenka Boulevard', 'Volodymyrska Street'],
                TR: ['Istiklal Caddesi', 'Bagdat Caddesi', 'Ataturk Bulvari', 'Cumhuriyet Caddesi', 'Barbaros Bulvari', 'Nisantasi', 'Taksim Square', 'Galata Bridge', 'Bosphorus Bridge', 'Golden Horn'],
                ID: ['Jalan Sudirman', 'Jalan Thamrin', 'Jalan Gatot Subroto', 'Jalan Kuningan', 'Jalan Rasuna Said', 'Jalan Casablanca', 'Jalan Kemang', 'Jalan Senopati', 'Jalan Pantai Indah', 'Jalan Asia Afrika'],
                KR: ['Gangnam-daero', 'Teheran-ro', 'Sejong-daero', 'Jongno', 'Myeongdong', 'Hongdae', 'Itaewon', 'Apgujeong', 'Sinchon', 'Dongdaemun'],
                IN: ['MG Road', 'Brigade Road', 'Commercial Street', 'Residency Road', 'Richmond Road', 'Cunningham Road', 'Lavelle Road', 'Museum Road', 'Palace Road', 'Kasturba Road'],
                KG: ['Chuy Avenue', 'Manas Avenue', 'Erkindik Boulevard', 'Jibek Jolu', 'Togolok Moldo Street', 'Bokonbaeva Street', 'Kievskaya Street', 'Moskovskaya Street', 'Sovietskaya Street', 'Toktogula Street'],
                MY: ['Jalan Bukit Bintang', 'Jalan Ampang', 'Jalan Raja Chulan', 'Jalan Tun Razak', 'Jalan Kuching', 'Jalan Ipoh', 'Jalan Cheras', 'Jalan Klang Lama', 'Jalan Bangsar', 'Jalan Damansara'],
                BR: ['Rua Augusta', 'Avenida Paulista', 'Rua Oscar Freire', 'Avenida Faria Lima', 'Rua da Consolacao', 'Avenida Reboucas', 'Rua Haddock Lobo', 'Avenida Brasil', 'Rua Teodoro Sampaio', 'Avenida Ipiranga'],
                AR: ['Avenida Corrientes', 'Calle Florida', 'Avenida Santa Fe', 'Avenida Rivadavia', 'Calle Defensa', 'Avenida de Mayo', 'Calle San Martin', 'Avenida Callao', 'Calle Lavalle', 'Avenida Pueyrredon'],
                VN: ['Nguyen Hue Street', 'Le Loi Street', 'Dong Khoi Street', 'Ham Nghi Street', 'Nguyen Thai Hoc Street', 'Tran Hung Dao Street', 'Le Duan Street', 'Vo Van Tan Street', 'Nguyen Du Street', 'Pasteur Street'],
                CL: ['Avenida Providencia', 'Calle Huerfanos', 'Avenida Libertador', 'Calle Estado', 'Avenida Las Condes', 'Calle Moneda', 'Avenida Vicuna Mackenna', 'Calle Bandera', 'Avenida Apoquindo', 'Calle Compania'],
                IL: ['Dizengoff Street', 'Ben Yehuda Street', 'King George Street', 'Rothschild Boulevard', 'Allenby Street', 'Ibn Gabirol Street', 'Hayarkon Street', 'Sheinkin Street', 'Bialik Street', 'Herzl Street']
            };

            const streets = streetNames[countryCode] || streetNames['US'];
            const streetNumber = Math.floor(Math.random() * 9999) + 1;
            const streetName = streets[Math.floor(Math.random() * streets.length)];

            return `${streetNumber} ${streetName}`;
        }

        function smartFillAddress(address, city, state, zipCode, countryCode) {
            const addressData = getAddressData();
            const countryData = addressData[countryCode];

            if (!countryData) {
                // 如果没有该国家的数据，返回基本填充
                return {
                    address: address || generateRandomAddress(countryCode),
                    city: city || 'Default City',
                    state: state || 'ST',
                    zipCode: zipCode || generateValidZipCode(countryCode)
                };
            }

            // 如果邮编有效，根据邮编推断其他信息
            if (zipCode && isValidZipCode(zipCode, countryCode)) {
                for (const [stateCode, stateInfo] of Object.entries(countryData.states)) {
                    if (stateInfo.zips.some(zip => zip.startsWith(zipCode.substring(0, 3)))) {
                        return {
                            address: address || generateRandomAddress(countryCode),
                            city: city || stateInfo.cities[Math.floor(Math.random() * stateInfo.cities.length)],
                            state: state || stateCode,
                            zipCode: zipCode
                        };
                    }
                }
            }

            // 如果州和城市有效，根据它们填充其他信息
            if (state && city) {
                const stateInfo = countryData.states[state.toUpperCase()];
                if (stateInfo && stateInfo.cities.some(c => c.toLowerCase().includes(city.toLowerCase()))) {
                    return {
                        address: address || generateRandomAddress(countryCode),
                        city: city,
                        state: state,
                        zipCode: (zipCode && isValidZipCode(zipCode, countryCode)) ? zipCode : stateInfo.zips[Math.floor(Math.random() * stateInfo.zips.length)]
                    };
                }
            }

            // 默认填充：随机选择一个州的信息
            const stateKeys = Object.keys(countryData.states);
            const randomState = stateKeys[Math.floor(Math.random() * stateKeys.length)];
            const randomStateInfo = countryData.states[randomState];

            return {
                address: address || generateRandomAddress(countryCode),
                city: city || randomStateInfo.cities[Math.floor(Math.random() * randomStateInfo.cities.length)],
                state: state || randomState,
                zipCode: (zipCode && isValidZipCode(zipCode, countryCode)) ? zipCode : randomStateInfo.zips[Math.floor(Math.random() * randomStateInfo.zips.length)]
            };
        }

        function processData() {
            const emailText = document.getElementById('emailInput').value;
            const dataText = document.getElementById('dataInput').value;
            const selectedCountry = document.getElementById('countrySelect').value;

            // 国家代码到中文名称的映射
            const countryNameMap = {
                'US': '美国',
                'CA': '加拿大',
                'MX': '墨西哥',
                'UK': '英国',
                'AU': '澳大利亚',
                'JP': '日本',
                'DE': '德国',
                'FR': '法国',
                'IT': '意大利',
                'KZ': '哈萨克斯坦',
                'AZ': '阿塞拜疆',
                'SA': '沙特阿拉伯',
                'QA': '卡塔尔',
                'PY': '巴拉圭',
                'UA': '乌克兰',
                'TR': '土耳其',
                'ID': '印度尼西亚',
                'KR': '韩国',
                'IN': '印度',
                'KG': '吉尔吉斯斯坦',
                'MY': '马来西亚',
                'BR': '巴西',
                'AR': '阿根廷',
                'VN': '越南',
                'CL': '智利',
                'IL': '以色列',
                'BR': '巴西',
                'VN': '越南',
                'CL': '智利',
                'IL': '以色列'
            };

            // 处理数据行
            const dataLines = dataText.split('\n').filter(line => line.trim());
            const emailLines = emailText.split('\n').filter(line => line.trim());

            let result = [];
            let extraEmails = [];
            let extraData = [];
            
            // 确定处理的行数
            const processLines = Math.min(dataLines.length, emailLines.length);
            
            // 检查多余的行
            if (emailLines.length > dataLines.length) {
                const extraCount = emailLines.length - dataLines.length;
                alert(`输入的油箱有${extraCount}条是多余的`);
                extraEmails = emailLines.slice(dataLines.length);
            } else if (dataLines.length > emailLines.length) {
                const extraCount = dataLines.length - emailLines.length;
                alert(`输入的调料${extraCount}条无法匹配油箱`);
                extraData = dataLines.slice(emailLines.length);
            }

            // 处理匹配的行
            for (let i = 0; i < processLines; i++) {
                let dataLine = dataLines[i];
                const emailLine = emailLines[i] || '';

                // 处理特殊的Gmail格式：<EMAIL>----https://api.online-disposablemail.com/api/latest/code?orderId=1950110802194665473----123h123lk
                let emailAccount = '';
                let emailPassword = '';

                if (emailLine.includes('@gmail.com') && emailLine.includes('----') && emailLine.includes('orderId=')) {
                    // 特殊Gmail格式处理
                    const emailParts = emailLine.split('----');
                    emailAccount = emailParts[0] || '';  // 提取邮箱地址

                    // 从URL中提取orderId
                    if (emailParts.length >= 2) {
                        const urlPart = emailParts[1];
                        const orderIdMatch = urlPart.match(/orderId=([^&\s]+)/);
                        if (orderIdMatch) {
                            emailPassword = orderIdMatch[1];  // 使用orderId作为邮箱密码
                        }
                    }
                } else {
                    // 普通格式处理
                    const emailParts = emailLine.split('----');
                    emailAccount = emailParts[0] || '';
                    emailPassword = emailParts[1] || '';  // 提取邮箱密码
                }
                const randomPassword = generateRandomPassword();

                // 预处理数据行，处理特殊格式
                // 移除可能的 "=> MDBCheck.CC" 等后缀
                dataLine = dataLine.split('=>')[0].trim();
                
                // 分割数据，处理可能的混合分隔符情况
                let parts = [];
                if (dataLine.includes('|')) {
                    parts = dataLine.split('|').map(part => part.trim());
                    
                    // 处理可能的 "Live | " 前缀
                    if (parts[0].toLowerCase() === 'live') {
                        parts.shift(); // 移除"Live"
                    }
                }
                
                // 确保数据格式正确
                if (parts.length >= 4) {
                    // 解析卡号、有效期、CVV等信息
                    const cardNumber = parts[0] || "";
                    
                    // 处理有效期，可能是 "MM/YY" 或 "MM" "YY" 分开的情况
                    let month = "", year = "";
                    if (parts[1] && parts[1].includes('/')) {
                        const dateParts = parts[1].split('/');
                        month = dateParts[0];
                        year = dateParts[1];
                    } else {
                        month = parts[1] || "";
                        year = parts[2] || "";
                    }
                    
                    // 找出CVV、持卡人姓名、地址等
                    const cvv = parts[1].includes('/') ? parts[2] : parts[3];

                    // 获取持卡人信息字段（可能包含多个信息用制表符或空格分隔）
                    let holderInfoField = parts[1].includes('/') ? parts[3] : parts[4];

                    // 解析持卡人信息字段
                    let holderName = '', address = '', city = '', state = '', zipCode = '', countryCode = selectedCountry;

                    if (holderInfoField && holderInfoField.includes('\t')) {
                        // 用制表符分隔的格式
                        const holderParts = holderInfoField.split('\t').map(p => p.trim()).filter(p => p);
                        holderName = holderParts[0] || '';
                        address = holderParts[1] || '';
                        city = holderParts[2] || '';
                        state = holderParts[3] || '';
                        zipCode = holderParts[4] || '';
                        if (holderParts[5] && holderParts[5].length === 2) {
                            countryCode = holderParts[5];
                        }
                    } else if (holderInfoField) {
                        // 用空格分隔的格式，尝试智能解析
                        const holderParts = holderInfoField.split(/\s+/).filter(p => p);
                        if (holderParts.length >= 2) {
                            holderName = holderParts.slice(0, 2).join(' '); // 前两个词作为姓名
                            // 其余部分作为地址
                            if (holderParts.length > 2) {
                                address = holderParts.slice(2).join(' ');
                            }
                        } else {
                            holderName = holderInfoField;
                        }
                    }

                    // 如果没有从数据中解析到国家代码，使用选择的
                    if (!countryCode) {
                        countryCode = selectedCountry;
                    }

                    // 根据国家代码生成公司名称
                    const companyName = generateCompanyName(countryCode);

                    // 额外验证：如果解析出的邮编明显不是邮编格式，尝试重新映射
                    if (zipCode && !isValidZipCode(zipCode, countryCode)) {
                        // 如果当前邮编无效，检查是否是字段错位导致的
                        // 尝试在后续字段中寻找有效的邮编
                        for (let i = 8; i < parts.length && i < 12; i++) {
                            if (parts[i] && isValidZipCode(parts[i], countryCode)) {
                                zipCode = parts[i];
                                break;
                            }
                        }
                    }
                    /*for (let j = 8; j < parts.length; j++) {
                        if (parts[j] && parts[j].length === 2) {
                            countryCode = parts[j];
                            break;
                        }
                    }*/

                    // 智能填充姓名（如果缺失）
                    if (!holderName || holderName.trim() === '') {
                        holderName = generateNameByCountry(countryCode);
                    }

                    // 智能填充地址信息（如果缺失或无效）
                    if (!isAddressDataValid(address, city, state, zipCode, countryCode)) {
                        const filledAddress = smartFillAddress(address, city, state, zipCode, countryCode);
                        address = filledAddress.address;
                        city = filledAddress.city;
                        state = filledAddress.state;
                        zipCode = filledAddress.zipCode;
                    }

                    // 确保所有值都没有前后空格
                    const emailTrimmed = emailAccount.trim();

                    const formattedLine = [
                        emailTrimmed,                      // 邮箱地址
                        randomPassword,                    // 随机生成的密码
                        holderName || "",                  // 持卡人姓名
                        companyName,                       // 组织名称
                        (address || "").substring(0, 29),  // 地址行1，限制为29个字符
                        city || "",                        // 城市
                        state || "",                       // 州/省
                        (zipCode || "").replace(/\s+/g, ''), // 邮编
                        cardNumber.replace(/\s+/g, ''),    // 信用卡号
                        month,                             // 到期月份
                        year,                              // 到期年份
                        (cvv || "").replace(/\s+/g, ''),   // 安全码
                        holderName || "",                  // 持卡人姓名
                        emailPassword,                     // 邮箱密码
                        countryCode                        // 国家缩写
                    ].join('|');
                    result.push(formattedLine);
                }
            }

            // 创建文件内容
            let fileContent = result.join('\n');
            
            // 添加多余的内容
            if (extraEmails.length > 0) {
                fileContent += '\n\n多余的油箱：\n' + extraEmails.join('\n');
            }
            if (extraData.length > 0) {
                fileContent += '\n\n多余的调料：\n' + extraData.join('\n');
            }
            
            // 提示用户将下载两个文件
            alert('处理完成！将为您下载两个文件：\n1. 处理结果文件\n2. 油箱原始数据文件');

            // 创建主文件下载链接
            const date = new Date();
            const countryName = countryNameMap[selectedCountry] || selectedCountry;
            const baseFileName = `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}-${countryName}`;
            const fileName = `${baseFileName}.txt`;
            const blob = new Blob([fileContent], { type: 'text/plain' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = fileName;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);

            // 延迟0.5秒后下载油箱文件，避免浏览器多文件下载提示
            setTimeout(() => {
                const mailFileName = `${baseFileName} - mail.txt`;
                const mailBlob = new Blob([emailText], { type: 'text/plain' });
                const mailUrl = window.URL.createObjectURL(mailBlob);
                const mailA = document.createElement('a');
                mailA.href = mailUrl;
                mailA.download = mailFileName;
                document.body.appendChild(mailA);
                mailA.click();
                document.body.removeChild(mailA);
                window.URL.revokeObjectURL(mailUrl);
            }, 500);
        }

        // 实时更新行数显示的函数
        function updateLineCount(textareaId, titleId, baseName) {
            const textarea = document.getElementById(textareaId);
            const title = document.getElementById(titleId);
            const text = textarea.value;
            const nonEmptyLines = text.split('\n').filter(line => line.trim()).length;
            title.textContent = `${baseName} (${nonEmptyLines})`;
        }

        // 为调料和油箱添加输入事件监听器
        document.getElementById('dataInput').addEventListener('input', function() {
            updateLineCount('dataInput', 'dataTitle', '调料');
        });

        document.getElementById('emailInput').addEventListener('input', function() {
            updateLineCount('emailInput', 'emailTitle', '油箱');
        });

        // 页面加载时初始化行数显示
        document.addEventListener('DOMContentLoaded', function() {
            updateLineCount('dataInput', 'dataTitle', '调料');
            updateLineCount('emailInput', 'emailTitle', '油箱');
        });

        document.getElementById('processBtn').addEventListener('click', processData);
    </script>
</body>
</html> 