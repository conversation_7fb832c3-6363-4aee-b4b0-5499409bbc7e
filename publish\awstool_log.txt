2025-08-05 16:28:20 [信息] AWS自动注册工具启动
2025-08-05 16:28:20 [信息] 程序版本: 1.0.0.0
2025-08-05 16:28:20 [信息] 启动时间: 2025-08-05 16:28:20
2025-08-05 16:28:20 [信息] 浏览器模式切换: 无痕 Chrome 模式
2025-08-05 16:28:20 [信息] 线程数量已选择: 1
2025-08-05 16:28:20 [信息] 线程数量选择初始化完成
2025-08-05 16:28:20 [信息] 程序初始化完成
2025-08-05 16:28:27 [按钮操作] 选择文件 -> 打开文件选择对话框
2025-08-05 16:28:28 [信息] 已选择文件: C:\Users\<USER>\Desktop\2025-08-05-智利.txt
2025-08-05 16:28:29 [按钮操作] 加载信息 -> 加载数据文件: C:\Users\<USER>\Desktop\2025-08-05-智利.txt
2025-08-05 16:28:29 [信息] 成功加载 8 条数据
2025-08-05 16:28:30 [信息] 线程数量已选择: 3
2025-08-05 16:28:35 [按钮操作] 开始注册 -> 启动注册流程
2025-08-05 16:28:35 [信息] 开始启动多线程注册，线程数量: 3
2025-08-05 16:28:35 [信息] 开始启动多线程注册，线程数量: 3，数据条数: 8
2025-08-05 16:28:35 [信息] 所有线程已停止并清理
2025-08-05 16:28:35 [信息] 正在初始化多线程服务...
2025-08-05 16:28:35 [信息] 榴莲手机API服务已初始化
2025-08-05 16:28:35 [信息] 手机号码管理器已初始化，服务商: Durian，将在第一个线程完成第二页后获取手机号码
2025-08-05 16:28:35 [信息] 多线程服务初始化完成
2025-08-05 16:28:35 [信息] 数据分配完成：共8条数据分配给3个线程
2025-08-05 16:28:35 [信息] 线程1分配到3条数据
2025-08-05 16:28:35 [信息] 线程2分配到3条数据
2025-08-05 16:28:35 [信息] 线程3分配到2条数据
2025-08-05 16:28:35 [信息] 屏幕工作区域: 1280x672
2025-08-05 16:28:35 [信息] 线程1窗口布局: 位置(0, 0), 大小(384x200), 列1行1, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-05 16:28:35 [信息] 线程1获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-05 16:28:35 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-05 16:28:35 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_010, WebGL=ANGLE (Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0), CPU=2核, RAM=14 GB
2025-08-05 16:28:35 线程1：[信息] 已创建，窗口位置: (0, 0)
2025-08-05 16:28:35 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-05 16:28:35 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-05 16:28:35 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-05 16:28:35 [信息] 线程1已创建，窗口位置: (0, 0)，指纹: 国家=CL, 时区=America/Santiago
2025-08-05 16:28:35 [信息] 屏幕工作区域: 1280x672
2025-08-05 16:28:35 [信息] 线程2窗口布局: 位置(0, 219), 大小(384x200), 列1行2, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-05 16:28:35 [信息] 线程2获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-05 16:28:35 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-05 16:28:35 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_003, WebGL=ANGLE (AMD Radeon RX 6600 Direct3D11 vs_5_0 ps_5_0), CPU=10核, RAM=4 GB
2025-08-05 16:28:35 线程2：[信息] 已创建，窗口位置: (0, 219)
2025-08-05 16:28:35 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-05 16:28:35 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-05 16:28:35 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-05 16:28:35 [信息] 线程2已创建，窗口位置: (0, 219)，指纹: 国家=CL, 时区=America/Santiago
2025-08-05 16:28:35 [信息] 屏幕工作区域: 1280x672
2025-08-05 16:28:35 [信息] 线程3窗口布局: 位置(0, 438), 大小(384x200), 列1行3, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-05 16:28:35 [信息] 线程3获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-05 16:28:35 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-05 16:28:35 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_011, WebGL=ANGLE (NVIDIA GeForce RTX 3060 Direct3D11 vs_5_0 ps_5_0), CPU=10核, RAM=64 GB
2025-08-05 16:28:35 线程3：[信息] 已创建，窗口位置: (0, 438)
2025-08-05 16:28:35 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-05 16:28:35 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-05 16:28:35 [信息] 线程3已创建，窗口位置: (0, 438)，指纹: 国家=CL, 时区=America/Santiago
2025-08-05 16:28:35 [信息] 多线程注册启动成功，共3个线程
2025-08-05 16:28:35 线程1：[信息] 开始启动注册流程
2025-08-05 16:28:35 线程2：[信息] 开始启动注册流程
2025-08-05 16:28:35 线程3：[信息] 开始启动注册流程
2025-08-05 16:28:35 线程2：[信息] 开始启动浏览器: 位置(0, 219), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0
2025-08-05 16:28:35 线程1：[信息] 开始启动浏览器: 位置(0, 0), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0
2025-08-05 16:28:35 线程1：[信息] 启动无痕Chrome浏览器...
2025-08-05 16:28:35 线程2：[信息] 启动无痕Chrome浏览器...
2025-08-05 16:28:35 线程3：[信息] 开始启动浏览器: 位置(0, 438), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0
2025-08-05 16:28:35 线程3：[信息] 启动无痕Chrome浏览器...
2025-08-05 16:28:35 线程2：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-05 16:28:35 线程1：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-05 16:28:35 线程3：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-05 16:28:35 [信息] 多线程管理窗口已初始化
2025-08-05 16:28:35 [信息] UniformGrid列数已更新为: 1
2025-08-05 16:28:35 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 0, 列数: 1
2025-08-05 16:28:35 [信息] 多线程管理窗口已打开
2025-08-05 16:28:35 [信息] 多线程注册启动成功，共3个线程
2025-08-05 16:28:40 [信息] UniformGrid列数已更新为: 1
2025-08-05 16:28:40 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 1, 列数: 1
2025-08-05 16:28:40 线程2：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-05 16:28:40 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-05 16:28:40 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36
2025-08-05 16:28:40 线程2：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-05 16:28:41 [信息] UniformGrid列数已更新为: 1
2025-08-05 16:28:41 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-08-05 16:28:41 线程3：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-05 16:28:41 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-05 16:28:41 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36
2025-08-05 16:28:41 线程3：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-05 16:28:41 [信息] UniformGrid列数已更新为: 2
2025-08-05 16:28:41 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 3, 列数: 2
2025-08-05 16:28:41 线程1：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-05 16:28:41 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-05 16:28:41 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36
2025-08-05 16:28:42 线程1：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-05 16:28:43 线程3：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-05 16:28:43 线程1：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-05 16:28:43 线程2：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-05 16:28:45 线程3：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-05 16:28:45 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-05 16:28:45 线程3：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-05 16:28:45 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-05 16:28:45 线程3：[信息] [信息] 多线程模式使用指纹地理位置: -36.8201, -73.0444 (进度: 0%)
2025-08-05 16:28:45 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-36.8201, 经度=-73.0444
2025-08-05 16:28:45 线程3：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_011, CPU: 10核 (进度: 0%)
2025-08-05 16:28:45 [信息] 浏览器指纹注入: Canvas=canvas_fp_011, WebGL=ANGLE (NVIDIA GeForce RTX 3060 Direct3D11 vs_5_0 ps_5_0), CPU=10核, RAM=64 GB
2025-08-05 16:28:45 线程1：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-05 16:28:45 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-05 16:28:45 线程1：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-05 16:28:45 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-05 16:28:45 线程1：[信息] [信息] 多线程模式使用指纹地理位置: -29.9027, -71.2519 (进度: 0%)
2025-08-05 16:28:45 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-29.9027, 经度=-71.2519
2025-08-05 16:28:45 线程2：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-05 16:28:45 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-05 16:28:45 线程2：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-05 16:28:45 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-05 16:28:45 线程2：[信息] [信息] 多线程模式使用指纹地理位置: -33.4489, -70.6693 (进度: 0%)
2025-08-05 16:28:45 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-33.4489, 经度=-70.6693
2025-08-05 16:28:46 线程2：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_003, CPU: 10核 (进度: 0%)
2025-08-05 16:28:46 [信息] 浏览器指纹注入: Canvas=canvas_fp_003, WebGL=ANGLE (AMD Radeon RX 6600 Direct3D11 vs_5_0 ps_5_0), CPU=10核, RAM=4 GB
2025-08-05 16:28:46 线程1：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_010, CPU: 2核 (进度: 0%)
2025-08-05 16:28:46 [信息] 浏览器指纹注入: Canvas=canvas_fp_010, WebGL=ANGLE (Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0), CPU=2核, RAM=14 GB
2025-08-05 16:28:47 线程3：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-05 16:28:48 线程1：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-05 16:28:48 线程2：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-05 16:28:50 线程3：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 10
   • 设备内存: 64 GB
   • 平台信息: Win32
   • Do Not Track: auto

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 3060 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (NVIDIA Corporation)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 3C4D5E6F
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_012
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-G5H6I7J
   • MAC地址: 12-34-56-78-9A-BC
   • 屏幕分辨率: 2104x1074
   • 可用区域: 2104x1034

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: C9D0E1F2
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: bluetooth
   • 电池API支持: True
   • 电池电量: 0.50
   • 电池充电状态: False
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-05 16:28:50 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 10    • 设备内存: 64 GB    • 平台信息: Win32    • Do Not Track: auto   图形渲染信息:    • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 3060 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (NVIDIA Corporation)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 3C4D5E6F    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_012    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-G5H6I7J    • MAC地址: 12-34-56-78-9A-BC    • 屏幕分辨率: 2104x1074    • 可用区域: 2104x1034   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: C9D0E1F2    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: bluetooth    • 电池API支持: True    • 电池电量: 0.50    • 电池充电状态: False    • 性能API支持: True  ================================
2025-08-05 16:28:50 线程2：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 10
   • 设备内存: 4 GB
   • 平台信息: Win32
   • Do Not Track: undefined

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (AMD Radeon RX 6600 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (NVIDIA Corporation)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 1E2F3A4B
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_012
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-E4F5G6H
   • MAC地址: 9A-BC-DE-F0-12-34
   • 屏幕分辨率: 1829x1070
   • 可用区域: 1829x1030

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: A7B8C9D0
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: wifi
   • 电池API支持: True
   • 电池电量: 0.50
   • 电池充电状态: True
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-05 16:28:50 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 10    • 设备内存: 4 GB    • 平台信息: Win32    • Do Not Track: undefined   图形渲染信息:    • WebGL渲染器: ANGLE (AMD Radeon RX 6600 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (NVIDIA Corporation)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 1E2F3A4B    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_012    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-E4F5G6H    • MAC地址: 9A-BC-DE-F0-12-34    • 屏幕分辨率: 1829x1070    • 可用区域: 1829x1030   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: A7B8C9D0    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: wifi    • 电池API支持: True    • 电池电量: 0.50    • 电池充电状态: True    • 性能API支持: True  ================================
2025-08-05 16:28:50 线程3：[信息] [信息] 已设置浏览器标题: 线程3 - AWS注册工具 (进度: 5%)
2025-08-05 16:28:50 线程3：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-05 16:28:50 线程3：[信息] 浏览器启动成功
2025-08-05 16:28:50 线程1：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 2
   • 设备内存: 14 GB
   • 平台信息: Win32
   • Do Not Track: auto

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (NVIDIA Technologies)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 1E2F3A4B
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_006
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-M0N1O2P
   • MAC地址: 34-56-78-9A-BC-DE
   • 屏幕分辨率: 1742x1100
   • 可用区域: 1742x1060

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: C1D2E3F4
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: ethernet
   • 电池API支持: True
   • 电池电量: 0.59
   • 电池充电状态: True
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-05 16:28:50 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 2    • 设备内存: 14 GB    • 平台信息: Win32    • Do Not Track: auto   图形渲染信息:    • WebGL渲染器: ANGLE (Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (NVIDIA Technologies)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 1E2F3A4B    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_006    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-M0N1O2P    • MAC地址: 34-56-78-9A-BC-DE    • 屏幕分辨率: 1742x1100    • 可用区域: 1742x1060   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: C1D2E3F4    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: ethernet    • 电池API支持: True    • 电池电量: 0.59    • 电池充电状态: True    • 性能API支持: True  ================================
2025-08-05 16:28:50 线程3：[信息] 获取下一个数据: <EMAIL>
2025-08-05 16:28:50 线程3：[信息] 开始处理账户: <EMAIL>
2025-08-05 16:28:50 线程3：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-05 16:28:50 线程3：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-05 16:28:50 线程3：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-05 16:28:50 线程3：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-05 16:28:50 线程3：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-05 16:28:50 线程1：[信息] [信息] 已设置浏览器标题: 线程1 - AWS注册工具 (进度: 5%)
2025-08-05 16:28:50 线程1：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-05 16:28:50 线程1：[信息] 浏览器启动成功
2025-08-05 16:28:50 线程1：[信息] 获取下一个数据: <EMAIL>
2025-08-05 16:28:51 线程1：[信息] 开始处理账户: <EMAIL>
2025-08-05 16:28:51 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-05 16:28:51 线程1：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-05 16:28:51 线程1：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-05 16:28:51 线程1：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-05 16:28:51 线程1：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-05 16:28:51 线程2：[信息] [信息] 已设置浏览器标题: 线程2 - AWS注册工具 (进度: 5%)
2025-08-05 16:28:51 线程2：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-05 16:28:51 线程2：[信息] 浏览器启动成功
2025-08-05 16:28:51 线程2：[信息] 获取下一个数据: <EMAIL>
2025-08-05 16:28:51 线程2：[信息] 开始处理账户: <EMAIL>
2025-08-05 16:28:51 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-05 16:28:51 线程2：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-05 16:28:51 线程2：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-05 16:28:51 线程2：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-05 16:28:51 线程2：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-05 16:28:51 线程3：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-05 16:28:51 线程3：[信息] [信息] 已重新设置浏览器标题: 线程3 - AWS注册工具 (进度: 98%)
2025-08-05 16:28:51 线程3：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-05 16:28:51 线程1：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-05 16:28:51 线程3：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-05 16:28:51 线程3：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-05 16:28:51 线程3：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-05 16:28:51 线程1：[信息] [信息] 已重新设置浏览器标题: 线程1 - AWS注册工具 (进度: 98%)
2025-08-05 16:28:51 线程1：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-05 16:28:51 线程1：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-05 16:28:51 线程1：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-05 16:28:51 线程1：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-05 16:28:52 线程2：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-05 16:28:52 线程2：[信息] [信息] 已重新设置浏览器标题: 线程2 - AWS注册工具 (进度: 98%)
2025-08-05 16:28:52 线程2：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-05 16:28:52 线程2：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-05 16:28:52 线程2：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-05 16:28:52 线程2：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-05 16:29:15 线程2：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程2 - AWS注册 (进度: 98%)
2025-08-05 16:29:15 线程2：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-05 16:29:15 线程2：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-05 16:29:15 线程2：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-05 16:29:15 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-05 16:29:16 线程2：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-05 16:29:19 线程2：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-05 16:29:19 线程2：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-05 16:29:19 线程2：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-05 16:29:19 线程2：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-05 16:29:19 线程2：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-05 16:29:19 线程2：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-05 16:29:19 线程1：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程1 - AWS注册 (进度: 98%)
2025-08-05 16:29:19 线程1：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-05 16:29:19 线程1：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-05 16:29:19 线程1：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-05 16:29:19 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-05 16:29:20 线程1：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-05 16:29:21 线程2：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-05 16:29:21 线程2：[信息] [信息] 🔍 第2次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-05 16:29:21 线程2：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 未发现验证码 (进度: 100%)
2025-08-05 16:29:21 线程2：[信息] [信息] ✅ 第一页未检测到图形验证码，继续流程 (进度: 100%)
2025-08-05 16:29:21 线程2：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-05 16:29:21 线程2：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-05 16:29:21 线程2：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-05 16:29:21 线程2：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-05 16:29:21 线程2：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-05 16:29:21 线程2：[信息] 账户注册流程已启动: <EMAIL>
2025-08-05 16:29:21 线程2：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-05 16:29:21 [信息] [线程2] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-05 16:29:21 [信息] [线程2] 等待2秒后开始第一次触发...
2025-08-05 16:29:21 线程3：[信息] [信息] 注册失败: Timeout 30000ms exceeded.
Call log:
  - navigating to "https://portal.aws.amazon.com/gp/aws/developer/registration/index.html?refid=em_127222&p=free&c=hp&z=1", waiting until "load" (进度: 98%)
2025-08-05 16:29:21 线程3：[信息] [信息] 第一页相关失败，数据保持不动 (进度: 98%)
2025-08-05 16:29:21 [信息] 第一页相关失败，数据保持不动
2025-08-05 16:29:21 线程3：[错误] 账户注册启动失败: <EMAIL>
2025-08-05 16:29:21 线程3：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-05 16:29:23 线程1：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-05 16:29:23 线程1：[信息] [信息] ⚠️ 检测到错误信息，开始重试机制... (进度: 98%)
2025-08-05 16:29:23 [信息] 检测到错误信息，开始重试机制
2025-08-05 16:29:23 线程1：[信息] [信息] 🔄 第1次重试点击验证邮箱按钮... (进度: 98%)
2025-08-05 16:29:23 [信息] 第1次重试点击验证邮箱按钮
2025-08-05 16:29:23 [信息] [线程2] 第1次触发邮箱验证码获取...（最多20次）
2025-08-05 16:29:23 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-05 16:29:23
2025-08-05 16:29:25 线程1：[信息] [信息] ✅ 第1次重试：已点击验证邮箱按钮 (进度: 100%)
2025-08-05 16:29:25 [信息] 第1次重试：已点击验证邮箱按钮
2025-08-05 16:29:26 [信息] [线程2] 第2次触发邮箱验证码获取...（最多20次）
2025-08-05 16:29:26 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-05 16:29:26
2025-08-05 16:29:27 线程1：[信息] [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']" (进度: 100%)
2025-08-05 16:29:27 [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']"
2025-08-05 16:29:28 线程1：[信息] [信息] ✅ 第1次重试成功：已到达第二页，继续正常流程 (进度: 100%)
2025-08-05 16:29:28 [信息] 第1次重试成功：已到达第二页
2025-08-05 16:29:28 线程1：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-05 16:29:28 线程1：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-05 16:29:29 线程1：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-05 16:29:29 线程1：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-05 16:29:30 [信息] [线程2] 第3次触发邮箱验证码获取...（最多20次）
2025-08-05 16:29:30 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-05 16:29:30
2025-08-05 16:29:31 线程1：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-05 16:29:31 线程1：[信息] [信息] 🔍 第2次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-05 16:29:31 线程1：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 未发现验证码 (进度: 100%)
2025-08-05 16:29:31 线程1：[信息] [信息] ✅ 第一页未检测到图形验证码，继续流程 (进度: 100%)
2025-08-05 16:29:31 线程1：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-05 16:29:31 线程1：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-05 16:29:31 线程1：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-05 16:29:31 线程1：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-05 16:29:31 线程1：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-05 16:29:31 线程1：[信息] 账户注册流程已启动: <EMAIL>
2025-08-05 16:29:31 线程1：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-05 16:29:31 [信息] [线程1] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-05 16:29:31 [信息] [线程1] 等待2秒后开始第一次触发...
2025-08-05 16:29:33 [信息] [线程2] 第4次触发邮箱验证码获取...（最多20次）
2025-08-05 16:29:33 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-05 16:29:33
2025-08-05 16:29:33 [信息] [线程1] 第1次触发邮箱验证码获取...（最多20次）
2025-08-05 16:29:33 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-05 16:29:33
2025-08-05 16:29:34 [信息] [线程2] 邮箱验证码获取成功: 438216，立即停止重复请求
2025-08-05 16:29:34 [信息] [线程2] 已清理请求文件，停止重复触发
2025-08-05 16:29:34 [信息] [线程2] 已清理响应文件
2025-08-05 16:29:34 线程2：[信息] [信息] 验证码获取成功: 438216，正在自动填入... (进度: 25%)
2025-08-05 16:29:34 线程2：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 25%)
2025-08-05 16:29:35 线程2：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 25%)
2025-08-05 16:29:35 线程2：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 25%)
2025-08-05 16:29:35 [信息] 线程2完成第二页事件已处理
2025-08-05 16:29:35 [信息] 线程2完成第二页，开始批量获取手机号码...
2025-08-05 16:29:35 线程2：[信息] [信息] 线程2第二页验证完成，已通知管理器 (进度: 35%)
2025-08-05 16:29:35 [信息] 开始批量获取3个手机号码，服务商: Durian
2025-08-05 16:29:35 [信息] [手机API] 批量获取3个手机号码，URL: https://api.durianrcs.com/out/ext_api/getMobile?name=oneone&ApiKey=NnlaR01xQm9hMlAwRnJDSVB2SG1kQT09&cuy=mx&pid=0209&num=3&noblack=0&serial=2&secret_key=null&vip=null
2025-08-05 16:29:37 [信息] [线程1] 第2次触发邮箱验证码获取...（最多20次）
2025-08-05 16:29:37 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-05 16:29:37
2025-08-05 16:29:38 线程2：[信息] [信息] 等待密码设置页面加载... (进度: 38%)
2025-08-05 16:29:39 线程2：[信息] [信息] 开始填写密码信息... (进度: 38%)
2025-08-05 16:29:39 线程2：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-05 16:29:39 线程2：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-05 16:29:39 线程2：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 38%)
2025-08-05 16:29:40 [信息] [手机API] 批量获取响应内容: {"code":200,"msg":"Success","data":["+525545388081","+528712405636","+524793287401"]}
2025-08-05 16:29:40 [信息] [手机API] 检测到数组格式，元素数量: 3
2025-08-05 16:29:40 [信息] [手机API] 批量获取成功，获得3个手机号码
2025-08-05 16:29:40 [信息] 线程1分配榴莲手机号码: +525545388081
2025-08-05 16:29:40 [信息] 线程2分配榴莲手机号码: +528712405636
2025-08-05 16:29:40 [信息] 线程3分配榴莲手机号码: +524793287401
2025-08-05 16:29:40 [信息] 榴莲API批量获取手机号码成功，已分配给3个线程
2025-08-05 16:29:40 [信息] 批量获取3个手机号码成功
2025-08-05 16:29:40 [信息] [线程1] 第3次触发邮箱验证码获取...（最多20次）
2025-08-05 16:29:40 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-05 16:29:40
2025-08-05 16:29:40 线程2：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 38%)
2025-08-05 16:29:41 [信息] [线程1] 邮箱验证码获取成功: 029857，立即停止重复请求
2025-08-05 16:29:41 [信息] [线程1] 已清理请求文件，停止重复触发
2025-08-05 16:29:41 [信息] [线程1] 已清理响应文件
2025-08-05 16:29:41 线程1：[信息] [信息] 验证码获取成功: 029857，正在自动填入... (进度: 25%)
2025-08-05 16:29:41 线程1：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 25%)
2025-08-05 16:29:50 线程1：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 25%)
2025-08-05 16:29:50 线程1：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 25%)
2025-08-05 16:29:50 [信息] 线程1完成第二页事件已处理
2025-08-05 16:29:50 [信息] 线程1完成第二页，手机号码已获取，无需重复获取
2025-08-05 16:29:50 线程1：[信息] [信息] 线程1第二页验证完成，已通知管理器 (进度: 35%)
2025-08-05 16:29:53 线程1：[信息] [信息] 等待密码设置页面加载... (进度: 38%)
2025-08-05 16:29:53 线程1：[信息] [信息] 开始填写密码信息... (进度: 38%)
2025-08-05 16:29:53 线程1：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-05 16:29:53 线程1：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-05 16:29:53 线程1：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 38%)
2025-08-05 16:29:54 线程1：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 38%)
2025-08-05 16:29:54 线程2：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 38%)
2025-08-05 16:29:54 线程2：[信息] [信息] 等待账户类型确认页面加载... (进度: 38%)
2025-08-05 16:29:54 线程2：[信息] [信息] 开始处理账户类型确认... (进度: 38%)
2025-08-05 16:29:58 线程1：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 38%)
2025-08-05 16:29:58 线程1：[信息] [信息] 等待账户类型确认页面加载... (进度: 38%)
2025-08-05 16:29:58 线程1：[信息] [信息] 开始处理账户类型确认... (进度: 38%)
2025-08-05 16:30:00 线程2：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 38%)
2025-08-05 16:30:00 线程2：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 38%)
2025-08-05 16:30:03 线程1：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 38%)
2025-08-05 16:30:03 线程1：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 38%)
2025-08-05 16:30:24 线程2：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 38%)
2025-08-05 16:30:24 [信息] 线程2获取已分配的榴莲手机号码: +528712405636
2025-08-05 16:30:24 线程2：[信息] [信息] 多线程模式：使用已分配的手机号码 +528712405636 (进度: 38%)
2025-08-05 16:30:28 线程1：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 38%)
2025-08-05 16:30:28 [信息] 线程1获取已分配的榴莲手机号码: +525545388081
2025-08-05 16:30:28 线程1：[信息] [信息] 多线程模式：使用已分配的手机号码 +525545388081 (进度: 38%)
2025-08-05 16:30:31 线程2：[信息] [信息] 数据国家代码为CL，需要选择Chile (进度: 38%)
2025-08-05 16:30:31 线程2：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 38%)
2025-08-05 16:30:31 线程1：[信息] [信息] 数据国家代码为CL，需要选择Chile (进度: 38%)
2025-08-05 16:30:32 线程1：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 38%)
2025-08-05 16:30:33 线程2：[信息] [信息] 已选择国家: Chile (进度: 38%)
2025-08-05 16:30:33 线程2：[信息] [信息] 已成功选择国家: Chile (进度: 38%)
2025-08-05 16:30:33 线程2：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 38%)
2025-08-05 16:30:33 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-08-05 16:30:34 线程1：[信息] [信息] 已选择国家: Chile (进度: 38%)
2025-08-05 16:30:34 线程1：[信息] [信息] 已成功选择国家: Chile (进度: 38%)
2025-08-05 16:30:34 线程1：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 38%)
2025-08-05 16:30:34 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-08-05 16:30:36 线程2：[信息] [信息] 已选择国家代码 +52 (进度: 38%)
2025-08-05 16:30:37 线程2：[信息] [信息] 等待后台获取的手机号码结果... (进度: 38%)
2025-08-05 16:30:37 线程2：[信息] [信息] 已自动获取并填入手机号码: +528712405636 (进度: 38%)
2025-08-05 16:30:38 线程2：[信息] [信息] 使用已获取的手机号码: +528712405636（保存本地号码: +528712405636） (进度: 38%)
2025-08-05 16:30:38 线程2：[信息] [信息] 联系信息完成，等待页面加载... (进度: 38%)
2025-08-05 16:30:39 线程1：[信息] [信息] 已选择国家代码 +52 (进度: 38%)
2025-08-05 16:30:40 线程1：[信息] [信息] 等待后台获取的手机号码结果... (进度: 38%)
2025-08-05 16:30:40 线程1：[信息] [信息] 已自动获取并填入手机号码: +525545388081 (进度: 38%)
2025-08-05 16:30:41 线程1：[信息] [信息] 使用已获取的手机号码: +525545388081（保存本地号码: +525545388081） (进度: 38%)
2025-08-05 16:30:41 线程1：[信息] [信息] 联系信息完成，等待页面加载... (进度: 38%)
2025-08-05 16:30:41 线程2：[信息] [信息] 进入付款信息页面... (进度: 38%)
2025-08-05 16:30:42 线程2：[信息] [信息] 正在选择月份: April (进度: 38%)
2025-08-05 16:30:43 线程2：[信息] [信息] 已选择月份（标准选项）: April (进度: 38%)
2025-08-05 16:30:43 线程2：[信息] [信息] 正在选择年份: 2027 (进度: 38%)
2025-08-05 16:30:44 线程2：[信息] [信息] 已选择年份（标准选项）: 2027 (进度: 38%)
2025-08-05 16:30:44 线程1：[信息] [信息] 进入付款信息页面... (进度: 38%)
2025-08-05 16:30:44 线程2：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 38%)
2025-08-05 16:30:44 线程2：[信息] [信息] 开始填写验证码验证页面... (进度: 38%)
2025-08-05 16:30:44 线程2：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 38%)
2025-08-05 16:30:45 线程1：[信息] [信息] 正在选择月份: November (进度: 38%)
2025-08-05 16:30:45 线程1：[信息] [信息] 已选择月份（标准选项）: November (进度: 38%)
2025-08-05 16:30:46 线程1：[信息] [信息] 正在选择年份: 2027 (进度: 38%)
2025-08-05 16:30:47 线程1：[信息] [信息] 已选择年份（标准选项）: 2027 (进度: 38%)
2025-08-05 16:30:48 线程1：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 38%)
2025-08-05 16:30:48 线程1：[信息] [信息] 开始填写验证码验证页面... (进度: 38%)
2025-08-05 16:30:48 线程1：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 38%)
2025-08-05 16:30:50 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-08-05 16:30:51 线程2：[信息] [信息] 已选择国家代码: +52 (进度: 38%)
2025-08-05 16:30:51 线程2：[信息] [信息] 已清空并重新填写手机号码: +528712405636 (进度: 38%)
2025-08-05 16:30:51 线程2：[信息] [信息] 已点击发送验证码按钮 (进度: 38%)
2025-08-05 16:30:53 线程2：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 38%)
2025-08-05 16:30:53 线程2：[信息] [信息] ⚠️ 检测到错误信息，开始重试机制... (进度: 38%)
2025-08-05 16:30:53 [信息] 检测到错误信息，开始重试机制
2025-08-05 16:30:53 线程2：[信息] [信息] 🔄 第1次重试发送验证码按钮... (进度: 38%)
2025-08-05 16:30:53 [信息] 第1次重试发送验证码按钮
2025-08-05 16:30:54 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-08-05 16:30:55 线程1：[信息] [信息] 已选择国家代码: +52 (进度: 38%)
2025-08-05 16:30:55 线程1：[信息] [信息] 已清空并重新填写手机号码: +525545388081 (进度: 38%)
2025-08-05 16:30:56 线程1：[信息] [信息] 已点击发送验证码按钮 (进度: 38%)
2025-08-05 16:30:56 线程2：[信息] [信息] ✅ 第1次重试：已点击发送验证码按钮 (进度: 100%)
2025-08-05 16:30:56 [信息] 第1次重试：已点击发送验证码按钮
2025-08-05 16:30:58 线程1：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 38%)
2025-08-05 16:30:58 线程1：[信息] [信息] ⚠️ 检测到错误信息，开始重试机制... (进度: 38%)
2025-08-05 16:30:58 [信息] 检测到错误信息，开始重试机制
2025-08-05 16:30:58 线程1：[信息] [信息] 🔄 第1次重试发送验证码按钮... (进度: 38%)
2025-08-05 16:30:58 [信息] 第1次重试发送验证码按钮
2025-08-05 16:30:59 线程2：[信息] [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']" (进度: 100%)
2025-08-05 16:30:59 [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']"
2025-08-05 16:30:59 线程2：[信息] [信息] ❌ 第1次重试失败：错误信息仍然存在 (进度: 100%)
2025-08-05 16:30:59 [信息] 第1次重试失败：错误信息仍然存在
2025-08-05 16:30:59 线程2：[信息] [信息] 🔄 第2次重试发送验证码按钮... (进度: 100%)
2025-08-05 16:30:59 [信息] 第2次重试发送验证码按钮
2025-08-05 16:31:01 线程1：[信息] [信息] ✅ 第1次重试：已点击发送验证码按钮 (进度: 100%)
2025-08-05 16:31:01 [信息] 第1次重试：已点击发送验证码按钮
2025-08-05 16:31:02 线程2：[信息] [信息] ✅ 第2次重试：已点击发送验证码按钮 (进度: 100%)
2025-08-05 16:31:02 [信息] 第2次重试：已点击发送验证码按钮
2025-08-05 16:31:03 线程1：[信息] [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']" (进度: 100%)
2025-08-05 16:31:03 [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']"
2025-08-05 16:31:03 线程1：[信息] [信息] ❌ 第1次重试失败：错误信息仍然存在 (进度: 100%)
2025-08-05 16:31:03 [信息] 第1次重试失败：错误信息仍然存在
2025-08-05 16:31:03 线程1：[信息] [信息] 🔄 第2次重试发送验证码按钮... (进度: 100%)
2025-08-05 16:31:03 [信息] 第2次重试发送验证码按钮
2025-08-05 16:31:04 线程2：[信息] [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']" (进度: 100%)
2025-08-05 16:31:04 [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']"
2025-08-05 16:31:13 线程1：[信息] [信息] ✅ 第2次重试：已点击发送验证码按钮 (进度: 100%)
2025-08-05 16:31:13 [信息] 第2次重试：已点击发送验证码按钮
2025-08-05 16:31:13 线程2：[信息] [信息] ✅ 第2次重试成功：错误信息消失，继续正常流程 (进度: 100%)
2025-08-05 16:31:13 [信息] 第2次重试成功：错误信息消失
2025-08-05 16:31:13 线程2：[信息] [信息] 手机号码自动模式 + 图形验证码自动模式：开始自动处理... (进度: 100%)
2025-08-05 16:31:13 线程2：[信息] [信息] 自动模式：开始处理图形验证码... (进度: 100%)
2025-08-05 16:31:13 线程2：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-05 16:31:15 线程1：[信息] [信息] ✅ 检测到图形验证码存在 (进度: 100%)
2025-08-05 16:31:15 [信息] 检测到图形验证码存在
2025-08-05 16:31:15 线程1：[信息] [信息] ✅ 第2次重试成功：检测到图形验证码，继续正常流程 (进度: 100%)
2025-08-05 16:31:15 [信息] 第2次重试成功：检测到图形验证码
2025-08-05 16:31:15 线程1：[信息] [信息] 手机号码自动模式 + 图形验证码自动模式：开始自动处理... (进度: 100%)
2025-08-05 16:31:15 线程1：[信息] [信息] 自动模式：开始处理图形验证码... (进度: 100%)
2025-08-05 16:31:15 线程1：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-05 16:31:16 线程2：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-05 16:31:16 线程2：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-05 16:31:18 线程1：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-05 16:31:18 线程1：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-05 16:31:19 线程2：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 34814 字节 (进度: 100%)
2025-08-05 16:31:19 线程2：[信息] [信息] ✅ 图片验证通过：201x70px，34814字节，复杂度符合要求 (进度: 100%)
2025-08-05 16:31:19 线程2：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-05 16:31:20 线程2：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"2hpgmb"},"taskId":"90434e44-71d6-11f0-ac7a-3a376870684b"} (进度: 100%)
2025-08-05 16:31:20 线程2：[信息] [信息] 第六页第1次识别结果: 2hpgmb → 转换为小写: 2hpgmb (进度: 100%)
2025-08-05 16:31:20 线程2：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-05 16:31:20 线程2：[信息] [信息] 第六页已填入验证码: 2hpgmb (进度: 100%)
2025-08-05 16:31:20 线程2：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-05 16:31:21 线程1：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 35049 字节 (进度: 100%)
2025-08-05 16:31:21 线程1：[信息] [信息] ✅ 图片验证通过：201x70px，35049字节，复杂度符合要求 (进度: 100%)
2025-08-05 16:31:21 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-05 16:31:24 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"xgd7sw"},"taskId":"9263f62e-71d6-11f0-aaaf-6e28fd6820d3"} (进度: 100%)
2025-08-05 16:31:24 线程1：[信息] [信息] 第六页第1次识别结果: xgd7sw → 转换为小写: xgd7sw (进度: 100%)
2025-08-05 16:31:24 线程1：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-05 16:31:24 线程1：[信息] [信息] 第六页已填入验证码: xgd7sw (进度: 100%)
2025-08-05 16:31:24 线程1：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-05 16:31:25 线程2：[信息] [信息] 第1次图形验证码识别结果错误，等待新验证码 (进度: 100%)
2025-08-05 16:31:25 线程2：[信息] [信息] 第六页第1次识别异常: 验证码错误 (进度: 100%)
2025-08-05 16:31:27 线程2：[信息] [信息] 第六页第2次尝试自动识别图形验证码... (进度: 100%)
2025-08-05 16:31:27 线程1：[信息] [信息] 第1次图形验证码识别成功 (进度: 100%)
2025-08-05 16:31:27 线程1：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-08-05 16:31:30 线程2：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 31361 字节 (进度: 100%)
2025-08-05 16:31:30 线程2：[信息] [信息] ✅ 图片验证通过：200x70px，31361字节，复杂度符合要求 (进度: 100%)
2025-08-05 16:31:30 线程2：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-05 16:31:30 线程1：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-08-05 16:31:31 线程2：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"dpcsn3"},"taskId":"96978508-71d6-11f0-87f5-a646062fd64a"} (进度: 100%)
2025-08-05 16:31:31 线程2：[信息] [信息] 第六页第2次识别结果: dpcsn3 → 转换为小写: dpcsn3 (进度: 100%)
2025-08-05 16:31:31 线程2：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-05 16:31:31 线程2：[信息] [信息] 第六页已填入验证码: dpcsn3 (进度: 100%)
2025-08-05 16:31:31 线程2：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-05 16:31:33 线程1：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-05 16:31:33 线程1：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-05 16:31:33 线程1：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-05 16:31:33 线程1：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-08-05 16:31:35 线程2：[信息] [信息] 第2次图形验证码识别结果错误，等待新验证码 (进度: 100%)
2025-08-05 16:31:35 线程2：[信息] [信息] 第六页第2次识别异常: 验证码错误 (进度: 100%)
2025-08-05 16:31:37 线程2：[信息] [信息] 第六页第3次尝试自动识别图形验证码... (进度: 100%)
2025-08-05 16:31:38 线程1：[信息] [信息] 线程1开始独立获取验证码... (进度: 100%)
2025-08-05 16:31:38 线程1：[信息] [信息] 线程1开始获取验证码，最多尝试8次，每次间隔8秒 (进度: 100%)
2025-08-05 16:31:38 线程1：[信息] [信息] 线程1第1次尝试获取验证码...（剩余7次尝试） (进度: 100%)
2025-08-05 16:31:38 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-05 16:31:39 线程1：[信息] [信息] 线程1验证码获取成功: 8646 (进度: 100%)
2025-08-05 16:31:39 [信息] 线程1手机号码已加入释放队列: +525545388081 (原因: 获取验证码成功)
2025-08-05 16:31:39 线程1：[信息] [信息] 线程1验证码获取成功: 8646，立即填入验证码... (进度: 100%)
2025-08-05 16:31:39 线程1：[信息] [信息] 第七页：找到验证码输入框 (Verify code) (进度: 100%)
2025-08-05 16:31:41 线程1：[信息] [信息] 线程1已自动填入手机验证码: 8646 (进度: 100%)
2025-08-05 16:31:41 线程2：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 31482 字节 (进度: 100%)
2025-08-05 16:31:41 线程2：[信息] [信息] ✅ 图片验证通过：200x70px，31482字节，复杂度符合要求 (进度: 100%)
2025-08-05 16:31:41 线程2：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-05 16:31:42 线程1：[信息] [信息] 线程1正在自动点击Continue按钮... (进度: 100%)
2025-08-05 16:31:43 线程1：[信息] [信息] 线程1手机验证码验证完成，继续执行后续步骤... (进度: 100%)
2025-08-05 16:31:43 线程2：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"7x6d"},"taskId":"9da28a0a-71d6-11f0-8d33-d6daa566c6db"} (进度: 100%)
2025-08-05 16:31:43 线程2：[信息] [信息] 第六页第3次识别结果: 7x6d → 转换为小写: 7x6d (进度: 100%)
2025-08-05 16:31:43 线程2：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-05 16:31:43 线程2：[信息] [信息] 第六页已填入验证码: 7x6d (进度: 100%)
2025-08-05 16:31:43 线程2：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-05 16:31:46 线程1：[信息] [信息] 验证码验证完成，等待页面加载... (进度: 100%)
2025-08-05 16:31:46 线程1：[信息] [信息] 已点击完成注册按钮，正在处理... (进度: 100%)
2025-08-05 16:31:46 线程1：[信息] [信息] 自动模式验证完成，API手机号码已加入黑名单: 加入黑名单成功 (进度: 100%)
2025-08-05 16:31:47 线程1：[信息] [信息] 等待完成注册页面加载... (进度: 100%)
2025-08-05 16:31:47 线程2：[信息] [信息] 第3次图形验证码识别结果错误，等待新验证码 (进度: 100%)
2025-08-05 16:31:47 线程2：[信息] [信息] 第六页第3次识别异常: 验证码错误 (进度: 100%)
2025-08-05 16:31:47 线程2：[信息] [信息] 当前验证码抓取出错，需要手动完成图形验证码，并点击继续注册 (进度: 100%)
2025-08-05 16:31:50 线程1：[信息] [信息] 完成注册页面加载完成 (进度: 100%)
2025-08-05 16:31:50 线程1：[信息] [信息] 🔍 正在查找'Go to the AWS Management Console'链接... (进度: 100%)
2025-08-05 16:32:00 线程1：[信息] [信息] 🖱️ 已点击跳转管理控制台链接 (进度: 100%)
2025-08-05 16:32:00 线程1：[信息] [信息] 🔗 通过导航进入IAM安全凭证页面... (进度: 100%)
2025-08-05 16:32:00 线程1：[信息] [信息] ⏳ 等待管理控制台页面加载，智能检测'更多'按钮（超时20秒）... (进度: 100%)
2025-08-05 16:32:02 线程2：[信息] [信息]  继续注册被调用，当前状态: WaitingForSMSVerification，当前步骤: 6 (进度: 100%)
2025-08-05 16:32:02 线程2：[信息] [信息] 第六页手动模式继续注册，检测当前页面状态... (进度: 100%)
2025-08-05 16:32:02 线程2：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-05 16:32:02 线程2：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-05 16:32:03 线程2：[信息] [信息] 🎯 找到匹配按钮: 'Continue (step 4 of 5)' → 第7页 (进度: 100%)
2025-08-05 16:32:03 线程2：[信息] [信息] ✅ 直接确认为第7页 (进度: 100%)
2025-08-05 16:32:03 线程2：[信息] [信息] 检测到已跳转到第七页，更新步骤并执行第七页逻辑 (进度: 100%)
2025-08-05 16:32:03 线程2：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-05 16:32:03 线程2：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-05 16:32:03 线程2：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-05 16:32:03 线程2：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-08-05 16:32:05 [信息] 定时检查发现1个待释放手机号码，开始批量释放
2025-08-05 16:32:05 [信息] 开始释放1个手机号码
2025-08-05 16:32:05 [信息] [手机API] 开始批量释放1个手机号码
2025-08-05 16:32:05 [信息] [手机API] 释放手机号码: +525545388081
2025-08-05 16:32:07 [信息] [手机API] 手机号码释放成功: +525545388081
2025-08-05 16:32:07 [信息] [手机API] 批量释放完成: 成功1个, 失败0个
2025-08-05 16:32:07 [信息] 定时批量释放完成: 批量释放完成: 成功1个, 失败0个
2025-08-05 16:32:08 线程2：[信息] [信息] 线程2开始独立获取验证码... (进度: 100%)
2025-08-05 16:32:08 线程2：[信息] [信息] 线程2开始获取验证码，最多尝试8次，每次间隔8秒 (进度: 100%)
2025-08-05 16:32:08 线程2：[信息] [信息] 线程2第1次尝试获取验证码...（剩余7次尝试） (进度: 100%)
2025-08-05 16:32:08 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-05 16:32:08 线程2：[信息] [信息] 线程2验证码获取成功: 1146 (进度: 100%)
2025-08-05 16:32:08 [信息] 线程2手机号码已加入释放队列: +528712405636 (原因: 获取验证码成功)
2025-08-05 16:32:08 线程2：[信息] [信息] 线程2验证码获取成功: 1146，立即填入验证码... (进度: 100%)
2025-08-05 16:32:08 线程2：[信息] [信息] 第七页：找到验证码输入框 (Verify code) (进度: 100%)
2025-08-05 16:32:08 线程2：[信息] [信息] 线程2已自动填入手机验证码: 1146 (进度: 100%)
2025-08-05 16:32:09 线程2：[信息] [信息] 线程2正在自动点击Continue按钮... (进度: 100%)
2025-08-05 16:32:10 线程2：[信息] [信息] 线程2手机验证码验证完成，继续执行后续步骤... (进度: 100%)
2025-08-05 16:32:13 线程2：[信息] [信息] 检测到无资格错误提示: You are not eligible for new customer credits (进度: 100%)
2025-08-05 16:32:13 线程2：[信息] [信息] 线程2检测到卡号已被关联错误，注册失败 (进度: 100%)
2025-08-05 16:32:13 线程2：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：5ojgHD6XEU ③AWS密码：Cbvzxwe9 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联 (进度: 100%)
2025-08-05 16:32:13 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：5ojgHD6XEU ③AWS密码：Cbvzxwe9 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-05 16:32:13 线程2：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：5ojgHD6XEU ③AWS密码：Cbvzxwe9 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-05 16:32:13 [信息] 线程2请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：5ojgHD6XEU ③AWS密码：Cbvzxwe9 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-05 16:32:13 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：5ojgHD6XEU ③AWS密码：Cbvzxwe9 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-05 16:32:13 [信息] 线程2剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：5ojgHD6XEU ③AWS密码：Cbvzxwe9 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-05 16:32:13 线程2：[信息] 收到失败数据保存请求: 卡号已被关联, 数据: <EMAIL>
2025-08-05 16:32:13 [信息] 线程2请求保存失败数据: 卡号已被关联, 数据: <EMAIL>
2025-08-05 16:32:13 [信息] 已处理失败数据: <EMAIL>, 失败原因: 卡号已被关联
2025-08-05 16:32:13 [信息] 线程2失败数据已保存: 卡号已被关联, 数据: <EMAIL>
2025-08-05 16:32:13 线程2：[信息] [信息] 已通知保存失败数据，失败原因: 卡号已被关联 (进度: 0%)
2025-08-05 16:32:13 线程2：[信息] [信息] 线程2注册失败，数据已归类，注册流程终止 (进度: 0%)
2025-08-05 16:32:13 线程2：[信息] 已继续
2025-08-05 16:32:13 [信息] 线程2已继续
2025-08-05 16:32:18 线程1：[信息] [信息] ✅ 找到'更多'按钮，页面加载完成 (进度: 100%)
2025-08-05 16:32:18 线程1：[信息] [信息] 🖱️ 正在点击更多按钮... (进度: 100%)
2025-08-05 16:32:19 线程1：[信息] [信息] ✅ 成功点击更多按钮 (进度: 100%)
2025-08-05 16:32:19 [信息] 成功点击更多按钮
2025-08-05 16:32:20 线程1：[信息] [信息] 🖱️ 正在点击账户信息按钮... (进度: 100%)
2025-08-05 16:32:20 线程1：[信息] [信息] ✅ 成功点击账户信息按钮 (进度: 100%)
2025-08-05 16:32:20 [信息] 成功点击账户信息按钮
2025-08-05 16:32:21 线程1：[信息] [信息] 🖱️ 正在点击'安全凭证'菜单项... (进度: 100%)
2025-08-05 16:32:21 线程1：[信息] [信息] 🔍 正在定位'安全凭证'链接... (进度: 100%)
2025-08-05 16:32:21 线程1：[信息] [信息] ✅ 成功定位到'安全凭证'链接 (进度: 100%)
2025-08-05 16:32:21 [信息] 成功定位到'安全凭证'链接
2025-08-05 16:32:31 线程1：[信息] [信息] ✅ 成功点击'安全凭证'菜单项 (进度: 100%)
2025-08-05 16:32:31 [信息] 成功点击'安全凭证'链接
2025-08-05 16:32:31 线程1：[信息] [信息] ⏳ 等待IAM安全凭证页面加载（20秒超时，循环检测账单问题和创建密钥按钮）... (进度: 100%)
2025-08-05 16:32:35 [信息] 定时检查发现1个待释放手机号码，开始批量释放
2025-08-05 16:32:35 [信息] 开始释放1个手机号码
2025-08-05 16:32:35 [信息] [手机API] 开始批量释放1个手机号码
2025-08-05 16:32:35 [信息] [手机API] 释放手机号码: +528712405636
2025-08-05 16:32:36 [信息] [手机API] 手机号码释放成功: +528712405636
2025-08-05 16:32:36 [信息] [手机API] 批量释放完成: 成功1个, 失败0个
2025-08-05 16:32:36 [信息] 定时批量释放完成: 批量释放完成: 成功1个, 失败0个
2025-08-05 16:32:51 线程1：[信息] [信息] ✅ 找到'创建访问密钥'按钮，页面加载完成 (进度: 100%)
2025-08-05 16:32:51 线程1：[信息] [信息] ✅ 页面状态正常，找到创建密钥按钮，开始密钥提取流程 (进度: 100%)
2025-08-05 16:32:51 线程1：[信息] [信息] 🔍 设置页面缩放为50%... (进度: 100%)
2025-08-05 16:32:52 线程1：[信息] [信息] ✅ 页面缩放设置完成 (进度: 100%)
2025-08-05 16:32:52 [信息] 页面缩放设置为50%完成
2025-08-05 16:32:52 线程1：[信息] [信息] 🔍 正在查找并点击'下一步'按钮关闭向导... (进度: 100%)
2025-08-05 16:32:52 [信息] 开始查找并点击'下一步'按钮关闭向导
2025-08-05 16:32:52 线程1：[信息] [信息] ✅ 找到可见的下一步按钮，使用选择器: Locator(button[data-testid='awsc-nav-services-tooltip-confirm-button']) (进度: 100%)
2025-08-05 16:32:52 [信息] 找到可见的下一步按钮，使用选择器: Locator(button[data-testid='awsc-nav-services-tooltip-confirm-button'])
2025-08-05 16:32:53 线程1：[信息] [信息] ✅ 第一次点击'下一步'按钮完成 (选择器: Locator(button[data-testid='awsc-nav-services-tooltip-confirm-button'])) (进度: 100%)
2025-08-05 16:32:53 [信息] 第一次点击'下一步'按钮完成 (选择器: Locator(button[data-testid='awsc-nav-services-tooltip-confirm-button']))
2025-08-05 16:32:54 线程1：[信息] [信息] ℹ️ 第二次点击时按钮已消失，向导已关闭 (进度: 100%)
2025-08-05 16:32:54 [信息] 第二次点击时按钮已消失，向导已关闭
2025-08-05 16:32:54 线程1：[信息] [信息] ✅ '下一步'按钮点击流程完成 (进度: 100%)
2025-08-05 16:32:54 [信息] '下一步'按钮点击流程完成
2025-08-05 16:32:54 线程1：[信息] [信息] 🔑 第四阶段：开始创建和复制访问密钥 (进度: 100%)
2025-08-05 16:32:54 [信息] 开始创建和复制访问密钥
2025-08-05 16:32:54 线程1：[信息] [信息] 🔑 正在点击'创建访问密钥'按钮... (进度: 100%)
2025-08-05 16:32:54 线程1：[信息] [信息] 🔍 正在定位'创建访问密钥'按钮... (进度: 100%)
2025-08-05 16:32:54 线程1：[信息] [信息] ✅ 成功定位到'创建访问密钥'按钮 (进度: 100%)
2025-08-05 16:32:54 [信息] 成功定位到'创建访问密钥'按钮
2025-08-05 16:32:54 线程1：[信息] [信息] ✅ 成功点击'创建访问密钥'按钮 (进度: 100%)
2025-08-05 16:32:54 [信息] 成功点击'创建访问密钥'按钮
2025-08-05 16:32:56 线程1：[信息] [信息] ☑️ 正在勾选确认复选框... (进度: 100%)
2025-08-05 16:32:58 线程1：[信息] [信息] ✅ 使用id属性定位到确认复选框 (进度: 100%)
2025-08-05 16:32:58 [信息] 使用id属性定位到确认复选框
2025-08-05 16:32:58 线程1：[信息] [信息] ✅ 成功勾选确认复选框 (进度: 100%)
2025-08-05 16:32:58 [信息] 成功勾选确认复选框
2025-08-05 16:32:59 线程1：[信息] [信息] 🔑 正在点击'创建访问密钥'确认按钮... (进度: 100%)
2025-08-05 16:32:59 线程1：[信息] [信息] ✅ 成功点击'创建访问密钥'确认按钮 (进度: 100%)
2025-08-05 16:32:59 [信息] 成功点击'创建访问密钥'确认按钮
2025-08-05 16:33:02 线程1：[信息] [信息] 📋 开始复制访问密钥... (进度: 100%)
2025-08-05 16:33:02 [信息] 开始复制访问密钥
2025-08-05 16:33:04 线程1：[信息] [信息] 🔍 方法2找到 2 个单元格 (进度: 100%)
2025-08-05 16:33:04 [信息] 方法2找到 2 个单元格
2025-08-05 16:33:04 线程1：[信息] [信息] 🔍 总共找到 2 个单元格，开始分析... (进度: 100%)
2025-08-05 16:33:04 [信息] 总共找到 2 个单元格，开始分析
2025-08-05 16:33:04 [信息] 单元格[0]: '********************'
2025-08-05 16:33:04 [信息] 单元格[1]: '***************Mostrar'
2025-08-05 16:33:04 线程1：[信息] [信息] 📋 正在查找并复制访问密钥... (进度: 100%)
2025-08-05 16:33:04 线程1：[信息] [信息] 🔍 解决向导阻挡... (进度: 100%)
2025-08-05 16:33:04 线程1：[信息] [信息] 🔍 正在查找并点击'下一步'按钮关闭向导... (进度: 100%)
2025-08-05 16:33:04 [信息] 开始查找并点击'下一步'按钮关闭向导
2025-08-05 16:33:04 线程1：[信息] [信息] ℹ️ 未检测到向导元素，向导可能已自动关闭 (进度: 100%)
2025-08-05 16:33:04 [信息] 未检测到向导元素，向导可能已自动关闭
2025-08-05 16:33:05 线程1：[信息] [信息] ✅ 找到访问密钥: ******************** (进度: 100%)
2025-08-05 16:33:05 [信息] 找到访问密钥: ********************
2025-08-05 16:33:05 线程1：[信息] [信息] ✅ 方法1成功点击访问密钥复制按钮 (进度: 100%)
2025-08-05 16:33:05 [信息] 方法1成功点击访问密钥复制按钮
2025-08-05 16:33:06 线程1：[信息] [信息] 📋 正在查找并复制秘密访问密钥... (进度: 100%)
2025-08-05 16:33:06 线程1：[信息] [信息] ✅ 找到秘密访问密钥: ***************Mostrar (进度: 100%)
2025-08-05 16:33:06 [信息] 找到秘密访问密钥: ***************Mostrar
2025-08-05 16:33:06 线程1：[信息] [信息] 🔍 解决向导阻挡... (进度: 100%)
2025-08-05 16:33:06 线程1：[信息] [信息] 🔍 正在查找并点击'下一步'按钮关闭向导... (进度: 100%)
2025-08-05 16:33:06 [信息] 开始查找并点击'下一步'按钮关闭向导
2025-08-05 16:33:06 线程1：[信息] [信息] ℹ️ 未检测到向导元素，向导可能已自动关闭 (进度: 100%)
2025-08-05 16:33:06 [信息] 未检测到向导元素，向导可能已自动关闭
2025-08-05 16:33:06 线程1：[信息] [信息] 🔍 检测到隐藏的秘密访问密钥，尝试点击显示按钮... (进度: 100%)
2025-08-05 16:33:06 [信息] 检测到隐藏的秘密访问密钥，尝试点击显示按钮
2025-08-05 16:33:06 线程1：[信息] [信息] ✅ 使用TestId定位到显示按钮 (进度: 100%)
2025-08-05 16:33:06 [信息] 使用TestId定位到显示按钮
2025-08-05 16:33:07 线程1：[信息] [信息] ✅ 显示按钮点击成功，新文本: eWrx1J3eiNrGny7bSP38EUDtwVGeaK1ZXa7QuEnTOcultar (进度: 100%)
2025-08-05 16:33:07 [信息] 显示按钮点击成功，新文本: eWrx1J3eiNrGny7bSP38EUDtwVGeaK1ZXa7QuEnTOcultar
2025-08-05 16:33:07 线程1：[信息] [信息] ✅ 直接从显示文本提取秘密访问密钥: eWrx1J3eiNrGny7bSP38EUDtwVGeaK1ZXa7QuEnTOcultar (进度: 100%)
2025-08-05 16:33:07 [信息] 直接从显示文本提取秘密访问密钥: eWrx1J3eiNrGny7bSP38EUDtwVGeaK1ZXa7QuEnTOcultar
2025-08-05 16:33:07 线程1：[信息] [信息] ✅ 访问密钥复制完成 (进度: 100%)
2025-08-05 16:33:07 [信息] 访问密钥复制完成 - AccessKey: ********************, SecretKey: eWrx1J3eiNrGny7bSP38EUDtwVGeaK1ZXa7QuEnT
2025-08-05 16:33:08 线程1：[信息] [信息] 🖱️ 正在点击'已完成'按钮... (进度: 100%)
2025-08-05 16:33:10 线程1：[信息] [信息] ✅ 成功点击'已完成'按钮 (进度: 100%)
2025-08-05 16:33:10 [信息] 成功点击'已完成'按钮
2025-08-05 16:33:10 线程1：[信息] [信息] ✅ 密钥已保存到数据对象 - AccessKey: ********************, SecretKey: eWrx1J3eiNrGny7bSP38EUDtwVGeaK1ZXa7QuEnT (进度: 100%)
2025-08-05 16:33:10 [信息] 密钥已保存到数据对象 - AccessKey: ********************, SecretKey: eWrx1J3eiNrGny7bSP38EUDtwVGeaK1ZXa7QuEnT
2025-08-05 16:33:11 线程1：[信息] [信息] 🖱️ 正在点击'继续'按钮... (进度: 100%)
2025-08-05 16:33:14 线程1：[信息] [信息] ❌ 点击'继续'按钮失败: Timeout 3000ms exceeded.
Call log:
  - waiting for Locator("button[data-awsui-analytics*='navigate']") to be visible (进度: 100%)
2025-08-05 16:33:14 [信息] 点击'继续'按钮失败: Timeout 3000ms exceeded.
Call log:
  - waiting for Locator("button[data-awsui-analytics*='navigate']") to be visible
2025-08-05 16:33:14 线程1：[信息] [信息] 🔐 开始设置MFA设备... (进度: 100%)
2025-08-05 16:33:14 [信息] 开始设置MFA设备
2025-08-05 16:33:14 线程1：[信息] [信息] 🖱️ 正在点击'分配 MFA 设备'按钮... (进度: 100%)
2025-08-05 16:33:14 线程1：[信息] [信息] ✅ 成功点击'分配 MFA 设备'按钮 (进度: 100%)
2025-08-05 16:33:14 [信息] 成功点击'分配 MFA 设备'按钮
2025-08-05 16:33:14 线程1：[信息] [信息] ⏳ 等待MFA设备设置页面加载（20秒超时）... (进度: 100%)
2025-08-05 16:33:18 线程1：[信息] [信息] ✅ 通过第一个文本输入框找到设备名称输入框，页面加载完成 (进度: 100%)
2025-08-05 16:33:18 [信息] 通过第一个文本输入框找到设备名称输入框
2025-08-05 16:33:18 线程1：[信息] [信息] 📝 正在输入设备名称... (进度: 100%)
2025-08-05 16:33:19 线程1：[信息] [信息] ✅ 通过第一个文本输入框定位到设备名称输入框 (进度: 100%)
2025-08-05 16:33:19 线程1：[信息] [信息] ✅ 成功输入设备名称: joannkey (进度: 100%)
2025-08-05 16:33:19 [信息] 成功输入设备名称: joannkey
2025-08-05 16:33:19 线程1：[信息] [信息] ☑️ 正在选择'身份验证器应用程序'... (进度: 100%)
2025-08-05 16:33:20 线程1：[信息] [信息] ✅ 成功选择'身份验证器应用程序' (进度: 100%)
2025-08-05 16:33:20 [信息] 成功选择'身份验证器应用程序'
2025-08-05 16:33:20 线程1：[信息] [信息] 🖱️ 正在点击'下一步'按钮... (进度: 100%)
2025-08-05 16:33:20 线程1：[信息] [信息] ✅ 成功点击'下一步'按钮 (进度: 100%)
2025-08-05 16:33:20 [信息] 成功点击'下一步'按钮
2025-08-05 16:33:20 线程1：[信息] [信息] ⏳ 等待MFA密钥页面加载（20秒超时）... (进度: 100%)
2025-08-05 16:33:41 线程1：[信息] [信息] ⚠️ 20秒内未找到'显示密钥'按钮，需要手动处理 (进度: 100%)
2025-08-05 16:33:41 线程1：[信息] [信息] ⚠️ 显示密钥页面加载超时，需要手动处理 (进度: 100%)
2025-08-05 16:33:41 [信息] 显示密钥页面加载超时，需要手动处理
2025-08-05 16:33:41 线程1：[信息] [信息] MFA页面需要手动处理，等待用户操作 (进度: 100%)
2025-08-05 16:33:41 线程1：[信息] [信息] MFA页面需要手动处理，等待用户操作 (进度: 100%)
2025-08-05 16:34:54 [信息] 获取线程1当前数据: <EMAIL>
2025-08-05 16:34:54 线程1：[信息] 终止时正在处理的数据: <EMAIL>
2025-08-05 16:34:54 线程1：[信息] 数据详情: <EMAIL>|9rRdObSI|Valencia Almendra|SQM|Verna 912|Region Metropolitana|Padre Hurtado|9710000|5176085148651761|11|27|972|Valencia Almendra|3UMW00CfIt|CL
2025-08-05 16:34:54 线程1：[信息] 多线程终止 - 复制密钥信息到剪贴板
2025-08-05 16:34:54 [信息] 手动终止密钥检测 - _accessKey: '********************', _secretAccessKey: 'eWrx1J3eiNrGny7bSP38EUDtwVGeaK1ZXa7QuEnT', _currentData.AccessKey: '********************', _currentData.SecretAccessKey: 'eWrx1J3eiNrGny7bSP38EUDtwVGeaK1ZXa7QuEnT'
2025-08-05 16:34:54 线程1：[信息] [信息] 检测到密钥信息，复制完整注册数据 (进度: 100%)
2025-08-05 16:34:54 [信息] 手动终止 - 检测到密钥信息，复制完整注册数据
2025-08-05 16:34:54 线程1：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：3UMW00CfIt ③AWS密码：9rRdObSI ④访问密钥：******************** ⑤秘密访问密钥：eWrx1J3eiNrGny7bSP38EUDtwVGeaK1ZXa7QuEnT ⑥MFA信息：   //手动终止 (进度: 100%)
2025-08-05 16:34:54 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：3UMW00CfIt ③AWS密码：9rRdObSI ④访问密钥：******************** ⑤秘密访问密钥：eWrx1J3eiNrGny7bSP38EUDtwVGeaK1ZXa7QuEnT ⑥MFA信息：   //手动终止
2025-08-05 16:34:54 线程1：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：3UMW00CfIt ③AWS密码：9rRdObSI ④访问密钥：******************** ⑤秘密访问密钥：eWrx1J3eiNrGny7bSP38EUDtwVGeaK1ZXa7QuEnT ⑥MFA信息：   //手动终止
2025-08-05 16:34:54 [信息] 线程1请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：3UMW00CfIt ③AWS密码：9rRdObSI ④访问密钥：******************** ⑤秘密访问密钥：eWrx1J3eiNrGny7bSP38EUDtwVGeaK1ZXa7QuEnT ⑥MFA信息：   //手动终止
2025-08-05 16:34:54 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：3UMW00CfIt ③AWS密码：9rRdObSI ④访问密钥：******************** ⑤秘密访问密钥：eWrx1J3eiNrGny7bSP38EUDtwVGeaK1ZXa7QuEnT ⑥MFA信息：   //手动终止
2025-08-05 16:34:54 [信息] 线程1剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：3UMW00CfIt ③AWS密码：9rRdObSI ④访问密钥：******************** ⑤秘密访问密钥：eWrx1J3eiNrGny7bSP38EUDtwVGeaK1ZXa7QuEnT ⑥MFA信息：   //手动终止
2025-08-05 16:34:54 线程1：[信息] [信息] 注册已终止 (进度: 100%)
2025-08-05 16:34:54 线程1：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-08-05 16:34:54 线程1：[信息] [信息] 多线程模式 - 跳过重复数据复制 (进度: 100%)
2025-08-05 16:34:54 [信息] 多线程模式 - 跳过重复数据复制，数据已在CopyTerminatedRegistrationInfoAsync中处理
2025-08-05 16:34:54 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-08-05 16:34:54 线程1：[信息] 临时目录已清理: C:\Users\<USER>\AppData\Local\Temp\AWS_Thread_1_20250805_162835
2025-08-05 16:34:54 线程1：[信息] 已终止
2025-08-05 16:34:54 [信息] 线程1已终止
2025-08-05 16:34:54 [信息] 开始处理线程1终止数据，共1个数据
2025-08-05 16:34:54 [信息] 处理线程1终止数据: <EMAIL>
2025-08-05 16:34:54 [信息] 从注册数据列表中移除: <EMAIL>
2025-08-05 16:34:54 [信息] 线程1终止 - 数据已移动到终止列表: <EMAIL>
2025-08-05 16:34:54 [信息] 线程1终止数据处理完成，成功移动1个数据
2025-08-05 16:34:54 [信息] UniformGrid列数已更新为: 1
2025-08-05 16:34:54 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-08-05 16:34:54 [信息] 线程1已终止
2025-08-05 16:37:55 [信息] 多线程窗口引用已清理
2025-08-05 16:37:55 [信息] 主窗口已恢复正常状态，多线程窗口引用已清理
2025-08-05 16:37:55 [信息] 多线程管理窗口正在关闭
2025-08-05 16:37:56 [信息] 程序正在退出，开始清理工作...
2025-08-05 16:37:56 [信息] 程序退出，开始清理所有待拉黑千川手机号码
2025-08-05 16:37:56 [信息] 程序退出清理完成: 没有需要拉黑的千川手机号码
2025-08-05 16:37:56 [信息] 程序退出，开始清理所有待释放手机号码
2025-08-05 16:37:56 [信息] 程序退出清理完成: 没有需要释放的手机号码
2025-08-05 16:37:56 [信息] 程序退出清理工作完成
