2025-08-05 17:49:38 [按钮操作] 选择文件 -> 打开文件选择对话框
2025-08-05 17:49:40 [信息] 已选择文件: C:\Users\<USER>\Desktop\2025-08-05-智利.txt
2025-08-05 17:49:42 [信息] 线程数量已选择: 2
2025-08-05 17:49:42 [按钮操作] 加载信息 -> 加载数据文件: C:\Users\<USER>\Desktop\2025-08-05-智利.txt
2025-08-05 17:49:43 [信息] 成功加载 15 条数据
2025-08-05 17:49:45 [按钮操作] 开始注册 -> 启动注册流程
2025-08-05 17:49:45 [信息] 开始启动多线程注册，线程数量: 2
2025-08-05 17:49:45 [信息] 开始启动多线程注册，线程数量: 2，数据条数: 15
2025-08-05 17:49:45 [信息] 所有线程已停止并清理
2025-08-05 17:49:45 [信息] 正在初始化多线程服务...
2025-08-05 17:49:45 [信息] 榴莲手机API服务已初始化
2025-08-05 17:49:45 [信息] 手机号码管理器已初始化，服务商: Durian，将在第一个线程完成第二页后获取手机号码
2025-08-05 17:49:45 [信息] 多线程服务初始化完成
2025-08-05 17:49:45 [信息] 数据分配完成：共15条数据分配给2个线程
2025-08-05 17:49:45 [信息] 线程1分配到8条数据
2025-08-05 17:49:45 [信息] 线程2分配到7条数据
2025-08-05 17:49:45 [信息] 屏幕工作区域: 1280x672
2025-08-05 17:49:45 [信息] 线程1窗口布局: 位置(0, 0), 大小(384x310), 列1行1, 宽度30%, 当前列窗口数:2, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-05 17:49:45 [信息] 线程1获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-05 17:49:45 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-05 17:49:45 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_007, WebGL=ANGLE (Intel(R) Iris(R) Xe Graphics Direct3D11 vs_5_0 ps_5_0), CPU=32核, RAM=64 GB
2025-08-05 17:49:45 线程1：[信息] 已创建，窗口位置: (0, 0)
2025-08-05 17:49:45 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-05 17:49:45 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-05 17:49:45 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-05 17:49:45 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-05 17:49:45 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-05 17:49:45 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-05 17:49:45 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-05 17:49:45 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-05 17:49:45 [信息] 线程1已创建，窗口位置: (0, 0)，指纹: 国家=CL, 时区=America/Santiago
2025-08-05 17:49:45 [信息] 屏幕工作区域: 1280x672
2025-08-05 17:49:45 [信息] 线程2窗口布局: 位置(0, 329), 大小(384x310), 列1行2, 宽度30%, 当前列窗口数:2, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-05 17:49:45 [信息] 线程2获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-05 17:49:45 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-05 17:49:45 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_007, WebGL=ANGLE (NVIDIA GeForce RTX 3080 Direct3D11 vs_5_0 ps_5_0), CPU=8核, RAM=6 GB
2025-08-05 17:49:45 线程2：[信息] 已创建，窗口位置: (0, 329)
2025-08-05 17:49:45 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-05 17:49:45 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-05 17:49:45 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-05 17:49:45 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-05 17:49:45 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-05 17:49:45 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-05 17:49:45 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-05 17:49:45 [信息] 线程2已创建，窗口位置: (0, 329)，指纹: 国家=CL, 时区=America/Santiago
2025-08-05 17:49:45 [信息] 多线程注册启动成功，共2个线程
2025-08-05 17:49:45 线程1：[信息] 开始启动注册流程
2025-08-05 17:49:45 线程2：[信息] 开始启动注册流程
2025-08-05 17:49:45 线程1：[信息] 开始启动浏览器: 位置(0, 0), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0
2025-08-05 17:49:45 线程2：[信息] 开始启动浏览器: 位置(0, 329), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0
2025-08-05 17:49:45 线程2：[信息] 启动无痕Chrome浏览器...
2025-08-05 17:49:45 线程1：[信息] 启动无痕Chrome浏览器...
2025-08-05 17:49:45 线程2：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-05 17:49:45 线程1：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-05 17:49:45 [信息] 多线程管理窗口已初始化
2025-08-05 17:49:45 [信息] UniformGrid列数已更新为: 1
2025-08-05 17:49:45 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 0, 列数: 1
2025-08-05 17:49:45 [信息] 多线程管理窗口已打开
2025-08-05 17:49:45 [信息] 多线程注册启动成功，共2个线程
2025-08-05 17:49:47 [信息] UniformGrid列数已更新为: 1
2025-08-05 17:49:47 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 1, 列数: 1
2025-08-05 17:49:47 线程2：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-05 17:49:47 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-05 17:49:47 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36
2025-08-05 17:49:47 线程2：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-05 17:49:47 [信息] UniformGrid列数已更新为: 1
2025-08-05 17:49:47 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-08-05 17:49:47 线程1：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-05 17:49:47 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-05 17:49:47 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36
2025-08-05 17:49:47 线程1：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-05 17:49:48 线程2：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-05 17:49:48 线程1：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-05 17:49:50 线程2：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-05 17:49:50 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-05 17:49:50 线程2：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-05 17:49:50 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-05 17:49:50 线程2：[信息] [信息] 多线程模式使用指纹地理位置: -23.6509, -70.3975 (进度: 0%)
2025-08-05 17:49:50 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-23.6509, 经度=-70.3975
2025-08-05 17:49:50 线程2：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_007, CPU: 8核 (进度: 0%)
2025-08-05 17:49:50 [信息] 浏览器指纹注入: Canvas=canvas_fp_007, WebGL=ANGLE (NVIDIA GeForce RTX 3080 Direct3D11 vs_5_0 ps_5_0), CPU=8核, RAM=6 GB
2025-08-05 17:49:50 线程1：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-05 17:49:50 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-05 17:49:50 线程1：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-05 17:49:50 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-05 17:49:50 线程1：[信息] [信息] 多线程模式使用指纹地理位置: -33.4489, -70.6693 (进度: 0%)
2025-08-05 17:49:51 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-33.4489, 经度=-70.6693
2025-08-05 17:49:53 线程2：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-05 17:49:53 线程1：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_007, CPU: 32核 (进度: 0%)
2025-08-05 17:49:53 [信息] 浏览器指纹注入: Canvas=canvas_fp_007, WebGL=ANGLE (Intel(R) Iris(R) Xe Graphics Direct3D11 vs_5_0 ps_5_0), CPU=32核, RAM=64 GB
2025-08-05 17:49:54 线程1：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-05 17:49:55 线程2：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 8
   • 设备内存: 6 GB
   • 平台信息: Win32
   • Do Not Track: unspecified

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 3080 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (Intel Technologies)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 1E2F3A4B
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_008
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-E4F5G6H
   • MAC地址: BC-DE-F0-12-34-56
   • 屏幕分辨率: 2016x1029
   • 可用区域: 2016x989

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: A1B2C3D4
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: 4g
   • 电池API支持: True
   • 电池电量: 0.35
   • 电池充电状态: True
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-05 17:49:55 线程1：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 32
   • 设备内存: 64 GB
   • 平台信息: Win32
   • Do Not Track: unspecified

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (Intel(R) Iris(R) Xe Graphics Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (Intel Corp.)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 1A2B3C4D
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_005
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-O1P2Q3R
   • MAC地址: 9A-BC-DE-F0-12-34
   • 屏幕分辨率: 2044x1136
   • 可用区域: 2044x1096

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: E1F2A3B4
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: cellular
   • 电池API支持: True
   • 电池电量: 0.22
   • 电池充电状态: True
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-05 17:49:55 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 8    • 设备内存: 6 GB    • 平台信息: Win32    • Do Not Track: unspecified   图形渲染信息:    • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 3080 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (Intel Technologies)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 1E2F3A4B    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_008    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-E4F5G6H    • MAC地址: BC-DE-F0-12-34-56    • 屏幕分辨率: 2016x1029    • 可用区域: 2016x989   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: A1B2C3D4    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: 4g    • 电池API支持: True    • 电池电量: 0.35    • 电池充电状态: True    • 性能API支持: True  ================================
2025-08-05 17:49:55 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 32    • 设备内存: 64 GB    • 平台信息: Win32    • Do Not Track: unspecified   图形渲染信息:    • WebGL渲染器: ANGLE (Intel(R) Iris(R) Xe Graphics Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (Intel Corp.)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 1A2B3C4D    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_005    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-O1P2Q3R    • MAC地址: 9A-BC-DE-F0-12-34    • 屏幕分辨率: 2044x1136    • 可用区域: 2044x1096   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: E1F2A3B4    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: cellular    • 电池API支持: True    • 电池电量: 0.22    • 电池充电状态: True    • 性能API支持: True  ================================
2025-08-05 17:49:55 线程2：[信息] [信息] 已设置浏览器标题: 线程2 - AWS注册工具 (进度: 5%)
2025-08-05 17:49:55 线程1：[信息] [信息] 已设置浏览器标题: 线程1 - AWS注册工具 (进度: 5%)
2025-08-05 17:49:55 线程2：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-05 17:49:55 线程1：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-05 17:49:55 线程2：[信息] 浏览器启动成功
2025-08-05 17:49:55 线程1：[信息] 浏览器启动成功
2025-08-05 17:49:55 线程2：[信息] 获取下一个数据: <EMAIL>
2025-08-05 17:49:55 线程1：[信息] 获取下一个数据: <EMAIL>
2025-08-05 17:49:56 线程2：[信息] 开始处理账户: <EMAIL>
2025-08-05 17:49:56 线程1：[信息] 开始处理账户: <EMAIL>
2025-08-05 17:49:56 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-05 17:49:56 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-05 17:49:56 线程1：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-05 17:49:56 线程2：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-05 17:49:56 线程1：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-05 17:49:56 线程2：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-05 17:49:56 线程1：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-05 17:49:56 线程2：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-05 17:49:56 线程1：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-05 17:49:56 线程2：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-05 17:49:56 线程2：[信息] [信息] 已设置页面视口大小为384x310 (进度: 98%)
2025-08-05 17:49:56 线程1：[信息] [信息] 已设置页面视口大小为384x310 (进度: 98%)
2025-08-05 17:49:56 线程2：[信息] [信息] 已重新设置浏览器标题: 线程2 - AWS注册工具 (进度: 98%)
2025-08-05 17:49:56 线程2：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-05 17:49:56 线程1：[信息] [信息] 已重新设置浏览器标题: 线程1 - AWS注册工具 (进度: 98%)
2025-08-05 17:49:56 线程1：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-05 17:49:56 线程1：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-05 17:49:56 线程1：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-05 17:49:56 线程1：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-05 17:49:56 线程2：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-05 17:49:56 线程2：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-05 17:49:56 线程2：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-05 17:50:19 线程1：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程1 - AWS注册 (进度: 98%)
2025-08-05 17:50:19 线程1：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-05 17:50:19 线程1：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-05 17:50:19 线程1：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-05 17:50:19 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-05 17:50:19 线程2：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程2 - AWS注册 (进度: 98%)
2025-08-05 17:50:19 线程2：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-05 17:50:19 线程2：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-05 17:50:19 线程2：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-05 17:50:19 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-05 17:50:19 线程1：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-05 17:50:20 线程2：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-05 17:50:23 线程1：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-05 17:50:23 线程1：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-05 17:50:23 线程1：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-05 17:50:23 线程1：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-05 17:50:23 线程2：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-05 17:50:23 线程2：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-05 17:50:23 线程2：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-05 17:50:23 线程2：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-05 17:50:23 线程1：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-05 17:50:23 线程1：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-05 17:50:24 线程2：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-05 17:50:24 线程2：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-05 17:50:25 线程1：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-05 17:50:25 线程1：[信息] [信息] 🔍 第2次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-05 17:50:25 线程1：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 未发现验证码 (进度: 100%)
2025-08-05 17:50:25 线程1：[信息] [信息] ✅ 第一页未检测到图形验证码，继续流程 (进度: 100%)
2025-08-05 17:50:25 线程1：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-05 17:50:25 线程1：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-05 17:50:25 线程1：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-05 17:50:25 线程1：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-05 17:50:25 线程1：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-05 17:50:25 线程1：[信息] 账户注册流程已启动: <EMAIL>
2025-08-05 17:50:25 线程1：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-05 17:50:25 [信息] [线程1] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-05 17:50:25 [信息] [线程1] 等待2秒后开始第一次触发...
2025-08-05 17:50:26 线程2：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-05 17:50:26 线程2：[信息] [信息] 🔍 第2次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-05 17:50:26 线程2：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 未发现验证码 (进度: 100%)
2025-08-05 17:50:26 线程2：[信息] [信息] ✅ 第一页未检测到图形验证码，继续流程 (进度: 100%)
2025-08-05 17:50:26 线程2：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-05 17:50:26 线程2：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-05 17:50:26 线程2：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-05 17:50:26 线程2：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-05 17:50:26 线程2：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-05 17:50:26 [信息] [线程2] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-05 17:50:26 [信息] [线程2] 等待2秒后开始第一次触发...
2025-08-05 17:50:26 线程2：[信息] 账户注册流程已启动: <EMAIL>
2025-08-05 17:50:26 线程2：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-05 17:50:27 [信息] [线程1] 第1次触发邮箱验证码获取...（最多20次）
2025-08-05 17:50:27 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-05 17:50:27
2025-08-05 17:50:28 [信息] [线程2] 第1次触发邮箱验证码获取...（最多20次）
2025-08-05 17:50:28 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-05 17:50:28
2025-08-05 17:50:30 [信息] [线程1] 第2次触发邮箱验证码获取...（最多20次）
2025-08-05 17:50:30 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-05 17:50:30
2025-08-05 17:50:31 [信息] [线程2] 第2次触发邮箱验证码获取...（最多20次）
2025-08-05 17:50:31 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-05 17:50:31
2025-08-05 17:50:33 [信息] [线程1] 第3次触发邮箱验证码获取...（最多20次）
2025-08-05 17:50:33 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-05 17:50:33
2025-08-05 17:50:34 [信息] [线程1] 邮箱验证码获取成功: 036381，立即停止重复请求
2025-08-05 17:50:34 [信息] [线程1] 已清理请求文件，停止重复触发
2025-08-05 17:50:34 [信息] [线程1] 已清理响应文件
2025-08-05 17:50:34 线程1：[信息] [信息] 验证码获取成功: 036381，正在自动填入... (进度: 25%)
2025-08-05 17:50:34 线程1：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 25%)
2025-08-05 17:50:34 线程1：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 25%)
2025-08-05 17:50:34 线程1：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 25%)
2025-08-05 17:50:34 [信息] [线程2] 第3次触发邮箱验证码获取...（最多20次）
2025-08-05 17:50:34 [信息] 线程1完成第二页事件已处理
2025-08-05 17:50:34 [信息] 线程1完成第二页，开始批量获取手机号码...
2025-08-05 17:50:34 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-05 17:50:34
2025-08-05 17:50:34 线程1：[信息] [信息] 线程1第二页验证完成，已通知管理器 (进度: 35%)
2025-08-05 17:50:34 [信息] 开始批量获取2个手机号码，服务商: Durian
2025-08-05 17:50:34 [信息] [手机API] 批量获取2个手机号码，URL: https://api.durianrcs.com/out/ext_api/getMobile?name=oneone&ApiKey=NnlaR01xQm9hMlAwRnJDSVB2SG1kQT09&cuy=mx&pid=0209&num=2&noblack=0&serial=2&secret_key=null&vip=null
2025-08-05 17:50:35 [信息] [手机API] 批量获取响应内容: {"code":200,"msg":"Success","data":["+524775908188","+527753566266"]}
2025-08-05 17:50:35 [信息] [手机API] 检测到数组格式，元素数量: 2
2025-08-05 17:50:35 [信息] [手机API] 批量获取成功，获得2个手机号码
2025-08-05 17:50:35 [信息] 线程1分配榴莲手机号码: +524775908188
2025-08-05 17:50:35 [信息] 线程2分配榴莲手机号码: +527753566266
2025-08-05 17:50:35 [信息] 榴莲API批量获取手机号码成功，已分配给2个线程
2025-08-05 17:50:35 [信息] 批量获取2个手机号码成功
2025-08-05 17:50:35 [信息] [线程2] 邮箱验证码获取成功: 534044，立即停止重复请求
2025-08-05 17:50:35 [信息] [线程2] 已清理请求文件，停止重复触发
2025-08-05 17:50:35 [信息] [线程2] 已清理响应文件
2025-08-05 17:50:35 线程2：[信息] [信息] 验证码获取成功: 534044，正在自动填入... (进度: 25%)
2025-08-05 17:50:35 线程2：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 25%)
2025-08-05 17:50:35 线程2：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 25%)
2025-08-05 17:50:36 线程2：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 25%)
2025-08-05 17:50:36 [信息] 线程2完成第二页事件已处理
2025-08-05 17:50:36 [信息] 线程2完成第二页，手机号码已获取，无需重复获取
2025-08-05 17:50:36 线程2：[信息] [信息] 线程2第二页验证完成，已通知管理器 (进度: 35%)
2025-08-05 17:50:37 线程1：[信息] [信息] 等待密码设置页面加载... (进度: 38%)
2025-08-05 17:50:37 线程1：[信息] [信息] 开始填写密码信息... (进度: 38%)
2025-08-05 17:50:37 线程1：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-05 17:50:37 线程1：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-05 17:50:37 线程1：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 38%)
2025-08-05 17:50:38 线程1：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 38%)
2025-08-05 17:50:39 线程2：[信息] [信息] 等待密码设置页面加载... (进度: 38%)
2025-08-05 17:50:39 线程2：[信息] [信息] 开始填写密码信息... (进度: 38%)
2025-08-05 17:50:39 线程2：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-05 17:50:39 线程2：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-05 17:50:39 线程2：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 38%)
2025-08-05 17:50:40 线程2：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 38%)
2025-08-05 17:50:41 线程1：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 38%)
2025-08-05 17:50:41 线程1：[信息] [信息] 等待账户类型确认页面加载... (进度: 38%)
2025-08-05 17:50:41 线程1：[信息] [信息] 开始处理账户类型确认... (进度: 38%)
2025-08-05 17:50:43 线程2：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 38%)
2025-08-05 17:50:43 线程2：[信息] [信息] 等待账户类型确认页面加载... (进度: 38%)
2025-08-05 17:50:43 线程2：[信息] [信息] 开始处理账户类型确认... (进度: 38%)
2025-08-05 17:50:46 线程1：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 38%)
2025-08-05 17:50:46 线程1：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 38%)
2025-08-05 17:50:47 线程2：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 38%)
2025-08-05 17:50:47 线程2：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 38%)
2025-08-05 17:51:05 线程2：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 38%)
2025-08-05 17:51:05 [信息] 线程2获取已分配的榴莲手机号码: +527753566266
2025-08-05 17:51:05 线程2：[信息] [信息] 多线程模式：使用已分配的手机号码 +527753566266 (进度: 38%)
2025-08-05 17:51:06 线程1：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 38%)
2025-08-05 17:51:06 [信息] 线程1获取已分配的榴莲手机号码: +524775908188
2025-08-05 17:51:06 线程1：[信息] [信息] 多线程模式：使用已分配的手机号码 +524775908188 (进度: 38%)
2025-08-05 17:51:08 线程1：[信息] [信息] 数据国家代码为CL，需要选择Chile (进度: 38%)
2025-08-05 17:51:08 线程2：[信息] [信息] 数据国家代码为CL，需要选择Chile (进度: 38%)
2025-08-05 17:51:08 线程1：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 38%)
2025-08-05 17:51:08 线程2：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 38%)
2025-08-05 17:51:10 线程1：[信息] [信息] 已选择国家: Chile (进度: 38%)
2025-08-05 17:51:10 线程1：[信息] [信息] 已成功选择国家: Chile (进度: 38%)
2025-08-05 17:51:10 线程1：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 38%)
2025-08-05 17:51:10 线程2：[信息] [信息] 已选择国家: Chile (进度: 38%)
2025-08-05 17:51:10 线程2：[信息] [信息] 已成功选择国家: Chile (进度: 38%)
2025-08-05 17:51:10 线程2：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 38%)
2025-08-05 17:51:11 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-08-05 17:51:11 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-08-05 17:51:17 线程1：[信息] [信息] 已选择国家代码 +52 (进度: 38%)
2025-08-05 17:51:17 线程2：[信息] [信息] 已选择国家代码 +52 (进度: 38%)
2025-08-05 17:51:18 线程2：[信息] [信息] 等待后台获取的手机号码结果... (进度: 38%)
2025-08-05 17:51:18 线程1：[信息] [信息] 等待后台获取的手机号码结果... (进度: 38%)
2025-08-05 17:51:18 线程2：[信息] [信息] 已自动获取并填入手机号码: +527753566266 (进度: 38%)
2025-08-05 17:51:18 线程1：[信息] [信息] 已自动获取并填入手机号码: +524775908188 (进度: 38%)
2025-08-05 17:51:19 线程2：[信息] [信息] 使用已获取的手机号码: +527753566266（保存本地号码: +527753566266） (进度: 38%)
2025-08-05 17:51:19 线程1：[信息] [信息] 使用已获取的手机号码: +524775908188（保存本地号码: +524775908188） (进度: 38%)
2025-08-05 17:51:19 线程2：[信息] [信息] 联系信息完成，等待页面加载... (进度: 38%)
2025-08-05 17:51:19 线程1：[信息] [信息] 联系信息完成，等待页面加载... (进度: 38%)
2025-08-05 17:51:22 线程1：[信息] [信息] 进入付款信息页面... (进度: 38%)
2025-08-05 17:51:23 线程2：[信息] [信息] 进入付款信息页面... (进度: 38%)
2025-08-05 17:51:23 线程1：[信息] [信息] 正在选择月份: June (进度: 38%)
2025-08-05 17:51:23 线程1：[信息] [信息] 已选择月份（标准选项）: June (进度: 38%)
2025-08-05 17:51:24 线程1：[信息] [信息] 正在选择年份: 2029 (进度: 38%)
2025-08-05 17:51:25 线程2：[信息] [信息] 正在选择月份: August (进度: 38%)
2025-08-05 17:51:25 线程1：[信息] [信息] 已选择年份（标准选项）: 2029 (进度: 38%)
2025-08-05 17:51:25 线程2：[信息] [信息] 已选择月份（标准选项）: August (进度: 38%)
2025-08-05 17:51:26 线程1：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 38%)
2025-08-05 17:51:26 线程1：[信息] [信息] 开始填写验证码验证页面... (进度: 38%)
2025-08-05 17:51:26 线程1：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 38%)
2025-08-05 17:51:26 线程2：[信息] [信息] 正在选择年份: 2026 (进度: 38%)
2025-08-05 17:51:26 线程2：[信息] [信息] 已选择年份（标准选项）: 2026 (进度: 38%)
2025-08-05 17:51:27 线程2：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 38%)
2025-08-05 17:51:27 线程2：[信息] [信息] 开始填写验证码验证页面... (进度: 38%)
2025-08-05 17:51:27 线程2：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 38%)
2025-08-05 17:51:31 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-08-05 17:51:32 线程1：[信息] [信息] 已选择国家代码: +52 (进度: 38%)
2025-08-05 17:51:32 线程1：[信息] [信息] 已清空并重新填写手机号码: +524775908188 (进度: 38%)
2025-08-05 17:51:32 线程1：[信息] [信息] 已点击发送验证码按钮 (进度: 38%)
2025-08-05 17:51:33 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-08-05 17:51:34 线程1：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 38%)
2025-08-05 17:51:34 线程1：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-08-05 17:51:34 线程1：[信息] [信息] 手机号码自动模式 + 图形验证码自动模式：开始自动处理... (进度: 100%)
2025-08-05 17:51:34 线程1：[信息] [信息] 自动模式：开始处理图形验证码... (进度: 100%)
2025-08-05 17:51:34 线程1：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-05 17:51:34 线程2：[信息] [信息] 已选择国家代码: +52 (进度: 38%)
2025-08-05 17:51:34 线程2：[信息] [信息] 已清空并重新填写手机号码: +527753566266 (进度: 38%)
2025-08-05 17:51:34 线程2：[信息] [信息] 已点击发送验证码按钮 (进度: 38%)
2025-08-05 17:51:36 线程2：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 38%)
2025-08-05 17:51:36 线程2：[信息] [信息] ⚠️ 检测到错误信息，开始重试机制... (进度: 38%)
2025-08-05 17:51:36 [信息] 检测到错误信息，开始重试机制
2025-08-05 17:51:36 线程2：[信息] [信息] 🔄 第1次重试发送验证码按钮... (进度: 38%)
2025-08-05 17:51:36 [信息] 第1次重试发送验证码按钮
2025-08-05 17:51:37 线程1：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-05 17:51:37 线程1：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-05 17:51:40 线程2：[信息] [信息] ✅ 第1次重试：已点击发送验证码按钮 (进度: 100%)
2025-08-05 17:51:40 [信息] 第1次重试：已点击发送验证码按钮
2025-08-05 17:51:43 线程1：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 34803 字节 (进度: 100%)
2025-08-05 17:51:44 线程1：[信息] [信息] ✅ 图片验证通过：201x71px，34803字节，复杂度符合要求 (进度: 100%)
2025-08-05 17:51:44 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-05 17:51:44 线程2：[信息] [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']" (进度: 100%)
2025-08-05 17:51:44 [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']"
2025-08-05 17:51:44 线程2：[信息] [信息] ❌ 第1次重试失败：错误信息仍然存在 (进度: 100%)
2025-08-05 17:51:44 [信息] 第1次重试失败：错误信息仍然存在
2025-08-05 17:51:44 线程2：[信息] [信息] 🔄 第2次重试发送验证码按钮... (进度: 100%)
2025-08-05 17:51:44 [信息] 第2次重试发送验证码按钮
2025-08-05 17:51:45 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"7gwx3n"},"taskId":"cc8e3714-71e1-11f0-aaaf-6e28fd6820d3"} (进度: 100%)
2025-08-05 17:51:45 线程1：[信息] [信息] 第六页第1次识别结果: 7gwx3n → 转换为小写: 7gwx3n (进度: 100%)
2025-08-05 17:51:45 线程1：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-05 17:51:45 线程1：[信息] [信息] 第六页已填入验证码: 7gwx3n (进度: 100%)
2025-08-05 17:51:45 线程1：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-05 17:51:47 线程2：[信息] [信息] ✅ 第2次重试：已点击发送验证码按钮 (进度: 100%)
2025-08-05 17:51:47 [信息] 第2次重试：已点击发送验证码按钮
2025-08-05 17:51:48 线程1：[信息] [信息] 第1次图形验证码识别成功 (进度: 100%)
2025-08-05 17:51:48 线程1：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-08-05 17:51:49 线程2：[信息] [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']" (进度: 100%)
2025-08-05 17:51:49 [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']"
2025-08-05 17:51:49 线程2：[信息] [信息] ❌ 第2次重试失败：错误信息仍然存在 (进度: 100%)
2025-08-05 17:51:49 [信息] 第2次重试失败：错误信息仍然存在
2025-08-05 17:51:49 线程2：[信息] [信息] 🔄 第3次重试发送验证码按钮... (进度: 100%)
2025-08-05 17:51:49 [信息] 第3次重试发送验证码按钮
2025-08-05 17:51:51 线程1：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-08-05 17:51:52 线程2：[信息] [信息] ✅ 第3次重试：已点击发送验证码按钮 (进度: 100%)
2025-08-05 17:51:52 [信息] 第3次重试：已点击发送验证码按钮
2025-08-05 17:51:54 线程1：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-05 17:51:54 线程1：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-05 17:51:54 线程1：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-05 17:51:54 线程1：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-08-05 17:51:54 线程2：[信息] [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']" (进度: 100%)
2025-08-05 17:51:54 [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']"
2025-08-05 17:51:54 线程2：[信息] [信息] ❌ 第3次重试失败：错误信息仍然存在 (进度: 100%)
2025-08-05 17:51:54 [信息] 第3次重试失败：错误信息仍然存在
2025-08-05 17:51:54 线程2：[信息] [信息] 🔄 第4次重试发送验证码按钮... (进度: 100%)
2025-08-05 17:51:54 [信息] 第4次重试发送验证码按钮
2025-08-05 17:51:57 线程2：[信息] [信息] ✅ 第4次重试：已点击发送验证码按钮 (进度: 100%)
2025-08-05 17:51:57 [信息] 第4次重试：已点击发送验证码按钮
2025-08-05 17:51:59 线程1：[信息] [信息] 线程1开始独立获取验证码... (进度: 100%)
2025-08-05 17:51:59 线程1：[信息] [信息] 线程1开始获取验证码，最多尝试8次，每次间隔8秒 (进度: 100%)
2025-08-05 17:51:59 线程1：[信息] [信息] 线程1第1次尝试获取验证码...（剩余7次尝试） (进度: 100%)
2025-08-05 17:51:59 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-05 17:52:00 线程2：[信息] [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']" (进度: 100%)
2025-08-05 17:52:00 [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']"
2025-08-05 17:52:00 线程2：[信息] [信息] ❌ 第4次重试失败：错误信息仍然存在 (进度: 100%)
2025-08-05 17:52:00 [信息] 第4次重试失败：错误信息仍然存在
2025-08-05 17:52:00 线程2：[信息] [信息] ❌ 4次重试均失败，确认为验证手机区号错误 (进度: 100%)
2025-08-05 17:52:00 [信息] 4次重试均失败，确认为验证手机区号错误
2025-08-05 17:52:00 线程2：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-08-05 17:52:00 线程2：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：5qT4787Xb6 ③AWS密码：66uGToDJ ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //验证手机出现区号 (进度: 100%)
2025-08-05 17:52:00 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：5qT4787Xb6 ③AWS密码：66uGToDJ ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //验证手机出现区号
2025-08-05 17:52:00 线程2：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：5qT4787Xb6 ③AWS密码：66uGToDJ ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //验证手机出现区号
2025-08-05 17:52:00 [信息] 线程2请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：5qT4787Xb6 ③AWS密码：66uGToDJ ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //验证手机出现区号
2025-08-05 17:52:00 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：5qT4787Xb6 ③AWS密码：66uGToDJ ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //验证手机出现区号
2025-08-05 17:52:00 [信息] 线程2剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：5qT4787Xb6 ③AWS密码：66uGToDJ ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //验证手机出现区号
2025-08-05 17:52:00 线程2：[信息] 收到失败数据保存请求: 验证手机出现区号, 数据: <EMAIL>
2025-08-05 17:52:00 [信息] 线程2请求保存失败数据: 验证手机出现区号, 数据: <EMAIL>
2025-08-05 17:52:00 [信息] 已处理失败数据: <EMAIL>, 失败原因: 验证手机出现区号
2025-08-05 17:52:00 [信息] 线程2失败数据已保存: 验证手机出现区号, 数据: <EMAIL>
2025-08-05 17:52:00 线程2：[信息] [信息] 已通知保存失败数据，失败原因: 验证手机出现区号 (进度: 0%)
2025-08-05 17:52:00 线程2：[信息] [信息] ❌ 注册失败，验证手机出现区号 (进度: 0%)
2025-08-05 17:52:00 [信息] 多线程模式：注册失败，验证手机出现区号
2025-08-05 17:52:00 线程2：[信息] [信息] 检测到验证手机区号错误，注册已终止 (进度: 0%)
2025-08-05 17:52:00 线程1：[信息] [信息] 线程1验证码获取成功: 1224 (进度: 100%)
2025-08-05 17:52:00 [信息] 线程1手机号码已加入释放队列: +524775908188 (原因: 获取验证码成功)
2025-08-05 17:52:00 线程1：[信息] [信息] 线程1验证码获取成功: 1224，立即填入验证码... (进度: 100%)
2025-08-05 17:52:00 线程1：[信息] [信息] 第七页：找到验证码输入框 (Verify code) (进度: 100%)
2025-08-05 17:52:00 线程1：[信息] [信息] 线程1已自动填入手机验证码: 1224 (进度: 100%)
2025-08-05 17:52:01 线程1：[信息] [信息] 线程1正在自动点击Continue按钮... (进度: 100%)
2025-08-05 17:52:01 线程1：[信息] [信息] 线程1手机验证码验证完成，继续执行后续步骤... (进度: 100%)
2025-08-05 17:52:05 线程1：[信息] [信息] 验证码验证完成，等待页面加载... (进度: 100%)
2025-08-05 17:52:05 线程1：[信息] [信息] 已点击完成注册按钮，正在处理... (进度: 100%)
2025-08-05 17:52:05 线程1：[信息] [信息] 自动模式验证完成，API手机号码已加入黑名单: 加入黑名单成功 (进度: 100%)
2025-08-05 17:52:06 线程1：[信息] [信息] 等待完成注册页面加载... (进度: 100%)
2025-08-05 17:52:09 线程1：[信息] [信息] 完成注册页面加载完成 (进度: 100%)
2025-08-05 17:52:09 线程1：[信息] [信息] 🔍 正在查找'Go to the AWS Management Console'链接... (进度: 100%)
2025-08-05 17:52:13 线程1：[信息] [信息] 🖱️ 已点击跳转管理控制台链接 (进度: 100%)
2025-08-05 17:52:13 线程1：[信息] [信息] 🔗 通过导航进入IAM安全凭证页面... (进度: 100%)
2025-08-05 17:52:13 线程1：[信息] [信息] ⏳ 等待管理控制台页面加载，智能检测'更多'按钮（超时20秒）... (进度: 100%)
2025-08-05 17:52:15 [信息] 定时检查发现1个待释放手机号码，开始批量释放
2025-08-05 17:52:15 [信息] 开始释放1个手机号码
2025-08-05 17:52:15 [信息] [手机API] 开始批量释放1个手机号码
2025-08-05 17:52:15 [信息] [手机API] 释放手机号码: +524775908188
2025-08-05 17:52:15 [信息] [手机API] 手机号码释放成功: +524775908188
2025-08-05 17:52:16 [信息] [手机API] 批量释放完成: 成功1个, 失败0个
2025-08-05 17:52:16 [信息] 定时批量释放完成: 批量释放完成: 成功1个, 失败0个
2025-08-05 17:52:25 线程1：[信息] [信息] ✅ 找到'更多'按钮，页面加载完成 (进度: 100%)
2025-08-05 17:52:25 线程1：[信息] [信息] 🖱️ 正在点击更多按钮... (进度: 100%)
2025-08-05 17:52:25 线程1：[信息] [信息] ✅ 成功点击更多按钮 (进度: 100%)
2025-08-05 17:52:25 [信息] 成功点击更多按钮
2025-08-05 17:52:26 线程1：[信息] [信息] 🖱️ 正在点击账户信息按钮... (进度: 100%)
2025-08-05 17:52:26 线程1：[信息] [信息] ✅ 成功点击账户信息按钮 (进度: 100%)
2025-08-05 17:52:26 [信息] 成功点击账户信息按钮
2025-08-05 17:52:27 线程1：[信息] [信息] 🖱️ 正在点击'安全凭证'菜单项... (进度: 100%)
2025-08-05 17:52:27 线程1：[信息] [信息] 🔍 正在定位'安全凭证'链接... (进度: 100%)
2025-08-05 17:52:27 线程1：[信息] [信息] ✅ 成功定位到'安全凭证'链接 (进度: 100%)
2025-08-05 17:52:27 [信息] 成功定位到'安全凭证'链接
2025-08-05 17:52:34 线程1：[信息] [信息] ✅ 成功点击'安全凭证'菜单项 (进度: 100%)
2025-08-05 17:52:34 [信息] 成功点击'安全凭证'链接
2025-08-05 17:52:34 线程1：[信息] [信息] ⏳ 等待IAM安全凭证页面加载（20秒超时，循环检测账单问题和创建密钥按钮）... (进度: 100%)
2025-08-05 17:52:38 线程1：[信息] [信息] ❌ 检测到账单问题页面跳转 (进度: 100%)
2025-08-05 17:52:38 线程1：[信息] [信息] ❌ 检测到账单问题，处理账单问题流程 (进度: 100%)
2025-08-05 17:52:38 线程1：[信息] [信息] ⚠️ 处理账单问题，终止注册 (进度: 100%)
2025-08-05 17:52:38 [信息] 检测到账单问题，开始处理
2025-08-05 17:52:38 线程1：[信息] [信息] 账单问题注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：33R86948wc37 ③AWS密码：zT8hIeuo ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单 (进度: 100%)
2025-08-05 17:52:38 [信息] 账单问题注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：33R86948wc37 ③AWS密码：zT8hIeuo ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单
2025-08-05 17:52:38 线程1：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：33R86948wc37 ③AWS密码：zT8hIeuo ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单
2025-08-05 17:52:38 [信息] 线程1请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：33R86948wc37 ③AWS密码：zT8hIeuo ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单
2025-08-05 17:52:38 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：33R86948wc37 ③AWS密码：zT8hIeuo ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单
2025-08-05 17:52:38 [信息] 线程1剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：33R86948wc37 ③AWS密码：zT8hIeuo ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单
2025-08-05 17:52:38 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-05 17:52:38 [信息] 多线程状态已重置
2025-08-05 17:52:38 线程1：[信息] 账户注册完成(账单问题)，等待后续操作: <EMAIL>
2025-08-05 17:52:38 线程1：[信息] [信息] ✅ 注册成功，账户提示账单 (进度: 100%)
2025-08-05 17:52:38 [信息] 注册完成 - 账单提示处理
2025-08-05 17:52:38 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-08-05 17:52:39 [信息] 开始处理账单问题数据完成事件: <EMAIL>
2025-08-05 17:52:39 [信息] 已将账单问题数据移动到注册成功区域: <EMAIL>
2025-08-05 17:52:39 [信息] 已完成账单问题数据移除: <EMAIL>
2025-08-05 17:52:39 [信息] 账单问题数据完成事件处理完毕: <EMAIL>
2025-08-05 17:52:39 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-05 17:52:39 [信息] 多线程状态已重置
2025-08-05 17:52:39 线程1：[信息] 最终完成: 注册完成，账单提示无法提取密钥: <EMAIL> (类型: WithBillingIssue)
2025-08-05 17:52:39 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-05 17:52:39 [信息] 多线程状态已重置
2025-08-05 17:52:39 [信息] 线程1数据完成事件已通知主窗口并最终完成: <EMAIL>
2025-08-05 17:53:04 [信息] 多线程窗口引用已清理
2025-08-05 17:53:04 [信息] 主窗口已恢复正常状态，多线程窗口引用已清理
2025-08-05 17:53:04 [信息] 多线程管理窗口正在关闭
2025-08-05 17:53:07 [信息] 程序正在退出，开始清理工作...
2025-08-05 17:53:07 [信息] 程序退出，开始清理所有待拉黑千川手机号码
2025-08-05 17:53:07 [信息] 程序退出清理完成: 没有需要拉黑的千川手机号码
2025-08-05 17:53:07 [信息] 程序退出，开始清理所有待释放手机号码
2025-08-05 17:53:07 [信息] 程序退出清理完成: 没有需要释放的手机号码
2025-08-05 17:53:07 [信息] 程序退出清理工作完成
2025-08-05 17:53:21 [信息] AWS自动注册工具启动
2025-08-05 17:53:21 [信息] 程序版本: 1.0.0.0
2025-08-05 17:53:21 [信息] 启动时间: 2025-08-05 17:53:21
2025-08-05 17:53:21 [信息] 浏览器模式切换: 无痕 Chrome 模式
2025-08-05 17:53:21 [信息] 线程数量已选择: 1
2025-08-05 17:53:21 [信息] 线程数量选择初始化完成
2025-08-05 17:53:21 [信息] 程序初始化完成
2025-08-05 17:53:23 [按钮操作] 选择文件 -> 打开文件选择对话框
2025-08-05 17:53:25 [信息] 已选择文件: C:\Users\<USER>\Desktop\2025-08-05-智利.txt
2025-08-05 17:53:25 [按钮操作] 加载信息 -> 加载数据文件: C:\Users\<USER>\Desktop\2025-08-05-智利.txt
2025-08-05 17:53:25 [信息] 成功加载 13 条数据
2025-08-05 17:53:26 [按钮操作] 加载信息 -> 加载数据文件: C:\Users\<USER>\Desktop\2025-08-05-智利.txt
2025-08-05 17:53:26 [信息] 成功加载 13 条数据
2025-08-05 17:53:27 [信息] 线程数量已选择: 2
2025-08-05 17:53:30 [按钮操作] 开始注册 -> 启动注册流程
2025-08-05 17:53:30 [信息] 开始启动多线程注册，线程数量: 2
2025-08-05 17:53:30 [信息] 开始启动多线程注册，线程数量: 2，数据条数: 13
2025-08-05 17:53:30 [信息] 所有线程已停止并清理
2025-08-05 17:53:30 [信息] 正在初始化多线程服务...
2025-08-05 17:53:30 [信息] 千川手机API服务已初始化
2025-08-05 17:53:30 [信息] 手机号码管理器已初始化，服务商: Qianchuan，将在第一个线程完成第二页后获取手机号码
2025-08-05 17:53:30 [信息] 多线程服务初始化完成
2025-08-05 17:53:30 [信息] 数据分配完成：共13条数据分配给2个线程
2025-08-05 17:53:30 [信息] 线程1分配到7条数据
2025-08-05 17:53:30 [信息] 线程2分配到6条数据
2025-08-05 17:53:30 [信息] 屏幕工作区域: 1280x672
2025-08-05 17:53:30 [信息] 线程1窗口布局: 位置(0, 0), 大小(384x310), 列1行1, 宽度30%, 当前列窗口数:2, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-05 17:53:30 [信息] 线程1获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-05 17:53:30 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-05 17:53:30 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_004, WebGL=ANGLE (NVIDIA GeForce RTX 4060 Direct3D11 vs_5_0 ps_5_0), CPU=32核, RAM=32 GB
2025-08-05 17:53:30 线程1：[信息] 已创建，窗口位置: (0, 0)
2025-08-05 17:53:30 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-05 17:53:30 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-05 17:53:30 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-05 17:53:30 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-05 17:53:30 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-05 17:53:30 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-05 17:53:30 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-05 17:53:30 [信息] 线程1已创建，窗口位置: (0, 0)，指纹: 国家=CL, 时区=America/Santiago
2025-08-05 17:53:30 [信息] 屏幕工作区域: 1280x672
2025-08-05 17:53:30 [信息] 线程2窗口布局: 位置(0, 329), 大小(384x310), 列1行2, 宽度30%, 当前列窗口数:2, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-05 17:53:30 [信息] 线程2获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-05 17:53:30 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-05 17:53:30 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_008, WebGL=ANGLE (Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0), CPU=8核, RAM=32 GB
2025-08-05 17:53:30 线程2：[信息] 已创建，窗口位置: (0, 329)
2025-08-05 17:53:30 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-05 17:53:30 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-05 17:53:30 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-05 17:53:30 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-05 17:53:30 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-05 17:53:30 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-05 17:53:30 [信息] 线程2已创建，窗口位置: (0, 329)，指纹: 国家=CL, 时区=America/Santiago
2025-08-05 17:53:30 [信息] 多线程注册启动成功，共2个线程
2025-08-05 17:53:30 线程2：[信息] 开始启动注册流程
2025-08-05 17:53:30 线程1：[信息] 开始启动注册流程
2025-08-05 17:53:30 线程2：[信息] 开始启动浏览器: 位置(0, 329), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36
2025-08-05 17:53:30 线程1：[信息] 开始启动浏览器: 位置(0, 0), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36
2025-08-05 17:53:30 线程1：[信息] 启动无痕Chrome浏览器...
2025-08-05 17:53:30 线程2：[信息] 启动无痕Chrome浏览器...
2025-08-05 17:53:30 线程1：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-05 17:53:30 线程2：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-05 17:53:30 [信息] 多线程管理窗口已初始化
2025-08-05 17:53:30 [信息] UniformGrid列数已更新为: 1
2025-08-05 17:53:30 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 0, 列数: 1
2025-08-05 17:53:30 [信息] 多线程管理窗口已打开
2025-08-05 17:53:30 [信息] 多线程注册启动成功，共2个线程
2025-08-05 17:53:31 [信息] UniformGrid列数已更新为: 1
2025-08-05 17:53:31 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 1, 列数: 1
2025-08-05 17:53:31 线程1：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-05 17:53:31 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-05 17:53:31 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0
2025-08-05 17:53:31 线程1：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-05 17:53:31 [信息] UniformGrid列数已更新为: 1
2025-08-05 17:53:31 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-08-05 17:53:31 线程2：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-05 17:53:31 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-05 17:53:31 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0) Gecko/20100101 Firefox/119.0
2025-08-05 17:53:31 线程2：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-05 17:53:32 线程2：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-05 17:53:32 线程1：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-05 17:53:34 线程2：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-05 17:53:34 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-05 17:53:34 线程2：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-05 17:53:34 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-05 17:53:34 线程2：[信息] [信息] 多线程模式使用指纹地理位置: -29.9027, -71.2519 (进度: 0%)
2025-08-05 17:53:34 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-29.9027, 经度=-71.2519
2025-08-05 17:53:34 线程2：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_008, CPU: 8核 (进度: 0%)
2025-08-05 17:53:34 [信息] 浏览器指纹注入: Canvas=canvas_fp_008, WebGL=ANGLE (Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0), CPU=8核, RAM=32 GB
2025-08-05 17:53:34 线程1：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-05 17:53:34 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-05 17:53:34 线程1：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-05 17:53:34 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-05 17:53:34 线程1：[信息] [信息] 多线程模式使用指纹地理位置: -23.6509, -70.3975 (进度: 0%)
2025-08-05 17:53:34 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-23.6509, 经度=-70.3975
2025-08-05 17:53:34 线程1：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_004, CPU: 32核 (进度: 0%)
2025-08-05 17:53:34 [信息] 浏览器指纹注入: Canvas=canvas_fp_004, WebGL=ANGLE (NVIDIA GeForce RTX 4060 Direct3D11 vs_5_0 ps_5_0), CPU=32核, RAM=32 GB
2025-08-05 17:53:35 线程2：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-05 17:53:36 线程1：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-05 17:53:37 线程1：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 32
   • 设备内存: 32 GB
   • 平台信息: Win32
   • Do Not Track: auto

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 4060 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (Advanced Micro Devices, Inc.)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 9E0F1A2B
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_011
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-C2D3E4F
   • MAC地址: A1-B2-C3-D4-E5-F6
   • 屏幕分辨率: 2110x1020
   • 可用区域: 2110x980

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: A5B6C7D8
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: 4g
   • 电池API支持: True
   • 电池电量: 0.23
   • 电池充电状态: True
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-05 17:53:37 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 32    • 设备内存: 32 GB    • 平台信息: Win32    • Do Not Track: auto   图形渲染信息:    • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 4060 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (Advanced Micro Devices, Inc.)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 9E0F1A2B    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_011    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-C2D3E4F    • MAC地址: A1-B2-C3-D4-E5-F6    • 屏幕分辨率: 2110x1020    • 可用区域: 2110x980   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: A5B6C7D8    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: 4g    • 电池API支持: True    • 电池电量: 0.23    • 电池充电状态: True    • 性能API支持: True  ================================
2025-08-05 17:53:37 线程2：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 8
   • 设备内存: 32 GB
   • 平台信息: Win32
   • Do Not Track: undefined

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (NVIDIA Technologies)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 0426959D
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_009
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-A1B2C3D
   • MAC地址: 78-9A-BC-DE-F0-12
   • 屏幕分辨率: 1833x1071
   • 可用区域: 1833x1031

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: C1D2E3F4
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: 3g
   • 电池API支持: True
   • 电池电量: 0.47
   • 电池充电状态: True
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-05 17:53:37 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 8    • 设备内存: 32 GB    • 平台信息: Win32    • Do Not Track: undefined   图形渲染信息:    • WebGL渲染器: ANGLE (Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (NVIDIA Technologies)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 0426959D    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_009    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-A1B2C3D    • MAC地址: 78-9A-BC-DE-F0-12    • 屏幕分辨率: 1833x1071    • 可用区域: 1833x1031   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: C1D2E3F4    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: 3g    • 电池API支持: True    • 电池电量: 0.47    • 电池充电状态: True    • 性能API支持: True  ================================
2025-08-05 17:53:37 线程1：[信息] [信息] 已设置浏览器标题: 线程1 - AWS注册工具 (进度: 5%)
2025-08-05 17:53:37 线程1：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-05 17:53:37 线程1：[信息] 浏览器启动成功
2025-08-05 17:53:37 线程2：[信息] [信息] 已设置浏览器标题: 线程2 - AWS注册工具 (进度: 5%)
2025-08-05 17:53:37 线程1：[信息] 获取下一个数据: <EMAIL>
2025-08-05 17:53:37 线程2：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-05 17:53:37 线程2：[信息] 浏览器启动成功
2025-08-05 17:53:37 线程2：[信息] 获取下一个数据: <EMAIL>
2025-08-05 17:53:37 线程1：[信息] 开始处理账户: <EMAIL>
2025-08-05 17:53:37 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-05 17:53:37 线程2：[信息] 开始处理账户: <EMAIL>
2025-08-05 17:53:37 线程1：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-05 17:53:37 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-05 17:53:37 线程1：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-05 17:53:37 线程1：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-05 17:53:37 线程2：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-05 17:53:37 线程1：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-05 17:53:37 线程2：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-05 17:53:37 线程2：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-05 17:53:37 线程2：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-05 17:53:38 线程1：[信息] [信息] 已设置页面视口大小为384x310 (进度: 98%)
2025-08-05 17:53:38 线程1：[信息] [信息] 已重新设置浏览器标题: 线程1 - AWS注册工具 (进度: 98%)
2025-08-05 17:53:38 线程1：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-05 17:53:38 线程2：[信息] [信息] 已设置页面视口大小为384x310 (进度: 98%)
2025-08-05 17:53:38 线程1：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-05 17:53:38 线程1：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-05 17:53:38 线程1：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-05 17:53:38 线程2：[信息] [信息] 已重新设置浏览器标题: 线程2 - AWS注册工具 (进度: 98%)
2025-08-05 17:53:38 线程2：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-05 17:53:38 线程2：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-05 17:53:38 线程2：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0... (进度: 98%)
2025-08-05 17:53:38 线程2：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-05 17:54:04 线程1：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程1 - AWS注册 (进度: 98%)
2025-08-05 17:54:04 线程1：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-05 17:54:04 线程1：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-05 17:54:04 线程1：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-05 17:54:04 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-05 17:54:04 线程1：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-05 17:54:07 线程1：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-05 17:54:07 线程1：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-05 17:54:07 线程1：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-05 17:54:07 线程1：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-05 17:54:07 线程1：[信息] [信息] 🔍 第1次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-05 17:54:07 线程1：[信息] [信息] ✅ 第1次检测发现图形验证码！ (进度: 100%)
2025-08-05 17:54:07 线程1：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-08-05 17:54:07 线程1：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-08-05 17:54:07 线程1：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-08-05 17:54:07 线程1：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-05 17:54:08 线程2：[信息] [信息] 注册失败: Timeout 30000ms exceeded.
Call log:
  - navigating to "https://portal.aws.amazon.com/gp/aws/developer/registration/index.html?refid=em_127222&p=free&c=hp&z=1", waiting until "load" (进度: 98%)
2025-08-05 17:54:08 线程2：[信息] [信息] 第一页相关失败，数据保持不动 (进度: 98%)
2025-08-05 17:54:08 [信息] 第一页相关失败，数据保持不动
2025-08-05 17:54:08 线程2：[错误] 账户注册启动失败: <EMAIL>
2025-08-05 17:54:08 线程2：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-05 17:54:13 线程1：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 35268 字节 (进度: 100%)
2025-08-05 17:54:13 线程1：[信息] [信息] ✅ 图片验证通过：201x71px，35268字节，复杂度符合要求 (进度: 100%)
2025-08-05 17:54:13 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-05 17:54:15 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"bh5yrz"},"taskId":"26336b90-71e2-11f0-bf32-6e28fd6820d3"} (进度: 100%)
2025-08-05 17:54:15 线程1：[信息] [信息] 第一页第1次识别结果: bh5yrz → 转换为小写: bh5yrz (进度: 100%)
2025-08-05 17:54:15 线程1：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-05 17:54:15 线程1：[信息] [信息] 已填入验证码: bh5yrz (进度: 100%)
2025-08-05 17:54:16 线程1：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-05 17:54:18 线程1：[信息] [信息] 第一页第1次图形验证码识别成功 (进度: 100%)
2025-08-05 17:54:18 线程1：[信息] [信息] 第一页图形验证码自动完成 (进度: 100%)
2025-08-05 17:54:18 线程1：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-05 17:54:18 线程1：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-05 17:54:18 线程1：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-05 17:54:18 线程1：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-05 17:54:18 线程1：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-05 17:54:18 线程1：[信息] 账户注册流程已启动: <EMAIL>
2025-08-05 17:54:18 线程1：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-05 17:54:18 [信息] [线程1] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-05 17:54:18 [信息] [线程1] 等待2秒后开始第一次触发...
2025-08-05 17:54:20 [信息] [线程1] 第1次触发邮箱验证码获取...（最多20次）
2025-08-05 17:54:20 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-05 17:54:20
2025-08-05 17:54:23 [信息] [线程1] 第2次触发邮箱验证码获取...（最多20次）
2025-08-05 17:54:23 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-05 17:54:23
2025-08-05 17:54:26 [信息] [线程1] 第3次触发邮箱验证码获取...（最多20次）
2025-08-05 17:54:26 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-05 17:54:26
2025-08-05 17:54:27 [信息] [线程1] 邮箱验证码获取成功: 767659，立即停止重复请求
2025-08-05 17:54:27 [信息] [线程1] 已清理请求文件，停止重复触发
2025-08-05 17:54:27 [信息] [线程1] 已清理响应文件
2025-08-05 17:54:27 线程1：[信息] [信息] 验证码获取成功: 767659，正在自动填入... (进度: 25%)
2025-08-05 17:54:27 线程1：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 25%)
2025-08-05 17:54:27 线程1：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 25%)
2025-08-05 17:54:28 线程1：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 25%)
2025-08-05 17:54:28 [信息] 线程1完成第二页事件已处理
2025-08-05 17:54:28 [信息] 线程1完成第二页，开始批量获取手机号码...
2025-08-05 17:54:28 线程1：[信息] [信息] 线程1第二页验证完成，已通知管理器 (进度: 35%)
2025-08-05 17:54:28 线程1：[信息] [信息] 千川专用：第二页完成，开始获取手机号码（20秒超时，不重试） (进度: 35%)
2025-08-05 17:54:28 线程1：[信息] [信息] 千川手机号码获取任务已启动，将在第四页等待结果 (进度: 48%)
2025-08-05 17:54:28 [信息] [千川API] 第二页完成后开始获取手机号码（20秒超时，不重试）
2025-08-05 17:54:28 [信息] 开始批量获取2个手机号码，服务商: Qianchuan
2025-08-05 17:54:28 [信息] 千川API：不在初始化时获取手机号码，将在各线程第二页完成后获取
2025-08-05 17:54:28 [信息] 批量获取2个手机号码成功
2025-08-05 17:54:28 [信息] [千川API] 请求URL: https://api.qc86.shop/api/getPhone?token=170154-*-c774223f-7655-4425-9871-c8bc459d4a6b&channelId=1532842809726341126&operator=5
2025-08-05 17:54:28 线程2：[信息] [信息]  继续注册被调用，当前状态: Error，当前步骤: 1 (进度: 0%)
2025-08-05 17:54:28 线程2：[信息] [信息]  进行智能页面检测... (进度: 0%)
2025-08-05 17:54:28 线程2：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 0%)
2025-08-05 17:54:28 线程2：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 0%)
2025-08-05 17:54:28 线程2：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 0%)
2025-08-05 17:54:28 线程2：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-05 17:54:28 线程2：[信息] [信息]  智能检测到当前在第1页 (进度: 100%)
2025-08-05 17:54:28 线程2：[信息] [信息] 检测当前页面状态... (进度: 100%)
2025-08-05 17:54:28 线程2：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-05 17:54:28 线程2：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-05 17:54:28 线程2：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 100%)
2025-08-05 17:54:28 线程2：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-05 17:54:28 线程2：[信息] [信息] 智能检测到当前在第1页，继续执行... (进度: 100%)
2025-08-05 17:54:28 线程2：[信息] [信息] 🔍 等待第一页加载完成... (进度: 100%)
2025-08-05 17:54:28 线程2：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 100%)
2025-08-05 17:54:28 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-05 17:54:29 线程2：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 100%)
2025-08-05 17:54:31 [信息] [千川API] 响应内容: {"data":{"city":"","code":0,"desc":null,"lastMsgTime":null,"message":null,"minute":null,"mobile":"4367870308553","phoneId":"3567108553210","province":"","refreshTime":5000,"smsTask":{"createTime":null,"endTime":null,"id":5838490,"phoneNo":"4367870308553","projectId":804413,"startTime":"2025-08-05 17:54:31","status":0,"uid":170154,"updateTime":null},"sp":""},"msg":"操作成功","status":200,"success":true,"t":"35d79792-5650-496f-b26d-f1e9133418d6"}
2025-08-05 17:54:31 [信息] [千川API] 获取手机号码成功: +4367870308553
2025-08-05 17:54:31 线程1：[信息] [信息] 等待密码设置页面加载... (进度: 48%)
2025-08-05 17:54:31 线程1：[信息] [信息] 开始填写密码信息... (进度: 48%)
2025-08-05 17:54:31 线程1：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 48%)
2025-08-05 17:54:31 线程1：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 48%)
2025-08-05 17:54:31 线程1：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 48%)
2025-08-05 17:54:32 线程2：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 100%)
2025-08-05 17:54:32 线程2：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-05 17:54:32 线程2：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-05 17:54:32 线程2：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-05 17:54:32 线程2：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-05 17:54:32 线程2：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-05 17:54:32 线程1：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 48%)
2025-08-05 17:54:34 线程2：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-05 17:54:34 线程2：[信息] [信息] 🔍 第2次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-05 17:54:34 线程2：[信息] [信息] ✅ 第2次检测发现图形验证码！ (进度: 100%)
2025-08-05 17:54:34 线程2：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-08-05 17:54:34 线程2：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-08-05 17:54:34 线程2：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-08-05 17:54:34 线程2：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-05 17:54:35 线程1：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 48%)
2025-08-05 17:54:35 线程1：[信息] [信息] 等待账户类型确认页面加载... (进度: 48%)
2025-08-05 17:54:35 线程1：[信息] [信息] 开始处理账户类型确认... (进度: 48%)
2025-08-05 17:54:40 线程2：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 34462 字节 (进度: 100%)
2025-08-05 17:54:40 线程2：[信息] [信息] ✅ 图片验证通过：201x70px，34462字节，复杂度符合要求 (进度: 100%)
2025-08-05 17:54:40 线程2：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-05 17:54:41 线程1：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 48%)
2025-08-05 17:54:41 线程1：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 48%)
2025-08-05 17:54:43 线程2：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"7bxp85"},"taskId":"36e9b606-71e2-11f0-bbb5-3a376870684b"} (进度: 100%)
2025-08-05 17:54:43 线程2：[信息] [信息] 第一页第1次识别结果: 7bxp85 → 转换为小写: 7bxp85 (进度: 100%)
2025-08-05 17:54:43 线程2：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-05 17:54:43 线程2：[信息] [信息] 已填入验证码: 7bxp85 (进度: 100%)
2025-08-05 17:54:44 线程2：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-05 17:54:46 线程2：[信息] [信息] 第一页第1次图形验证码识别成功 (进度: 100%)
2025-08-05 17:54:46 线程2：[信息] [信息] 第一页图形验证码自动完成 (进度: 100%)
2025-08-05 17:54:46 线程2：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-05 17:54:46 线程2：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-05 17:54:46 线程2：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-05 17:54:46 线程2：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-05 17:54:46 线程2：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-05 17:54:46 [信息] [线程2] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-05 17:54:46 [信息] [线程2] 等待2秒后开始第一次触发...
2025-08-05 17:54:46 线程2：[信息] 已继续
2025-08-05 17:54:46 [信息] 线程2已继续
2025-08-05 17:54:48 [信息] [线程2] 第1次触发邮箱验证码获取...（最多20次）
2025-08-05 17:54:48 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-05 17:54:48
2025-08-05 17:54:51 [信息] [线程2] 第2次触发邮箱验证码获取...（最多20次）
2025-08-05 17:54:51 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-05 17:54:51
2025-08-05 17:54:54 [信息] [线程2] 第3次触发邮箱验证码获取...（最多20次）
2025-08-05 17:54:54 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-05 17:54:54
2025-08-05 17:54:55 [信息] [线程2] 邮箱验证码获取成功: 629212，立即停止重复请求
2025-08-05 17:54:55 [信息] [线程2] 已清理请求文件，停止重复触发
2025-08-05 17:54:55 [信息] [线程2] 已清理响应文件
2025-08-05 17:54:55 线程2：[信息] [信息] 验证码获取成功: 629212，正在自动填入... (进度: 100%)
2025-08-05 17:54:55 线程2：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 100%)
2025-08-05 17:54:56 线程2：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 100%)
2025-08-05 17:54:56 线程2：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 100%)
2025-08-05 17:54:56 [信息] 线程2完成第二页事件已处理
2025-08-05 17:54:56 [信息] 线程2完成第二页，手机号码已获取，无需重复获取
2025-08-05 17:54:56 线程2：[信息] [信息] 线程2第二页验证完成，已通知管理器 (进度: 100%)
2025-08-05 17:54:56 线程2：[信息] [信息] 千川专用：第二页完成，开始获取手机号码（20秒超时，不重试） (进度: 100%)
2025-08-05 17:54:56 [信息] [千川API] 第二页完成后开始获取手机号码（20秒超时，不重试）
2025-08-05 17:54:56 [信息] [千川API] 请求URL: https://api.qc86.shop/api/getPhone?token=170154-*-c774223f-7655-4425-9871-c8bc459d4a6b&channelId=1532842809726341126&operator=5
2025-08-05 17:54:56 线程2：[信息] [信息] 千川手机号码获取任务已启动，将在第四页等待结果 (进度: 100%)
2025-08-05 17:54:58 [信息] [千川API] 响应内容: {"data":{"city":"","code":0,"desc":null,"lastMsgTime":null,"message":null,"minute":null,"mobile":"4367870309746","phoneId":"3569909746601","province":"","refreshTime":5000,"smsTask":{"createTime":null,"endTime":null,"id":5838494,"phoneNo":"4367870309746","projectId":804413,"startTime":"2025-08-05 17:54:59","status":0,"uid":170154,"updateTime":null},"sp":""},"msg":"操作成功","status":200,"success":true,"t":"ca716e20-c7e3-483f-aa7a-0333758c05fc"}
2025-08-05 17:54:58 [信息] [千川API] 获取手机号码成功: +4367870309746
2025-08-05 17:54:59 线程2：[信息] [信息] 等待密码设置页面加载... (进度: 100%)
2025-08-05 17:54:59 线程2：[信息] [信息] 开始填写密码信息... (进度: 100%)
2025-08-05 17:54:59 线程2：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 100%)
2025-08-05 17:54:59 线程2：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 100%)
2025-08-05 17:54:59 线程2：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 100%)
2025-08-05 17:55:00 线程2：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 100%)
2025-08-05 17:55:01 线程1：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 48%)
2025-08-05 17:55:01 [警告] 线程1未找到分配的手机号码，服务商: Qianchuan
2025-08-05 17:55:01 线程1：[信息] [信息] 多线程模式：未找到分配的手机号码，尝试后台获取... (进度: 48%)
2025-08-05 17:55:01 线程1：[信息] [信息] 开始后台获取手机号码，同时填写其他信息... (进度: 48%)
2025-08-05 17:55:01 [信息] [千川API] 请求URL: https://api.qc86.shop/api/getPhone?token=170154-*-c774223f-7655-4425-9871-c8bc459d4a6b&channelId=1532842809726341126&operator=5
2025-08-05 17:55:02 线程1：[信息] [信息] 数据国家代码为CL，需要选择Chile (进度: 48%)
2025-08-05 17:55:02 线程1：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 48%)
2025-08-05 17:55:03 线程2：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 100%)
2025-08-05 17:55:03 线程2：[信息] [信息] 等待账户类型确认页面加载... (进度: 100%)
2025-08-05 17:55:03 线程2：[信息] [信息] 开始处理账户类型确认... (进度: 100%)
2025-08-05 17:55:03 [信息] [千川API] 响应内容: {"data":{"city":"","code":0,"desc":null,"lastMsgTime":null,"message":null,"minute":null,"mobile":"4367870300523","phoneId":"************","province":"","refreshTime":5000,"smsTask":{"createTime":null,"endTime":null,"id":5838496,"phoneNo":"4367870300523","projectId":804413,"startTime":"2025-08-05 17:55:04","status":0,"uid":170154,"updateTime":null},"sp":""},"msg":"操作成功","status":200,"success":true,"t":"87f802e6-57a5-43d4-af38-2fe6ee102b1d"}
2025-08-05 17:55:03 [信息] [千川API] 获取手机号码成功: +4367870300523
2025-08-05 17:55:04 线程1：[信息] [信息] 已选择国家: Chile (进度: 48%)
2025-08-05 17:55:04 线程1：[信息] [信息] 已成功选择国家: Chile (进度: 48%)
2025-08-05 17:55:04 线程1：[信息] [信息] 千川API模式：使用固定的+43奥地利区号 (进度: 48%)
2025-08-05 17:55:04 线程1：[信息] [信息] 正在选择国家代码 +43 (奥地利 (Austria) +43)... (进度: 48%)
2025-08-05 17:55:04 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 48%)
2025-08-05 17:55:06 线程1：[信息] [信息] 已选择国家代码 +43 (进度: 48%)
2025-08-05 17:55:07 线程1：[信息] [信息] 千川专用：第四页开始处理手机号码 (进度: 48%)
2025-08-05 17:55:07 线程1：[信息] [信息] 等待千川手机号码获取结果（最多20秒）... (进度: 48%)
2025-08-05 17:55:07 线程1：[信息] [信息] 千川手机号码获取成功: +4367870308553 (进度: 48%)
2025-08-05 17:55:07 线程1：[信息] [信息] 千川手机号码已自动填入: +4367870308553 (进度: 48%)
2025-08-05 17:55:08 线程1：[信息] [信息] 使用已保存的手机号码: 67870308553 (进度: 48%)
2025-08-05 17:55:08 线程1：[信息] [信息] 联系信息完成，等待页面加载... (进度: 48%)
2025-08-05 17:55:11 线程2：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 100%)
2025-08-05 17:55:11 线程2：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 100%)
2025-08-05 17:55:11 线程1：[信息] [信息] 进入付款信息页面... (进度: 48%)
2025-08-05 17:55:12 线程1：[信息] [信息] 正在选择月份: December (进度: 48%)
2025-08-05 17:55:13 线程1：[信息] [信息] 已选择月份（标准选项）: December (进度: 48%)
2025-08-05 17:55:13 线程1：[信息] [信息] 正在选择年份: 2029 (进度: 48%)
2025-08-05 17:55:14 线程1：[信息] [信息] 已选择年份（标准选项）: 2029 (进度: 48%)
2025-08-05 17:55:15 线程1：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 48%)
2025-08-05 17:55:15 线程1：[信息] [信息] 开始填写验证码验证页面... (进度: 48%)
2025-08-05 17:55:15 线程1：[信息] [信息] 千川API模式：使用固定的+43奥地利区号 (进度: 48%)
2025-08-05 17:55:15 线程1：[信息] [信息] 正在选择国家代码 +43 (奥地利 (Austria) +43)... (进度: 48%)
2025-08-05 17:55:21 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 48%)
2025-08-05 17:55:23 线程1：[信息] [信息] 已选择国家代码: +43 (进度: 48%)
2025-08-05 17:55:23 线程1：[信息] [信息] 已清空并重新填写手机号码: 67870308553 (进度: 48%)
2025-08-05 17:55:23 线程1：[信息] [信息] 已点击发送验证码按钮 (进度: 48%)
2025-08-05 17:55:25 线程1：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 48%)
2025-08-05 17:55:25 线程1：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-08-05 17:55:25 线程1：[信息] [信息] 图形验证码自动模式：开始自动处理图形验证码... (进度: 100%)
2025-08-05 17:55:25 线程1：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-05 17:55:28 线程1：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-05 17:55:28 线程1：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-05 17:55:33 线程2：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 100%)
2025-08-05 17:55:33 [警告] 线程2未找到分配的手机号码，服务商: Qianchuan
2025-08-05 17:55:33 线程2：[信息] [信息] 多线程模式：未找到分配的手机号码，尝试后台获取... (进度: 100%)
2025-08-05 17:55:33 线程2：[信息] [信息] 开始后台获取手机号码，同时填写其他信息... (进度: 100%)
2025-08-05 17:55:33 [信息] [千川API] 请求URL: https://api.qc86.shop/api/getPhone?token=170154-*-c774223f-7655-4425-9871-c8bc459d4a6b&channelId=1532842809726341126&operator=5
2025-08-05 17:55:33 线程2：[信息] [信息] 数据国家代码为CL，需要选择Chile (进度: 100%)
2025-08-05 17:55:34 线程1：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 34783 字节 (进度: 100%)
2025-08-05 17:55:34 线程1：[信息] [信息] ✅ 图片验证通过：201x71px，34783字节，复杂度符合要求 (进度: 100%)
2025-08-05 17:55:34 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-05 17:55:34 线程2：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 100%)
2025-08-05 17:55:35 [信息] [千川API] 响应内容: {"data":{"city":"","code":0,"desc":null,"lastMsgTime":null,"message":null,"minute":null,"mobile":"4367870301331","phoneId":"************","province":"","refreshTime":5000,"smsTask":{"createTime":null,"endTime":null,"id":5838503,"phoneNo":"4367870301331","projectId":804413,"startTime":"2025-08-05 17:55:35","status":0,"uid":170154,"updateTime":null},"sp":""},"msg":"操作成功","status":200,"success":true,"t":"91502395-ee4c-4075-8c04-f538629d6b07"}
2025-08-05 17:55:35 [信息] [千川API] 获取手机号码成功: +4367870301331
2025-08-05 17:55:35 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"fygnmd"},"taskId":"55f19a0a-71e2-11f0-87f5-a646062fd64a"} (进度: 100%)
2025-08-05 17:55:35 线程1：[信息] [信息] 第六页第1次识别结果: fygnmd → 转换为小写: fygnmd (进度: 100%)
2025-08-05 17:55:35 线程1：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-05 17:55:36 线程1：[信息] [信息] 第六页已填入验证码: fygnmd (进度: 100%)
2025-08-05 17:55:36 线程2：[信息] [信息] 已选择国家: Chile (进度: 100%)
2025-08-05 17:55:36 线程2：[信息] [信息] 已成功选择国家: Chile (进度: 100%)
2025-08-05 17:55:36 线程2：[信息] [信息] 千川API模式：使用固定的+43奥地利区号 (进度: 100%)
2025-08-05 17:55:36 线程2：[信息] [信息] 正在选择国家代码 +43 (奥地利 (Austria) +43)... (进度: 100%)
2025-08-05 17:55:36 线程1：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-05 17:55:36 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-05 17:55:37 线程2：[信息] [信息] 已选择国家代码 +43 (进度: 100%)
2025-08-05 17:55:38 线程2：[信息] [信息] 千川专用：第四页开始处理手机号码 (进度: 100%)
2025-08-05 17:55:38 线程2：[信息] [信息] 等待千川手机号码获取结果（最多20秒）... (进度: 100%)
2025-08-05 17:55:38 线程2：[信息] [信息] 千川手机号码获取成功: +4367870309746 (进度: 100%)
2025-08-05 17:55:38 线程2：[信息] [信息] 千川手机号码已自动填入: +4367870309746 (进度: 100%)
2025-08-05 17:55:39 线程1：[信息] [信息] 第1次图形验证码识别成功 (进度: 100%)
2025-08-05 17:55:39 线程1：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-08-05 17:55:39 线程2：[信息] [信息] 使用已保存的手机号码: 67870309746 (进度: 100%)
2025-08-05 17:55:40 线程2：[信息] [信息] 联系信息完成，等待页面加载... (进度: 100%)
2025-08-05 17:55:42 线程1：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-08-05 17:55:43 线程2：[信息] [信息] 进入付款信息页面... (进度: 100%)
2025-08-05 17:55:44 线程2：[信息] [信息] 正在选择月份: June (进度: 100%)
2025-08-05 17:55:44 线程2：[信息] [信息] 已选择月份（标准选项）: June (进度: 100%)
2025-08-05 17:55:45 线程1：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-05 17:55:45 线程1：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-05 17:55:45 线程2：[信息] [信息] 正在选择年份: 2027 (进度: 100%)
2025-08-05 17:55:45 线程1：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-05 17:55:45 线程1：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-08-05 17:55:45 线程2：[信息] [信息] 已选择年份（标准选项）: 2027 (进度: 100%)
2025-08-05 17:55:46 线程2：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 100%)
2025-08-05 17:55:46 线程2：[信息] [信息] 开始填写验证码验证页面... (进度: 100%)
2025-08-05 17:55:46 线程2：[信息] [信息] 千川API模式：使用固定的+43奥地利区号 (进度: 100%)
2025-08-05 17:55:46 线程2：[信息] [信息] 正在选择国家代码 +43 (奥地利 (Austria) +43)... (进度: 100%)
2025-08-05 17:55:50 线程1：[信息] [信息] 线程1开始独立获取验证码... (进度: 100%)
2025-08-05 17:55:50 线程1：[信息] [信息] 线程1等待5秒后开始获取千川API验证码... (进度: 100%)
2025-08-05 17:55:51 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-05 17:55:52 线程2：[信息] [信息] 已选择国家代码: +43 (进度: 100%)
2025-08-05 17:55:52 线程2：[信息] [信息] 已清空并重新填写手机号码: 67870309746 (进度: 100%)
2025-08-05 17:55:53 线程2：[信息] [信息] 已点击发送验证码按钮 (进度: 100%)
2025-08-05 17:55:55 线程2：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 100%)
2025-08-05 17:55:55 线程2：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-08-05 17:55:55 线程2：[信息] [信息] 图形验证码自动模式：开始自动处理图形验证码... (进度: 100%)
2025-08-05 17:55:55 线程2：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-05 17:55:55 线程1：[信息] [信息] 线程1第1次尝试获取千川验证码...（剩余9次尝试） (进度: 100%)
2025-08-05 17:55:55 [信息] [千川API] 获取验证码，尝试 1/4
2025-08-05 17:55:55 [信息] [千川API] 获取验证码请求: 4367870308553
2025-08-05 17:55:57 [信息] [千川API] 验证码响应: {"data":{"code":"3873","message":"ok","modle":"[AMAZON] 3873"},"msg":"操作成功","status":200,"success":true,"t":"565b9096-cc17-493a-b9ac-5bb2595fc60f"}
2025-08-05 17:55:57 [信息] [千川API] 获取验证码成功: 3873
2025-08-05 17:55:57 线程1：[信息] [信息] 线程1千川验证码获取成功: 3873 (进度: 100%)
2025-08-05 17:55:57 线程1：[信息] [信息] 线程1验证码获取成功: 3873，立即填入验证码... (进度: 100%)
2025-08-05 17:55:57 [信息] 线程1手机号码已加入释放队列: +4367870308553 (原因: 验证码获取成功)
2025-08-05 17:55:57 [信息] 定时检查发现1个待释放手机号码，开始批量释放
2025-08-05 17:55:57 [错误] 手机API服务未设置，无法释放手机号码
2025-08-05 17:55:57 [信息] 定时批量释放完成: 手机API服务未设置
2025-08-05 17:55:57 线程1：[信息] [信息] 第七页：找到验证码输入框 (Verify code) (进度: 100%)
2025-08-05 17:55:57 线程1：[信息] [信息] 线程1已自动填入手机验证码: 3873 (进度: 100%)
2025-08-05 17:55:58 线程2：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-05 17:55:58 线程2：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-05 17:55:58 线程1：[信息] [信息] 线程1正在自动点击Continue按钮... (进度: 100%)
2025-08-05 17:55:58 线程1：[信息] [信息] 线程1手机验证码验证完成，继续执行后续步骤... (进度: 100%)
2025-08-05 17:56:01 线程1：[信息] [信息] 验证码验证完成，等待页面加载... (进度: 100%)
2025-08-05 17:56:01 线程1：[信息] [信息] 已点击完成注册按钮，正在处理... (进度: 100%)
2025-08-05 17:56:02 线程1：[信息] [信息] 等待完成注册页面加载... (进度: 100%)
2025-08-05 17:56:06 线程1：[信息] [信息] 完成注册页面加载完成 (进度: 100%)
2025-08-05 17:56:06 线程1：[信息] [信息] 🔍 正在查找'Go to the AWS Management Console'链接... (进度: 100%)
2025-08-05 17:56:07 线程2：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 34586 字节 (进度: 100%)
2025-08-05 17:56:07 线程2：[信息] [信息] ✅ 图片验证通过：201x71px，34586字节，复杂度符合要求 (进度: 100%)
2025-08-05 17:56:07 线程2：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-05 17:56:09 线程2：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"pb72wx"},"taskId":"69f64d70-71e2-11f0-bbb5-3a376870684b"} (进度: 100%)
2025-08-05 17:56:09 线程2：[信息] [信息] 第六页第1次识别结果: pb72wx → 转换为小写: pb72wx (进度: 100%)
2025-08-05 17:56:09 线程2：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-05 17:56:09 线程2：[信息] [信息] 第六页已填入验证码: pb72wx (进度: 100%)
2025-08-05 17:56:09 线程2：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-05 17:56:11 线程1：[信息] [信息] 🖱️ 已点击跳转管理控制台链接 (进度: 100%)
2025-08-05 17:56:11 线程1：[信息] [信息] 🔗 通过导航进入IAM安全凭证页面... (进度: 100%)
2025-08-05 17:56:11 线程1：[信息] [信息] ⏳ 等待管理控制台页面加载，智能检测'更多'按钮（超时20秒）... (进度: 100%)
2025-08-05 17:56:12 线程2：[信息] [信息] 第1次图形验证码识别成功 (进度: 100%)
2025-08-05 17:56:12 线程2：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-08-05 17:56:15 线程2：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-08-05 17:56:18 线程2：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-05 17:56:18 线程2：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-05 17:56:18 线程2：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-05 17:56:18 线程2：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-08-05 17:56:23 线程2：[信息] [信息] 线程2开始独立获取验证码... (进度: 100%)
2025-08-05 17:56:23 线程2：[信息] [信息] 线程2等待5秒后开始获取千川API验证码... (进度: 100%)
2025-08-05 17:56:28 线程2：[信息] [信息] 线程2第1次尝试获取千川验证码...（剩余9次尝试） (进度: 100%)
2025-08-05 17:56:28 [信息] [千川API] 获取验证码，尝试 1/4
2025-08-05 17:56:28 [信息] [千川API] 获取验证码请求: 4367870309746
2025-08-05 17:56:29 [信息] [千川API] 验证码响应: {"data":{"code":"","message":"","modle":null},"msg":"操作成功","status":200,"success":true,"t":"9847e8ac-de6b-464b-87f2-81665f6cc4f3"}
2025-08-05 17:56:29 [信息] [千川API] 验证码为空，code字段: , message: 
2025-08-05 17:56:29 [警告] [千川API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-05 17:56:29 线程2：[信息] [信息] 线程2第1次获取千川验证码失败，3秒后重试...（剩余9次尝试） (进度: 100%)
2025-08-05 17:56:32 线程2：[信息] [信息] 线程2第2次尝试获取千川验证码...（剩余8次尝试） (进度: 100%)
2025-08-05 17:56:32 [信息] [千川API] 获取验证码，尝试 1/4
2025-08-05 17:56:32 [信息] [千川API] 获取验证码请求: 4367870309746
2025-08-05 17:56:32 线程1：[信息] [信息] ⚠️ 20秒内未找到'更多'按钮，继续执行后续流程... (进度: 100%)
2025-08-05 17:56:32 [信息] 20秒内未找到'更多'按钮，但继续执行
2025-08-05 17:56:32 线程1：[信息] [信息] 🔍 智能检测更多按钮... (进度: 100%)
2025-08-05 17:56:32 线程1：[信息] [信息] 🔍 第1次检查更多按钮... (进度: 100%)
2025-08-05 17:56:32 [信息] [千川API] 验证码响应: {"data":{"code":"","message":"","modle":null},"msg":"操作成功","status":200,"success":true,"t":"0945c9db-516b-4a58-bf60-df01bcd4a1a3"}
2025-08-05 17:56:32 [信息] [千川API] 验证码为空，code字段: , message: 
2025-08-05 17:56:32 [警告] [千川API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-05 17:56:32 线程2：[信息] [信息] 线程2第2次获取千川验证码失败，3秒后重试...（剩余8次尝试） (进度: 100%)
2025-08-05 17:56:33 线程1：[信息] [信息] ⚠️ 第1次检查未找到更多按钮 (进度: 100%)
2025-08-05 17:56:33 [信息] 第1次检查未找到更多按钮
2025-08-05 17:56:33 线程1：[信息] [信息] ⏳ 等待3秒后重新检查... (进度: 100%)
2025-08-05 17:56:35 线程2：[信息] [信息] 线程2第3次尝试获取千川验证码...（剩余7次尝试） (进度: 100%)
2025-08-05 17:56:35 [信息] [千川API] 获取验证码，尝试 1/4
2025-08-05 17:56:35 [信息] [千川API] 获取验证码请求: 4367870309746
2025-08-05 17:56:35 [信息] [千川API] 验证码响应: {"data":{"code":"9413","message":"ok","modle":"[AMAZON] 9413"},"msg":"操作成功","status":200,"success":true,"t":"343d9863-7763-47ab-a2ca-74423b213958"}
2025-08-05 17:56:35 [信息] [千川API] 获取验证码成功: 9413
2025-08-05 17:56:35 线程2：[信息] [信息] 线程2千川验证码获取成功: 9413 (进度: 100%)
2025-08-05 17:56:35 线程2：[信息] [信息] 线程2验证码获取成功: 9413，立即填入验证码... (进度: 100%)
2025-08-05 17:56:35 [信息] 线程2手机号码已加入释放队列: +4367870309746 (原因: 验证码获取成功)
2025-08-05 17:56:35 线程2：[信息] [信息] 第七页：找到验证码输入框 (Verify code) (进度: 100%)
2025-08-05 17:56:35 线程2：[信息] [信息] 线程2已自动填入手机验证码: 9413 (进度: 100%)
2025-08-05 17:56:36 线程1：[信息] [信息] 🔍 第2次检查更多按钮... (进度: 100%)
2025-08-05 17:56:36 线程2：[信息] [信息] 线程2正在自动点击Continue按钮... (进度: 100%)
2025-08-05 17:56:36 线程2：[信息] [信息] 线程2手机验证码验证完成，继续执行后续步骤... (进度: 100%)
2025-08-05 17:56:37 线程1：[信息] [信息] ⚠️ 第2次检查未找到更多按钮 (进度: 100%)
2025-08-05 17:56:37 [信息] 第2次检查未找到更多按钮
2025-08-05 17:56:37 线程1：[信息] [信息] ⏳ 等待3秒后重新检查... (进度: 100%)
2025-08-05 17:56:39 线程2：[信息] [信息] 验证码验证完成，等待页面加载... (进度: 100%)
2025-08-05 17:56:40 线程1：[信息] [信息] 🔍 第3次检查更多按钮... (进度: 100%)
2025-08-05 17:56:40 线程1：[信息] [信息] ✅ 第3次检查成功找到更多按钮 (进度: 100%)
2025-08-05 17:56:40 [信息] 第3次检查成功找到更多按钮
2025-08-05 17:56:40 线程1：[信息] [信息] 🖱️ 正在点击更多按钮... (进度: 100%)
2025-08-05 17:56:40 线程1：[信息] [信息] ✅ 成功点击更多按钮 (进度: 100%)
2025-08-05 17:56:40 [信息] 成功点击更多按钮
2025-08-05 17:56:41 线程1：[信息] [信息] 🖱️ 正在点击账户信息按钮... (进度: 100%)
2025-08-05 17:56:41 线程1：[信息] [信息] ✅ 成功点击账户信息按钮 (进度: 100%)
2025-08-05 17:56:41 [信息] 成功点击账户信息按钮
2025-08-05 17:56:42 线程1：[信息] [信息] 🖱️ 正在点击'安全凭证'菜单项... (进度: 100%)
2025-08-05 17:56:42 线程1：[信息] [信息] 🔍 正在定位'安全凭证'链接... (进度: 100%)
2025-08-05 17:56:42 线程1：[信息] [信息] ✅ 成功定位到'安全凭证'链接 (进度: 100%)
2025-08-05 17:56:42 [信息] 成功定位到'安全凭证'链接
2025-08-05 17:56:50 线程1：[信息] [信息] ✅ 成功点击'安全凭证'菜单项 (进度: 100%)
2025-08-05 17:56:50 [信息] 成功点击'安全凭证'链接
2025-08-05 17:56:50 线程1：[信息] [信息] ⏳ 等待IAM安全凭证页面加载（20秒超时，循环检测账单问题和创建密钥按钮）... (进度: 100%)
2025-08-05 17:56:52 线程2：[信息] [信息] 已点击完成注册按钮，正在处理... (进度: 100%)
2025-08-05 17:56:53 线程2：[信息] [信息] 等待完成注册页面加载... (进度: 100%)
2025-08-05 17:56:56 线程2：[信息] [信息] 完成注册页面加载完成 (进度: 100%)
2025-08-05 17:56:56 线程2：[信息] [信息] 🔍 正在查找'Go to the AWS Management Console'链接... (进度: 100%)
2025-08-05 17:56:57 [信息] 定时检查发现1个待释放手机号码，开始批量释放
2025-08-05 17:56:57 [错误] 手机API服务未设置，无法释放手机号码
2025-08-05 17:56:57 [信息] 定时批量释放完成: 手机API服务未设置
2025-08-05 17:57:02 线程2：[信息] [信息] 🖱️ 已点击跳转管理控制台链接 (进度: 100%)
2025-08-05 17:57:02 线程2：[信息] [信息] 🔗 通过导航进入IAM安全凭证页面... (进度: 100%)
2025-08-05 17:57:02 线程2：[信息] [信息] ⏳ 等待管理控制台页面加载，智能检测'更多'按钮（超时20秒）... (进度: 100%)
2025-08-05 17:57:09 线程1：[信息] [信息] ✅ 找到'创建访问密钥'按钮，页面加载完成 (进度: 100%)
2025-08-05 17:57:09 线程1：[信息] [信息] ✅ 页面状态正常，找到创建密钥按钮，开始密钥提取流程 (进度: 100%)
2025-08-05 17:57:09 线程1：[信息] [信息] 🔍 设置页面缩放为50%... (进度: 100%)
2025-08-05 17:57:10 线程1：[信息] [信息] ✅ 页面缩放设置完成 (进度: 100%)
2025-08-05 17:57:10 [信息] 页面缩放设置为50%完成
2025-08-05 17:57:10 线程1：[信息] [信息] 🔍 正在查找并点击'下一步'按钮关闭向导... (进度: 100%)
2025-08-05 17:57:10 [信息] 开始查找并点击'下一步'按钮关闭向导
2025-08-05 17:57:10 线程1：[信息] [信息] ✅ 找到可见的下一步按钮，使用选择器: Locator(button[data-testid='awsc-nav-services-tooltip-confirm-button']) (进度: 100%)
2025-08-05 17:57:10 [信息] 找到可见的下一步按钮，使用选择器: Locator(button[data-testid='awsc-nav-services-tooltip-confirm-button'])
2025-08-05 17:57:10 线程1：[信息] [信息] ✅ 第一次点击'下一步'按钮完成 (选择器: Locator(button[data-testid='awsc-nav-services-tooltip-confirm-button'])) (进度: 100%)
2025-08-05 17:57:10 [信息] 第一次点击'下一步'按钮完成 (选择器: Locator(button[data-testid='awsc-nav-services-tooltip-confirm-button']))
2025-08-05 17:57:12 线程1：[信息] [信息] ℹ️ 第二次点击时按钮已消失，向导已关闭 (进度: 100%)
2025-08-05 17:57:12 [信息] 第二次点击时按钮已消失，向导已关闭
2025-08-05 17:57:12 线程1：[信息] [信息] ✅ '下一步'按钮点击流程完成 (进度: 100%)
2025-08-05 17:57:12 [信息] '下一步'按钮点击流程完成
2025-08-05 17:57:12 线程1：[信息] [信息] 🔑 第四阶段：开始创建和复制访问密钥 (进度: 100%)
2025-08-05 17:57:12 [信息] 开始创建和复制访问密钥
2025-08-05 17:57:12 线程1：[信息] [信息] 🔑 正在点击'创建访问密钥'按钮... (进度: 100%)
2025-08-05 17:57:12 线程1：[信息] [信息] 🔍 正在定位'创建访问密钥'按钮... (进度: 100%)
2025-08-05 17:57:12 线程1：[信息] [信息] ✅ 成功定位到'创建访问密钥'按钮 (进度: 100%)
2025-08-05 17:57:12 [信息] 成功定位到'创建访问密钥'按钮
2025-08-05 17:57:12 线程1：[信息] [信息] ✅ 成功点击'创建访问密钥'按钮 (进度: 100%)
2025-08-05 17:57:12 [信息] 成功点击'创建访问密钥'按钮
2025-08-05 17:57:14 线程1：[信息] [信息] ☑️ 正在勾选确认复选框... (进度: 100%)
2025-08-05 17:57:16 线程2：[信息] [信息] ✅ 找到'更多'按钮，页面加载完成 (进度: 100%)
2025-08-05 17:57:16 线程2：[信息] [信息] 🖱️ 正在点击更多按钮... (进度: 100%)
2025-08-05 17:57:16 线程2：[信息] [信息] ✅ 成功点击更多按钮 (进度: 100%)
2025-08-05 17:57:16 [信息] 成功点击更多按钮
2025-08-05 17:57:17 线程2：[信息] [信息] 🖱️ 正在点击账户信息按钮... (进度: 100%)
2025-08-05 17:57:17 线程2：[信息] [信息] ✅ 成功点击账户信息按钮 (进度: 100%)
2025-08-05 17:57:17 [信息] 成功点击账户信息按钮
2025-08-05 17:57:18 线程1：[信息] [信息] ✅ 使用id属性定位到确认复选框 (进度: 100%)
2025-08-05 17:57:18 [信息] 使用id属性定位到确认复选框
2025-08-05 17:57:18 线程1：[信息] [信息] ✅ 成功勾选确认复选框 (进度: 100%)
2025-08-05 17:57:18 [信息] 成功勾选确认复选框
2025-08-05 17:57:18 线程2：[信息] [信息] 🖱️ 正在点击'安全凭证'菜单项... (进度: 100%)
2025-08-05 17:57:18 线程2：[信息] [信息] 🔍 正在定位'安全凭证'链接... (进度: 100%)
2025-08-05 17:57:18 线程2：[信息] [信息] ✅ 成功定位到'安全凭证'链接 (进度: 100%)
2025-08-05 17:57:18 [信息] 成功定位到'安全凭证'链接
2025-08-05 17:57:19 线程1：[信息] [信息] 🔑 正在点击'创建访问密钥'确认按钮... (进度: 100%)
2025-08-05 17:57:19 线程1：[信息] [信息] ✅ 成功点击'创建访问密钥'确认按钮 (进度: 100%)
2025-08-05 17:57:19 [信息] 成功点击'创建访问密钥'确认按钮
2025-08-05 17:57:22 线程1：[信息] [信息] 📋 开始复制访问密钥... (进度: 100%)
2025-08-05 17:57:22 [信息] 开始复制访问密钥
2025-08-05 17:57:24 线程1：[信息] [信息] 🔍 方法2找到 2 个单元格 (进度: 100%)
2025-08-05 17:57:24 [信息] 方法2找到 2 个单元格
2025-08-05 17:57:24 线程1：[信息] [信息] 🔍 总共找到 2 个单元格，开始分析... (进度: 100%)
2025-08-05 17:57:24 [信息] 总共找到 2 个单元格，开始分析
2025-08-05 17:57:24 [信息] 单元格[0]: '********************'
2025-08-05 17:57:24 [信息] 单元格[1]: '***************Mostrar'
2025-08-05 17:57:24 线程1：[信息] [信息] 📋 正在查找并复制访问密钥... (进度: 100%)
2025-08-05 17:57:24 线程1：[信息] [信息] 🔍 解决向导阻挡... (进度: 100%)
2025-08-05 17:57:24 线程1：[信息] [信息] 🔍 正在查找并点击'下一步'按钮关闭向导... (进度: 100%)
2025-08-05 17:57:24 [信息] 开始查找并点击'下一步'按钮关闭向导
2025-08-05 17:57:24 线程1：[信息] [信息] ℹ️ 未检测到向导元素，向导可能已自动关闭 (进度: 100%)
2025-08-05 17:57:24 [信息] 未检测到向导元素，向导可能已自动关闭
2025-08-05 17:57:24 线程1：[信息] [信息] ✅ 找到访问密钥: ******************** (进度: 100%)
2025-08-05 17:57:24 [信息] 找到访问密钥: ********************
2025-08-05 17:57:25 线程1：[信息] [信息] ✅ 方法1成功点击访问密钥复制按钮 (进度: 100%)
2025-08-05 17:57:25 [信息] 方法1成功点击访问密钥复制按钮
2025-08-05 17:57:26 线程1：[信息] [信息] 📋 正在查找并复制秘密访问密钥... (进度: 100%)
2025-08-05 17:57:26 线程1：[信息] [信息] ✅ 找到秘密访问密钥: ***************Mostrar (进度: 100%)
2025-08-05 17:57:26 [信息] 找到秘密访问密钥: ***************Mostrar
2025-08-05 17:57:26 线程1：[信息] [信息] 🔍 解决向导阻挡... (进度: 100%)
2025-08-05 17:57:26 线程1：[信息] [信息] 🔍 正在查找并点击'下一步'按钮关闭向导... (进度: 100%)
2025-08-05 17:57:26 [信息] 开始查找并点击'下一步'按钮关闭向导
2025-08-05 17:57:26 线程1：[信息] [信息] ℹ️ 未检测到向导元素，向导可能已自动关闭 (进度: 100%)
2025-08-05 17:57:26 [信息] 未检测到向导元素，向导可能已自动关闭
2025-08-05 17:57:26 线程1：[信息] [信息] 🔍 检测到隐藏的秘密访问密钥，尝试点击显示按钮... (进度: 100%)
2025-08-05 17:57:26 [信息] 检测到隐藏的秘密访问密钥，尝试点击显示按钮
2025-08-05 17:57:26 线程1：[信息] [信息] ✅ 使用TestId定位到显示按钮 (进度: 100%)
2025-08-05 17:57:26 [信息] 使用TestId定位到显示按钮
2025-08-05 17:57:27 线程1：[信息] [信息] ✅ 显示按钮点击成功，新文本: Zkv9xgWkXWWw4Tji1EyIT9KLSwLSv7UodCEHBdFMOcultar (进度: 100%)
2025-08-05 17:57:27 [信息] 显示按钮点击成功，新文本: Zkv9xgWkXWWw4Tji1EyIT9KLSwLSv7UodCEHBdFMOcultar
2025-08-05 17:57:27 线程1：[信息] [信息] ✅ 直接从显示文本提取秘密访问密钥: Zkv9xgWkXWWw4Tji1EyIT9KLSwLSv7UodCEHBdFMOcultar (进度: 100%)
2025-08-05 17:57:27 [信息] 直接从显示文本提取秘密访问密钥: Zkv9xgWkXWWw4Tji1EyIT9KLSwLSv7UodCEHBdFMOcultar
2025-08-05 17:57:27 线程1：[信息] [信息] ✅ 访问密钥复制完成 (进度: 100%)
2025-08-05 17:57:27 [信息] 访问密钥复制完成 - AccessKey: ********************, SecretKey: Zkv9xgWkXWWw4Tji1EyIT9KLSwLSv7UodCEHBdFM
2025-08-05 17:57:28 线程1：[信息] [信息] 🖱️ 正在点击'已完成'按钮... (进度: 100%)
2025-08-05 17:57:28 线程1：[信息] [信息] ✅ 成功点击'已完成'按钮 (进度: 100%)
2025-08-05 17:57:28 [信息] 成功点击'已完成'按钮
2025-08-05 17:57:28 线程1：[信息] [信息] ✅ 密钥已保存到数据对象 - AccessKey: ********************, SecretKey: Zkv9xgWkXWWw4Tji1EyIT9KLSwLSv7UodCEHBdFM (进度: 100%)
2025-08-05 17:57:28 [信息] 密钥已保存到数据对象 - AccessKey: ********************, SecretKey: Zkv9xgWkXWWw4Tji1EyIT9KLSwLSv7UodCEHBdFM
2025-08-05 17:57:29 线程2：[信息] [信息] ✅ 成功点击'安全凭证'菜单项 (进度: 100%)
2025-08-05 17:57:29 [信息] 成功点击'安全凭证'链接
2025-08-05 17:57:29 线程2：[信息] [信息] ⏳ 等待IAM安全凭证页面加载（20秒超时，循环检测账单问题和创建密钥按钮）... (进度: 100%)
2025-08-05 17:57:29 线程1：[信息] [信息] 🖱️ 正在点击'继续'按钮... (进度: 100%)
2025-08-05 17:57:32 线程2：[信息] [信息] ❌ 检测到账单问题页面跳转 (进度: 100%)
2025-08-05 17:57:32 线程2：[信息] [信息] ❌ 检测到账单问题，处理账单问题流程 (进度: 100%)
2025-08-05 17:57:32 线程2：[信息] [信息] ⚠️ 处理账单问题，终止注册 (进度: 100%)
2025-08-05 17:57:32 [信息] 检测到账单问题，开始处理
2025-08-05 17:57:32 线程2：[信息] [信息] 账单问题注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：PbdH7NQ2 ③AWS密码：g9JnYlwO ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单 (进度: 100%)
2025-08-05 17:57:32 [信息] 账单问题注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：PbdH7NQ2 ③AWS密码：g9JnYlwO ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单
2025-08-05 17:57:32 线程2：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：PbdH7NQ2 ③AWS密码：g9JnYlwO ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单
2025-08-05 17:57:32 [信息] 线程2请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：PbdH7NQ2 ③AWS密码：g9JnYlwO ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单
2025-08-05 17:57:32 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：PbdH7NQ2 ③AWS密码：g9JnYlwO ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单
2025-08-05 17:57:32 [信息] 线程2剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：PbdH7NQ2 ③AWS密码：g9JnYlwO ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单
2025-08-05 17:57:32 线程2：[信息] 账户注册完成(账单问题)，等待后续操作: <EMAIL>
2025-08-05 17:57:32 线程2：[信息] [信息] ✅ 注册成功，账户提示账单 (进度: 100%)
2025-08-05 17:57:32 [信息] 注册完成 - 账单提示处理
2025-08-05 17:57:32 [信息] 开始处理账单问题数据完成事件: <EMAIL>
2025-08-05 17:57:32 [信息] 已将账单问题数据移动到注册成功区域: <EMAIL>
2025-08-05 17:57:32 [信息] 已完成账单问题数据移除: <EMAIL>
2025-08-05 17:57:32 [信息] 账单问题数据完成事件处理完毕: <EMAIL>
2025-08-05 17:57:32 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-08-05 17:57:32 线程2：[信息] 最终完成: 注册完成，账单提示无法提取密钥: <EMAIL> (类型: WithBillingIssue)
2025-08-05 17:57:32 [信息] 线程2数据完成事件已通知主窗口并最终完成: <EMAIL>
2025-08-05 17:57:32 线程2：[信息] [信息] 图形验证码处理完成，请手动输入手机验证码... (进度: 100%)
2025-08-05 17:57:32 线程1：[信息] [信息] ❌ 点击'继续'按钮失败: Timeout 3000ms exceeded.
Call log:
  - waiting for Locator("button[data-awsui-analytics*='navigate']") to be visible (进度: 100%)
2025-08-05 17:57:32 [信息] 点击'继续'按钮失败: Timeout 3000ms exceeded.
Call log:
  - waiting for Locator("button[data-awsui-analytics*='navigate']") to be visible
2025-08-05 17:57:32 线程1：[信息] [信息] 🔐 开始设置MFA设备... (进度: 100%)
2025-08-05 17:57:32 [信息] 开始设置MFA设备
2025-08-05 17:57:32 线程1：[信息] [信息] 🖱️ 正在点击'分配 MFA 设备'按钮... (进度: 100%)
2025-08-05 17:57:32 线程1：[信息] [信息] ✅ 成功点击'分配 MFA 设备'按钮 (进度: 100%)
2025-08-05 17:57:32 [信息] 成功点击'分配 MFA 设备'按钮
2025-08-05 17:57:32 线程1：[信息] [信息] ⏳ 等待MFA设备设置页面加载（20秒超时）... (进度: 100%)
2025-08-05 17:57:39 线程1：[信息] [信息] ✅ 通过第一个文本输入框找到设备名称输入框，页面加载完成 (进度: 100%)
2025-08-05 17:57:39 [信息] 通过第一个文本输入框找到设备名称输入框
2025-08-05 17:57:39 线程1：[信息] [信息] 📝 正在输入设备名称... (进度: 100%)
2025-08-05 17:57:39 线程1：[信息] [信息] ✅ 通过第一个文本输入框定位到设备名称输入框 (进度: 100%)
2025-08-05 17:57:39 线程1：[信息] [信息] ✅ 成功输入设备名称: joannkey (进度: 100%)
2025-08-05 17:57:39 [信息] 成功输入设备名称: joannkey
2025-08-05 17:57:39 线程1：[信息] [信息] ☑️ 正在选择'身份验证器应用程序'... (进度: 100%)
2025-08-05 17:57:39 线程1：[信息] [信息] ✅ 成功选择'身份验证器应用程序' (进度: 100%)
2025-08-05 17:57:39 [信息] 成功选择'身份验证器应用程序'
2025-08-05 17:57:39 线程1：[信息] [信息] 🖱️ 正在点击'下一步'按钮... (进度: 100%)
2025-08-05 17:57:40 线程1：[信息] [信息] ✅ 成功点击'下一步'按钮 (进度: 100%)
2025-08-05 17:57:40 [信息] 成功点击'下一步'按钮
2025-08-05 17:57:40 线程1：[信息] [信息] ⏳ 等待MFA密钥页面加载（20秒超时）... (进度: 100%)
2025-08-05 17:57:49 线程2：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-08-05 17:57:49 线程2：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 100%)
2025-08-05 17:57:49 线程2：[信息] 已暂停
2025-08-05 17:57:49 [信息] 线程2已暂停
2025-08-05 17:57:49 [信息] 线程2已暂停
2025-08-05 17:58:00 线程1：[信息] [信息] ⚠️ 20秒内未找到'显示密钥'按钮，开始抓取显示密钥元素 (进度: 100%)
2025-08-05 17:58:00 线程1：[信息] [信息] 🔍 正在抓取显示密钥按钮... (进度: 100%)
2025-08-05 17:58:00 [信息] 开始抓取显示密钥按钮元素
2025-08-05 17:58:00 线程1：[信息] [信息] 🔍 通过所有链接找到 60 个元素 (进度: 100%)
2025-08-05 17:58:00 线程1：[信息] [信息] ✅ 通过所有链接找到显示密钥按钮1 (进度: 100%)
2025-08-05 17:58:00 线程1：[信息] [信息] ✅ 通过所有链接找到显示密钥按钮2 (进度: 100%)
2025-08-05 17:58:00 线程1：[信息] [信息] ✅ 通过所有链接找到显示密钥按钮3 (进度: 100%)
2025-08-05 17:58:00 线程1：[信息] [信息] ✅ 通过所有链接找到显示密钥按钮4 (进度: 100%)
2025-08-05 17:58:00 线程1：[信息] [信息] ✅ 通过所有链接找到显示密钥按钮5 (进度: 100%)
2025-08-05 17:58:00 线程1：[信息] [信息] ✅ 成功抓取到 5 个显示密钥按钮 (进度: 100%)
2025-08-05 17:58:00 [信息] 显示密钥按钮抓取结果:
// 显示密钥按钮1 - 通过所有链接找到
await page.Locator("a").First
await page.Locator("a")
// 显示密钥按钮2 - 通过所有链接找到
await page.Locator("a").First
await page.Locator("a")
// 显示密钥按钮3 - 通过所有链接找到
await page.Locator("a").First
await page.Locator("a")
// 显示密钥按钮4 - 通过所有链接找到
await page.Locator("a[class*='awsui']")
await page.Locator("a").First
await page.Locator("a")
// 显示密钥按钮5 - 通过所有链接找到
await page.Locator("a[class*='awsui']")
await page.Locator("a").First
await page.Locator("a")
2025-08-05 17:58:00 线程1：[信息] [信息] ⚠️ 显示密钥页面加载超时，需要手动处理 (进度: 100%)
2025-08-05 17:58:00 [信息] 显示密钥页面加载超时，需要手动处理
2025-08-05 17:58:00 线程1：[信息] [信息] MFA页面需要手动处理，等待用户操作 (进度: 100%)
2025-08-05 17:58:00 线程1：[信息] [信息] MFA页面需要手动处理，等待用户操作 (进度: 100%)
2025-08-05 17:58:00 线程1：[信息] [信息] 图形验证码处理完成，请手动输入手机验证码... (进度: 100%)
