2025-08-05 15:41:51 [信息] UniformGrid列数已更新为: 1
2025-08-05 15:41:51 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 1, 列数: 1
2025-08-05 15:41:51 线程2：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-05 15:41:51 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-05 15:41:51 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0
2025-08-05 15:41:51 线程2：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-05 15:41:52 [信息] UniformGrid列数已更新为: 1
2025-08-05 15:41:52 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-08-05 15:41:52 线程3：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-05 15:41:52 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-05 15:41:52 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36
2025-08-05 15:41:52 线程3：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-05 15:41:52 [信息] UniformGrid列数已更新为: 2
2025-08-05 15:41:52 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 3, 列数: 2
2025-08-05 15:41:52 线程1：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-05 15:41:52 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-05 15:41:52 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36
2025-08-05 15:41:52 线程1：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-05 15:41:54 线程3：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-05 15:41:56 线程2：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-05 15:41:56 线程1：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-05 15:41:56 线程3：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-05 15:41:56 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-05 15:41:56 线程3：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-05 15:41:56 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-05 15:41:56 线程3：[信息] [信息] 多线程模式使用指纹地理位置: -29.9027, -71.2519 (进度: 0%)
2025-08-05 15:41:56 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-29.9027, 经度=-71.2519
2025-08-05 15:41:57 线程3：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_011, CPU: 20核 (进度: 0%)
2025-08-05 15:41:57 [信息] 浏览器指纹注入: Canvas=canvas_fp_011, WebGL=ANGLE (NVIDIA GeForce RTX 3060 Direct3D11 vs_5_0 ps_5_0), CPU=20核, RAM=20 GB
2025-08-05 15:41:58 线程2：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-05 15:41:58 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-05 15:41:58 线程2：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-05 15:41:58 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-05 15:41:58 线程2：[信息] [信息] 多线程模式使用指纹地理位置: -41.4693, -72.9424 (进度: 0%)
2025-08-05 15:41:58 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-41.4693, 经度=-72.9424
2025-08-05 15:41:58 线程2：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_007, CPU: 2核 (进度: 0%)
2025-08-05 15:41:58 [信息] 浏览器指纹注入: Canvas=canvas_fp_007, WebGL=ANGLE (NVIDIA GeForce GTX 1660 Direct3D11 vs_5_0 ps_5_0), CPU=2核, RAM=12 GB
2025-08-05 15:41:58 线程3：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-05 15:41:58 线程1：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-05 15:41:58 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-05 15:41:58 线程1：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-05 15:41:58 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-05 15:41:58 线程1：[信息] [信息] 多线程模式使用指纹地理位置: -41.4693, -72.9424 (进度: 0%)
2025-08-05 15:41:58 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-41.4693, 经度=-72.9424
2025-08-05 15:41:58 线程1：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_003, CPU: 24核 (进度: 0%)
2025-08-05 15:41:58 [信息] 浏览器指纹注入: Canvas=canvas_fp_003, WebGL=ANGLE (NVIDIA GeForce RTX 3080 Direct3D11 vs_5_0 ps_5_0), CPU=24核, RAM=32 GB
2025-08-05 15:41:59 线程2：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-05 15:42:00 线程1：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-05 15:42:01 线程2：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 2
   • 设备内存: 12 GB
   • 平台信息: Win32
   • Do Not Track: default

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (NVIDIA GeForce GTX 1660 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (Intel Corporation)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 1E2F3A4B
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_010
   • 语音数量: 0

📱 设备环境信息:
   • 设备名称: DESKTOP-I7J8K9L
   • MAC地址: AA-BB-CC-DD-EE-FF
   • 屏幕分辨率: 2031x1109
   • 可用区域: 2031x1069

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: A3B4C5D6
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: 4g
   • 电池API支持: True
   • 电池电量: 0.57
   • 电池充电状态: False
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-05 15:42:01 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 2    • 设备内存: 12 GB    • 平台信息: Win32    • Do Not Track: default   图形渲染信息:    • WebGL渲染器: ANGLE (NVIDIA GeForce GTX 1660 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (Intel Corporation)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 1E2F3A4B    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_010    • 语音数量: 0   设备环境信息:    • 设备名称: DESKTOP-I7J8K9L    • MAC地址: AA-BB-CC-DD-EE-FF    • 屏幕分辨率: 2031x1109    • 可用区域: 2031x1069   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: A3B4C5D6    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: 4g    • 电池API支持: True    • 电池电量: 0.57    • 电池充电状态: False    • 性能API支持: True  ================================
2025-08-05 15:42:01 线程2：[信息] [信息] 已设置浏览器标题: 线程2 - AWS注册工具 (进度: 5%)
2025-08-05 15:42:01 线程2：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-05 15:42:01 线程2：[信息] 浏览器启动成功
2025-08-05 15:42:01 线程2：[信息] 获取下一个数据: <EMAIL>
2025-08-05 15:42:01 线程2：[信息] 开始处理账户: <EMAIL>
2025-08-05 15:42:01 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-05 15:42:01 线程2：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-05 15:42:01 线程2：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-05 15:42:01 线程2：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-05 15:42:01 线程2：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-05 15:42:01 线程1：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 24
   • 设备内存: 32 GB
   • 平台信息: Win32
   • Do Not Track: manual

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 3080 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (Intel Technologies)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 1C2D3E4F
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_004
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-A1B2C3D
   • MAC地址: 34-56-78-9A-BC-DE
   • 屏幕分辨率: 1910x1071
   • 可用区域: 1910x1031

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: E1F2A3B4
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: unknown
   • 电池API支持: True
   • 电池电量: 0.67
   • 电池充电状态: True
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-05 15:42:01 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 24    • 设备内存: 32 GB    • 平台信息: Win32    • Do Not Track: manual   图形渲染信息:    • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 3080 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (Intel Technologies)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 1C2D3E4F    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_004    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-A1B2C3D    • MAC地址: 34-56-78-9A-BC-DE    • 屏幕分辨率: 1910x1071    • 可用区域: 1910x1031   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: E1F2A3B4    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: unknown    • 电池API支持: True    • 电池电量: 0.67    • 电池充电状态: True    • 性能API支持: True  ================================
2025-08-05 15:42:01 线程3：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 20
   • 设备内存: 20 GB
   • 平台信息: Win32
   • Do Not Track: unspecified

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 3060 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (NVIDIA)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 3C4D5E6F
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_005
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-87EL3RX
   • MAC地址: A1-B2-C3-D4-E5-F6
   • 屏幕分辨率: 1768x1165
   • 可用区域: 1768x1125

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: A1B2C3D4
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: cellular
   • 电池API支持: True
   • 电池电量: 0.28
   • 电池充电状态: False
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-05 15:42:01 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 20    • 设备内存: 20 GB    • 平台信息: Win32    • Do Not Track: unspecified   图形渲染信息:    • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 3060 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (NVIDIA)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 3C4D5E6F    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_005    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-87EL3RX    • MAC地址: A1-B2-C3-D4-E5-F6    • 屏幕分辨率: 1768x1165    • 可用区域: 1768x1125   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: A1B2C3D4    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: cellular    • 电池API支持: True    • 电池电量: 0.28    • 电池充电状态: False    • 性能API支持: True  ================================
2025-08-05 15:42:01 线程3：[信息] [信息] 已设置浏览器标题: 线程3 - AWS注册工具 (进度: 5%)
2025-08-05 15:42:01 线程3：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-05 15:42:01 线程3：[信息] 浏览器启动成功
2025-08-05 15:42:01 线程3：[信息] 获取下一个数据: <EMAIL>
2025-08-05 15:42:01 线程3：[信息] 开始处理账户: <EMAIL>
2025-08-05 15:42:02 线程3：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-05 15:42:02 线程3：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-05 15:42:02 线程3：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-05 15:42:02 线程3：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-05 15:42:02 线程3：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-05 15:42:02 线程1：[信息] [信息] 已设置浏览器标题: 线程1 - AWS注册工具 (进度: 5%)
2025-08-05 15:42:02 线程1：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-05 15:42:02 线程1：[信息] 浏览器启动成功
2025-08-05 15:42:02 线程1：[信息] 获取下一个数据: <EMAIL>
2025-08-05 15:42:02 线程1：[信息] 开始处理账户: <EMAIL>
2025-08-05 15:42:02 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-05 15:42:02 线程1：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-05 15:42:02 线程1：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-05 15:42:02 线程1：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-05 15:42:02 线程1：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-05 15:42:07 线程2：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-05 15:42:07 线程2：[信息] [信息] 已重新设置浏览器标题: 线程2 - AWS注册工具 (进度: 98%)
2025-08-05 15:42:07 线程2：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-05 15:42:07 线程2：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-05 15:42:07 线程2：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-05 15:42:07 线程2：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-05 15:42:07 线程1：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-05 15:42:07 线程1：[信息] [信息] 已重新设置浏览器标题: 线程1 - AWS注册工具 (进度: 98%)
2025-08-05 15:42:07 线程1：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-05 15:42:07 线程3：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-05 15:42:07 线程3：[信息] [信息] 已重新设置浏览器标题: 线程3 - AWS注册工具 (进度: 98%)
2025-08-05 15:42:07 线程3：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-05 15:42:07 线程1：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-05 15:42:07 线程1：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-05 15:42:07 线程1：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-05 15:42:07 线程3：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-05 15:42:07 线程3：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-05 15:42:07 线程3：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-05 15:42:37 线程2：[信息] [信息] 注册失败: Timeout 30000ms exceeded.
Call log:
  - navigating to "https://portal.aws.amazon.com/gp/aws/developer/registration/index.html?refid=em_127222&p=free&c=hp&z=1", waiting until "load" (进度: 98%)
2025-08-05 15:42:37 线程2：[信息] [信息] 第一页相关失败，数据保持不动 (进度: 98%)
2025-08-05 15:42:37 [信息] 第一页相关失败，数据保持不动
2025-08-05 15:42:37 线程2：[错误] 账户注册启动失败: <EMAIL>
2025-08-05 15:42:37 线程2：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-05 15:42:37 线程1：[信息] [信息] 注册失败: Timeout 30000ms exceeded.
Call log:
  - navigating to "https://portal.aws.amazon.com/gp/aws/developer/registration/index.html?refid=em_127222&p=free&c=hp&z=1", waiting until "load" (进度: 98%)
2025-08-05 15:42:37 线程1：[信息] [信息] 第一页相关失败，数据保持不动 (进度: 98%)
2025-08-05 15:42:37 [信息] 第一页相关失败，数据保持不动
2025-08-05 15:42:37 线程1：[错误] 账户注册启动失败: <EMAIL>
2025-08-05 15:42:37 线程1：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-05 15:42:37 线程3：[信息] [信息] 注册失败: Timeout 30000ms exceeded.
Call log:
  - navigating to "https://portal.aws.amazon.com/gp/aws/developer/registration/index.html?refid=em_127222&p=free&c=hp&z=1", waiting until "load" (进度: 98%)
2025-08-05 15:42:37 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-05 15:42:37 [信息] 多线程状态已重置
2025-08-05 15:42:37 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-05 15:42:37 [信息] 多线程状态已重置
2025-08-05 15:42:37 线程3：[信息] [信息] 第一页相关失败，数据保持不动 (进度: 98%)
2025-08-05 15:42:37 [信息] 第一页相关失败，数据保持不动
2025-08-05 15:42:37 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-05 15:42:37 [信息] 多线程状态已重置
2025-08-05 15:42:37 线程3：[错误] 账户注册启动失败: <EMAIL>
2025-08-05 15:42:37 线程3：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-05 15:44:02 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-05 15:44:02 [信息] 多线程状态已重置
2025-08-05 15:44:02 线程1：[信息] [信息]  继续注册被调用，当前状态: Error，当前步骤: 1 (进度: 0%)
2025-08-05 15:44:02 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-05 15:44:02 [信息] 多线程状态已重置
2025-08-05 15:44:02 线程1：[信息] [信息]  进行智能页面检测... (进度: 0%)
2025-08-05 15:44:02 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-05 15:44:02 [信息] 多线程状态已重置
2025-08-05 15:44:02 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 0%)
2025-08-05 15:44:02 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-05 15:44:02 [信息] 多线程状态已重置
2025-08-05 15:44:02 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 0%)
2025-08-05 15:44:02 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-05 15:44:02 [信息] 多线程状态已重置
2025-08-05 15:44:02 线程2：[信息] [信息]  继续注册被调用，当前状态: Error，当前步骤: 1 (进度: 0%)
2025-08-05 15:44:02 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-05 15:44:02 [信息] 多线程状态已重置
2025-08-05 15:44:02 线程2：[信息] [信息]  进行智能页面检测... (进度: 0%)
2025-08-05 15:44:02 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-05 15:44:02 [信息] 多线程状态已重置
2025-08-05 15:44:02 线程2：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 0%)
2025-08-05 15:44:02 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-05 15:44:02 [信息] 多线程状态已重置
2025-08-05 15:44:02 线程2：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 0%)
2025-08-05 15:44:02 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-05 15:44:02 [信息] 多线程状态已重置
2025-08-05 15:44:02 线程3：[信息] [信息]  继续注册被调用，当前状态: Error，当前步骤: 1 (进度: 0%)
2025-08-05 15:44:02 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-05 15:44:02 [信息] 多线程状态已重置
2025-08-05 15:44:02 线程3：[信息] [信息]  进行智能页面检测... (进度: 0%)
2025-08-05 15:44:02 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-05 15:44:02 [信息] 多线程状态已重置
2025-08-05 15:44:02 线程3：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 0%)
2025-08-05 15:44:02 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-05 15:44:02 [信息] 多线程状态已重置
2025-08-05 15:44:02 线程3：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 0%)
2025-08-05 15:44:02 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-05 15:44:02 [信息] 多线程状态已重置
2025-08-05 15:44:02 线程2：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 0%)
2025-08-05 15:44:02 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-05 15:44:02 [信息] 多线程状态已重置
2025-08-05 15:44:02 线程2：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-05 15:44:02 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-05 15:44:02 [信息] 多线程状态已重置
2025-08-05 15:44:02 线程2：[信息] [信息]  智能检测到当前在第1页 (进度: 100%)
2025-08-05 15:44:02 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-05 15:44:02 [信息] 多线程状态已重置
2025-08-05 15:44:02 线程2：[信息] [信息] 检测当前页面状态... (进度: 100%)
2025-08-05 15:44:02 线程2：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-05 15:44:02 线程2：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-05 15:44:02 线程3：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 0%)
2025-08-05 15:44:02 线程3：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-05 15:44:02 线程3：[信息] [信息]  智能检测到当前在第1页 (进度: 100%)
2025-08-05 15:44:02 线程3：[信息] [信息] 检测当前页面状态... (进度: 100%)
2025-08-05 15:44:02 线程3：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-05 15:44:02 线程3：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-05 15:44:02 线程2：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 100%)
2025-08-05 15:44:02 线程2：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-05 15:44:02 线程2：[信息] [信息] 智能检测到当前在第1页，继续执行... (进度: 100%)
2025-08-05 15:44:02 线程2：[信息] [信息] 🔍 等待第一页加载完成... (进度: 100%)
2025-08-05 15:44:02 线程3：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 100%)
2025-08-05 15:44:02 线程3：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-05 15:44:02 线程3：[信息] [信息] 智能检测到当前在第1页，继续执行... (进度: 100%)
2025-08-05 15:44:02 线程3：[信息] [信息] 🔍 等待第一页加载完成... (进度: 100%)
2025-08-05 15:44:02 线程2：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 100%)
2025-08-05 15:44:02 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-05 15:44:02 线程3：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 100%)
2025-08-05 15:44:02 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-05 15:44:02 线程3：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 100%)
2025-08-05 15:44:03 线程2：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 100%)
2025-08-05 15:44:05 线程3：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 100%)
2025-08-05 15:44:06 线程3：[信息] [信息] ⚠️ 检测到错误信息，开始重试机制... (进度: 100%)
2025-08-05 15:44:06 [信息] 检测到错误信息，开始重试机制
2025-08-05 15:44:06 线程3：[信息] [信息] 🔄 第1次重试点击验证邮箱按钮... (进度: 100%)
2025-08-05 15:44:06 [信息] 第1次重试点击验证邮箱按钮
2025-08-05 15:44:06 线程2：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 100%)
2025-08-05 15:44:06 线程2：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-05 15:44:06 线程2：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-05 15:44:06 线程2：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-05 15:44:06 线程2：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-05 15:44:06 线程2：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-05 15:44:08 线程3：[信息] [信息] ✅ 第1次重试：已点击验证邮箱按钮 (进度: 100%)
2025-08-05 15:44:08 [信息] 第1次重试：已点击验证邮箱按钮
2025-08-05 15:44:08 线程2：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-05 15:44:08 线程2：[信息] [信息] 🔍 第2次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-05 15:44:08 线程2：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 未发现验证码 (进度: 100%)
2025-08-05 15:44:08 线程2：[信息] [信息] ✅ 第一页未检测到图形验证码，继续流程 (进度: 100%)
2025-08-05 15:44:08 线程2：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-05 15:44:08 线程2：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-05 15:44:08 线程2：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-05 15:44:08 线程2：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-05 15:44:08 线程2：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-05 15:44:08 线程2：[信息] 已继续
2025-08-05 15:44:08 [信息] 线程2已继续
2025-08-05 15:44:08 [信息] [线程2] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-05 15:44:08 [信息] [线程2] 等待2秒后开始第一次触发...
2025-08-05 15:44:10 线程3：[信息] [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']" (进度: 100%)
2025-08-05 15:44:10 [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']"
2025-08-05 15:44:10 线程3：[信息] [信息] ✅ 第1次重试成功：已到达第二页，继续正常流程 (进度: 100%)
2025-08-05 15:44:10 [信息] 第1次重试成功：已到达第二页
2025-08-05 15:44:10 线程3：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-05 15:44:10 线程3：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-05 15:44:10 线程3：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-05 15:44:10 线程3：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-05 15:44:10 [信息] [线程2] 第1次触发邮箱验证码获取...（最多20次）
2025-08-05 15:44:10 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-05 15:44:10
2025-08-05 15:44:12 线程3：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-05 15:44:12 线程3：[信息] [信息] 🔍 第2次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-05 15:44:12 线程3：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 未发现验证码 (进度: 100%)
2025-08-05 15:44:12 线程3：[信息] [信息] ✅ 第一页未检测到图形验证码，继续流程 (进度: 100%)
2025-08-05 15:44:12 线程3：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-05 15:44:12 线程3：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-05 15:44:12 线程3：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-05 15:44:12 线程3：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-05 15:44:12 线程3：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-05 15:44:12 [信息] [线程3] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-05 15:44:12 线程3：[信息] 已继续
2025-08-05 15:44:12 [信息] [线程3] 等待2秒后开始第一次触发...
2025-08-05 15:44:12 [信息] 线程3已继续
2025-08-05 15:44:13 [信息] [线程2] 第2次触发邮箱验证码获取...（最多20次）
2025-08-05 15:44:13 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-05 15:44:13
2025-08-05 15:44:14 [信息] [线程3] 第1次触发邮箱验证码获取...（最多20次）
2025-08-05 15:44:14 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-05 15:44:14
2025-08-05 15:44:16 [信息] [线程2] 第3次触发邮箱验证码获取...（最多20次）
2025-08-05 15:44:16 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-05 15:44:16
2025-08-05 15:44:16 [信息] [线程2] 邮箱验证码获取成功: 309248，立即停止重复请求
2025-08-05 15:44:16 [信息] [线程2] 已清理请求文件，停止重复触发
2025-08-05 15:44:16 [信息] [线程2] 已清理响应文件
2025-08-05 15:44:16 线程2：[信息] [信息] 验证码获取成功: 309248，正在自动填入... (进度: 100%)
2025-08-05 15:44:16 线程2：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 100%)
2025-08-05 15:44:17 线程2：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 100%)
2025-08-05 15:44:17 [信息] [线程3] 第2次触发邮箱验证码获取...（最多20次）
2025-08-05 15:44:17 线程2：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 100%)
2025-08-05 15:44:17 [信息] 线程2完成第二页事件已处理
2025-08-05 15:44:17 [信息] 线程2完成第二页，开始批量获取手机号码...
2025-08-05 15:44:17 线程2：[信息] [信息] 线程2第二页验证完成，已通知管理器 (进度: 100%)
2025-08-05 15:44:17 [信息] 开始批量获取3个手机号码，服务商: Durian
2025-08-05 15:44:17 [信息] [手机API] 批量获取3个手机号码，URL: https://api.durianrcs.com/out/ext_api/getMobile?name=oneone&ApiKey=NnlaR01xQm9hMlAwRnJDSVB2SG1kQT09&cuy=mx&pid=0209&num=3&noblack=0&serial=2&secret_key=null&vip=null
2025-08-05 15:44:17 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-05 15:44:17
2025-08-05 15:44:20 线程2：[信息] [信息] 等待密码设置页面加载... (进度: 100%)
2025-08-05 15:44:20 线程2：[信息] [信息] 开始填写密码信息... (进度: 100%)
2025-08-05 15:44:20 线程2：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 100%)
2025-08-05 15:44:20 [信息] [线程3] 第3次触发邮箱验证码获取...（最多20次）
2025-08-05 15:44:20 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-05 15:44:20
2025-08-05 15:44:20 线程2：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 100%)
2025-08-05 15:44:20 线程2：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 100%)
2025-08-05 15:44:21 线程2：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 100%)
2025-08-05 15:44:22 [信息] [手机API] 批量获取响应内容: {"code":200,"msg":"Success","data":["+529541850935","+522227583656","+526161373267"]}
2025-08-05 15:44:22 [信息] [手机API] 检测到数组格式，元素数量: 3
2025-08-05 15:44:22 [信息] [手机API] 批量获取成功，获得3个手机号码
2025-08-05 15:44:22 [信息] 线程1分配榴莲手机号码: +529541850935
2025-08-05 15:44:22 [信息] 线程2分配榴莲手机号码: +522227583656
2025-08-05 15:44:22 [信息] 线程3分配榴莲手机号码: +526161373267
2025-08-05 15:44:22 [信息] 榴莲API批量获取手机号码成功，已分配给3个线程
2025-08-05 15:44:22 [信息] 批量获取3个手机号码成功
2025-08-05 15:44:23 [信息] [线程3] 第4次触发邮箱验证码获取...（最多20次）
2025-08-05 15:44:23 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-05 15:44:23
2025-08-05 15:44:24 线程2：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 100%)
2025-08-05 15:44:24 线程2：[信息] [信息] 等待账户类型确认页面加载... (进度: 100%)
2025-08-05 15:44:24 线程2：[信息] [信息] 开始处理账户类型确认... (进度: 100%)
2025-08-05 15:44:26 [信息] [线程3] 第5次触发邮箱验证码获取...（最多20次）
2025-08-05 15:44:26 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-05 15:44:26
2025-08-05 15:44:29 [信息] [线程3] 第6次触发邮箱验证码获取...（最多20次）
2025-08-05 15:44:29 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-05 15:44:29
2025-08-05 15:44:29 [信息] [线程3] 邮箱验证码获取成功: 814661，立即停止重复请求
2025-08-05 15:44:30 [信息] [线程3] 已清理请求文件，停止重复触发
2025-08-05 15:44:30 [信息] [线程3] 已清理响应文件
2025-08-05 15:44:30 线程3：[信息] [信息] 验证码获取成功: 814661，正在自动填入... (进度: 100%)
2025-08-05 15:44:30 线程3：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 100%)
2025-08-05 15:44:30 线程3：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 100%)
2025-08-05 15:44:30 线程3：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 100%)
2025-08-05 15:44:30 [信息] 线程3完成第二页事件已处理
2025-08-05 15:44:30 [信息] 线程3完成第二页，手机号码已获取，无需重复获取
2025-08-05 15:44:30 线程3：[信息] [信息] 线程3第二页验证完成，已通知管理器 (进度: 100%)
2025-08-05 15:44:32 线程2：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 100%)
2025-08-05 15:44:32 线程2：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 100%)
2025-08-05 15:44:33 线程3：[信息] [信息] 等待密码设置页面加载... (进度: 100%)
2025-08-05 15:44:33 线程3：[信息] [信息] 开始填写密码信息... (进度: 100%)
2025-08-05 15:44:33 线程3：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 100%)
2025-08-05 15:44:33 线程3：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 100%)
2025-08-05 15:44:33 线程3：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 100%)
2025-08-05 15:44:34 线程3：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 100%)
2025-08-05 15:44:37 线程3：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 100%)
2025-08-05 15:44:37 线程3：[信息] [信息] 等待账户类型确认页面加载... (进度: 100%)
2025-08-05 15:44:37 线程3：[信息] [信息] 开始处理账户类型确认... (进度: 100%)
2025-08-05 15:44:43 线程3：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 100%)
2025-08-05 15:44:43 线程3：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 100%)
2025-08-05 15:44:52 线程2：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 100%)
2025-08-05 15:44:52 [信息] 线程2获取已分配的榴莲手机号码: +522227583656
2025-08-05 15:44:52 线程2：[信息] [信息] 多线程模式：使用已分配的手机号码 +522227583656 (进度: 100%)
2025-08-05 15:44:53 线程2：[信息] [信息] 数据国家代码为CL，需要选择Chile (进度: 100%)
2025-08-05 15:44:54 线程2：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 100%)
2025-08-05 15:44:56 线程2：[信息] [信息] 已选择国家: Chile (进度: 100%)
2025-08-05 15:44:56 线程2：[信息] [信息] 已成功选择国家: Chile (进度: 100%)
2025-08-05 15:44:56 线程2：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 100%)
2025-08-05 15:44:57 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-05 15:45:01 线程2：[信息] [信息] 已选择国家代码 +52 (进度: 100%)
2025-08-05 15:45:01 线程2：[信息] [信息] 等待后台获取的手机号码结果... (进度: 100%)
2025-08-05 15:45:02 线程2：[信息] [信息] 已自动获取并填入手机号码: +522227583656 (进度: 100%)
2025-08-05 15:45:03 线程2：[信息] [信息] 使用已获取的手机号码: +522227583656（保存本地号码: +522227583656） (进度: 100%)
2025-08-05 15:45:03 线程2：[信息] [信息] 联系信息完成，等待页面加载... (进度: 100%)
2025-08-05 15:45:03 线程3：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 100%)
2025-08-05 15:45:03 [信息] 线程3获取已分配的榴莲手机号码: +526161373267
2025-08-05 15:45:03 线程3：[信息] [信息] 多线程模式：使用已分配的手机号码 +526161373267 (进度: 100%)
2025-08-05 15:45:03 线程3：[信息] [信息] 数据国家代码为CL，需要选择Chile (进度: 100%)
2025-08-05 15:45:04 线程3：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 100%)
2025-08-05 15:45:06 线程3：[信息] [信息] 已选择国家: Chile (进度: 100%)
2025-08-05 15:45:06 线程3：[信息] [信息] 已成功选择国家: Chile (进度: 100%)
2025-08-05 15:45:06 线程3：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 100%)
2025-08-05 15:45:07 线程3：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-05 15:45:07 线程2：[信息] [信息] 进入付款信息页面... (进度: 100%)
2025-08-05 15:45:08 线程2：[信息] [信息] 正在选择月份: September (进度: 100%)
2025-08-05 15:45:08 线程2：[信息] [信息] 已选择月份（标准选项）: September (进度: 100%)
2025-08-05 15:45:09 线程2：[信息] [信息] 正在选择年份: 2026 (进度: 100%)
2025-08-05 15:45:09 线程2：[信息] [信息] 已选择年份（标准选项）: 2026 (进度: 100%)
2025-08-05 15:45:10 线程2：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 100%)
2025-08-05 15:45:10 线程2：[信息] [信息] 开始填写验证码验证页面... (进度: 100%)
2025-08-05 15:45:10 线程2：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 100%)
2025-08-05 15:45:11 线程3：[信息] [信息] 已选择国家代码 +52 (进度: 100%)
2025-08-05 15:45:11 线程3：[信息] [信息] 等待后台获取的手机号码结果... (进度: 100%)
2025-08-05 15:45:11 线程3：[信息] [信息] 已自动获取并填入手机号码: +526161373267 (进度: 100%)
2025-08-05 15:45:12 线程3：[信息] [信息] 使用已获取的手机号码: +526161373267（保存本地号码: +526161373267） (进度: 100%)
2025-08-05 15:45:12 线程3：[信息] [信息] 联系信息完成，等待页面加载... (进度: 100%)
2025-08-05 15:45:16 线程3：[信息] [信息] 进入付款信息页面... (进度: 100%)
2025-08-05 15:45:17 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-05 15:45:17 线程3：[信息] [信息] 正在选择月份: May (进度: 100%)
2025-08-05 15:45:17 线程3：[信息] [信息] 已选择月份（标准选项）: May (进度: 100%)
2025-08-05 15:45:18 线程2：[信息] [信息] 已选择国家代码: +52 (进度: 100%)
2025-08-05 15:45:18 线程2：[信息] [信息] 已清空并重新填写手机号码: +522227583656 (进度: 100%)
2025-08-05 15:45:18 线程2：[信息] [信息] 已点击发送验证码按钮 (进度: 100%)
2025-08-05 15:45:18 线程3：[信息] [信息] 正在选择年份: 2029 (进度: 100%)
2025-08-05 15:45:18 线程3：[信息] [信息] 已选择年份（标准选项）: 2029 (进度: 100%)
2025-08-05 15:45:19 线程3：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 100%)
2025-08-05 15:45:19 线程3：[信息] [信息] 开始填写验证码验证页面... (进度: 100%)
2025-08-05 15:45:19 线程3：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 100%)
2025-08-05 15:45:20 线程2：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 100%)
2025-08-05 15:45:20 线程2：[信息] [信息] ⚠️ 检测到错误信息，开始重试机制... (进度: 100%)
2025-08-05 15:45:20 [信息] 检测到错误信息，开始重试机制
2025-08-05 15:45:20 线程2：[信息] [信息] 🔄 第1次重试发送验证码按钮... (进度: 100%)
2025-08-05 15:45:20 [信息] 第1次重试发送验证码按钮
2025-08-05 15:45:23 线程2：[信息] [信息] ✅ 第1次重试：已点击发送验证码按钮 (进度: 100%)
2025-08-05 15:45:23 [信息] 第1次重试：已点击发送验证码按钮
2025-08-05 15:45:24 线程3：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-05 15:45:25 线程2：[信息] [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']" (进度: 100%)
2025-08-05 15:45:25 [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']"
2025-08-05 15:45:25 线程2：[信息] [信息] ❌ 第1次重试失败：错误信息仍然存在 (进度: 100%)
2025-08-05 15:45:25 [信息] 第1次重试失败：错误信息仍然存在
2025-08-05 15:45:25 线程2：[信息] [信息] 🔄 第2次重试发送验证码按钮... (进度: 100%)
2025-08-05 15:45:25 [信息] 第2次重试发送验证码按钮
2025-08-05 15:45:27 线程3：[信息] [信息] 已选择国家代码: +52 (进度: 100%)
2025-08-05 15:45:27 线程3：[信息] [信息] 已清空并重新填写手机号码: +526161373267 (进度: 100%)
2025-08-05 15:45:27 线程3：[信息] [信息] 已点击发送验证码按钮 (进度: 100%)
2025-08-05 15:45:28 线程2：[信息] [信息] ✅ 第2次重试：已点击发送验证码按钮 (进度: 100%)
2025-08-05 15:45:28 [信息] 第2次重试：已点击发送验证码按钮
2025-08-05 15:45:29 线程3：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 100%)
2025-08-05 15:45:29 线程3：[信息] [信息] ⚠️ 检测到错误信息，开始重试机制... (进度: 100%)
2025-08-05 15:45:29 [信息] 检测到错误信息，开始重试机制
2025-08-05 15:45:29 线程3：[信息] [信息] 🔄 第1次重试发送验证码按钮... (进度: 100%)
2025-08-05 15:45:29 [信息] 第1次重试发送验证码按钮
2025-08-05 15:45:30 线程2：[信息] [信息] ✅ 第2次重试成功：错误信息消失，继续正常流程 (进度: 100%)
2025-08-05 15:45:30 [信息] 第2次重试成功：错误信息消失
2025-08-05 15:45:30 线程2：[信息] [信息] 手机号码自动模式 + 图形验证码自动模式：开始自动处理... (进度: 100%)
2025-08-05 15:45:30 线程2：[信息] [信息] 自动模式：开始处理图形验证码... (进度: 100%)
2025-08-05 15:45:30 线程2：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-05 15:45:32 线程3：[信息] [信息] ✅ 第1次重试：已点击发送验证码按钮 (进度: 100%)
2025-08-05 15:45:32 [信息] 第1次重试：已点击发送验证码按钮
2025-08-05 15:45:33 线程2：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-05 15:45:33 线程2：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-05 15:45:34 线程3：[信息] [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']" (进度: 100%)
2025-08-05 15:45:34 [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']"
2025-08-05 15:45:34 线程3：[信息] [信息] ❌ 第1次重试失败：错误信息仍然存在 (进度: 100%)
2025-08-05 15:45:34 [信息] 第1次重试失败：错误信息仍然存在
2025-08-05 15:45:34 线程3：[信息] [信息] 🔄 第2次重试发送验证码按钮... (进度: 100%)
2025-08-05 15:45:34 [信息] 第2次重试发送验证码按钮
2025-08-05 15:45:37 线程2：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 34627 字节 (进度: 100%)
2025-08-05 15:45:37 线程2：[信息] [信息] ✅ 图片验证通过：201x70px，34627字节，复杂度符合要求 (进度: 100%)
2025-08-05 15:45:37 线程2：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-05 15:45:37 线程3：[信息] [信息] ✅ 第2次重试：已点击发送验证码按钮 (进度: 100%)
2025-08-05 15:45:37 [信息] 第2次重试：已点击发送验证码按钮
2025-08-05 15:45:38 线程2：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"5m76mx"},"taskId":"2e01fd26-71d0-11f0-9cde-a646062fd64a"} (进度: 100%)
2025-08-05 15:45:38 线程2：[信息] [信息] 第六页第1次识别结果: 5m76mx → 转换为小写: 5m76mx (进度: 100%)
2025-08-05 15:45:38 线程2：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-05 15:45:38 线程2：[信息] [信息] 第六页已填入验证码: 5m76mx (进度: 100%)
2025-08-05 15:45:39 线程2：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-05 15:45:39 线程3：[信息] [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']" (进度: 100%)
2025-08-05 15:45:39 [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']"
2025-08-05 15:45:39 线程3：[信息] [信息] ✅ 第2次重试成功：错误信息消失，继续正常流程 (进度: 100%)
2025-08-05 15:45:39 [信息] 第2次重试成功：错误信息消失
2025-08-05 15:45:39 线程3：[信息] [信息] 手机号码自动模式 + 图形验证码自动模式：开始自动处理... (进度: 100%)
2025-08-05 15:45:39 线程3：[信息] [信息] 自动模式：开始处理图形验证码... (进度: 100%)
2025-08-05 15:45:39 线程3：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-05 15:45:42 线程2：[信息] [信息] 第1次图形验证码识别成功 (进度: 100%)
2025-08-05 15:45:42 线程2：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-08-05 15:45:42 线程3：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-05 15:45:42 线程3：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-05 15:45:45 线程2：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-08-05 15:45:46 线程3：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 35037 字节 (进度: 100%)
2025-08-05 15:45:46 线程3：[信息] [信息] ✅ 图片验证通过：201x70px，35037字节，复杂度符合要求 (进度: 100%)
2025-08-05 15:45:46 线程3：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-05 15:45:47 线程3：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"txp7tx"},"taskId":"3351e624-71d0-11f0-bf32-6e28fd6820d3"} (进度: 100%)
2025-08-05 15:45:47 线程3：[信息] [信息] 第六页第1次识别结果: txp7tx → 转换为小写: txp7tx (进度: 100%)
2025-08-05 15:45:47 线程3：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-05 15:45:47 线程3：[信息] [信息] 第六页已填入验证码: txp7tx (进度: 100%)
2025-08-05 15:45:48 线程3：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-05 15:45:48 线程2：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-05 15:45:48 线程2：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-05 15:45:48 线程2：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-05 15:45:48 线程2：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-08-05 15:45:51 线程3：[信息] [信息] 第1次图形验证码识别成功 (进度: 100%)
2025-08-05 15:45:51 线程3：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-08-05 15:45:53 线程2：[信息] [信息] 线程2开始独立获取验证码... (进度: 100%)
2025-08-05 15:45:53 线程2：[信息] [信息] 线程2开始获取验证码，最多尝试8次，每次间隔8秒 (进度: 100%)
2025-08-05 15:45:53 线程2：[信息] [信息] 线程2第1次尝试获取验证码...（剩余7次尝试） (进度: 100%)
2025-08-05 15:45:53 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-05 15:45:53 线程2：[信息] [信息] 线程2验证码获取成功: 0426 (进度: 100%)
2025-08-05 15:45:53 [信息] 线程2手机号码已加入释放队列: +522227583656 (原因: 获取验证码成功)
2025-08-05 15:45:53 线程2：[信息] [信息] 线程2验证码获取成功: 0426，立即填入验证码... (进度: 100%)
2025-08-05 15:45:53 线程2：[信息] [信息] 第七页：找到验证码输入框 (Verify code) (进度: 100%)
2025-08-05 15:45:53 线程2：[信息] [信息] 线程2已自动填入手机验证码: 0426 (进度: 100%)
2025-08-05 15:45:54 线程3：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-08-05 15:45:54 线程2：[信息] [信息] 线程2正在自动点击Continue按钮... (进度: 100%)
2025-08-05 15:45:54 线程2：[信息] [信息] 线程2手机验证码验证完成，继续执行后续步骤... (进度: 100%)
2025-08-05 15:45:57 线程3：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-05 15:45:57 线程3：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-05 15:45:57 线程3：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-05 15:45:57 线程3：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-08-05 15:45:58 线程2：[信息] [信息] 验证码验证完成，等待页面加载... (进度: 100%)
2025-08-05 15:45:58 线程2：[信息] [信息] 已点击完成注册按钮，正在处理... (进度: 100%)
2025-08-05 15:45:59 线程2：[信息] [信息] 等待完成注册页面加载... (进度: 100%)
2025-08-05 15:45:59 线程2：[信息] [信息] 自动模式验证完成，API手机号码已加入黑名单: 加入黑名单成功 (进度: 100%)
2025-08-05 15:46:02 线程3：[信息] [信息] 线程3开始独立获取验证码... (进度: 100%)
2025-08-05 15:46:02 线程3：[信息] [信息] 线程3开始获取验证码，最多尝试8次，每次间隔8秒 (进度: 100%)
2025-08-05 15:46:02 线程3：[信息] [信息] 线程3第1次尝试获取验证码...（剩余7次尝试） (进度: 100%)
2025-08-05 15:46:02 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-05 15:46:02 线程2：[信息] [信息] 完成注册页面加载完成 (进度: 100%)
2025-08-05 15:46:02 线程2：[信息] [信息] 🔍 正在查找'Go to the AWS Management Console'链接... (进度: 100%)
2025-08-05 15:46:02 线程3：[信息] [信息] 线程3验证码获取成功: 3037 (进度: 100%)
2025-08-05 15:46:02 [信息] 线程3手机号码已加入释放队列: +526161373267 (原因: 获取验证码成功)
2025-08-05 15:46:02 线程3：[信息] [信息] 线程3验证码获取成功: 3037，立即填入验证码... (进度: 100%)
2025-08-05 15:46:02 线程3：[信息] [信息] 第七页：找到验证码输入框 (Verify code) (进度: 100%)
2025-08-05 15:46:02 线程3：[信息] [信息] 线程3已自动填入手机验证码: 3037 (进度: 100%)
2025-08-05 15:46:03 线程2：[信息] [信息] 🖱️ 已点击跳转管理控制台链接 (进度: 100%)
2025-08-05 15:46:03 线程2：[信息] [信息] 🔗 通过导航进入IAM安全凭证页面... (进度: 100%)
2025-08-05 15:46:03 线程2：[信息] [信息] ⏳ 等待管理控制台页面加载，智能检测'更多'按钮（超时20秒）... (进度: 100%)
2025-08-05 15:46:03 线程3：[信息] [信息] 线程3正在自动点击Continue按钮... (进度: 100%)
2025-08-05 15:46:03 线程3：[信息] [信息] 线程3手机验证码验证完成，继续执行后续步骤... (进度: 100%)
2025-08-05 15:46:06 线程1：[信息] [信息] 所有自动线程已停止 (进度: 0%)
2025-08-05 15:46:06 线程1：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 0%)
2025-08-05 15:46:06 线程1：[信息] 已暂停
2025-08-05 15:46:06 [信息] 线程1已暂停
2025-08-05 15:46:06 [信息] 线程1已暂停
2025-08-05 15:46:07 线程3：[信息] [信息] 验证码验证完成，等待页面加载... (进度: 100%)
2025-08-05 15:46:07 线程3：[信息] [信息] 已点击完成注册按钮，正在处理... (进度: 100%)
2025-08-05 15:46:08 线程3：[信息] [信息] 自动模式验证完成，API手机号码已加入黑名单: 加入黑名单成功 (进度: 100%)
2025-08-05 15:46:08 线程1：[信息] [信息] ❌ 按钮匹配失败: Target page, context or browser has been closed (进度: 0%)
2025-08-05 15:46:08 线程1：[信息] [信息] ⚠️ 智能检测失败，启用详细页面分析... (进度: 0%)
2025-08-05 15:46:08 线程1：[信息] [信息] 🔬 执行详细页面分析... (进度: 0%)
2025-08-05 15:46:08 线程1：[信息] [信息] ❌ 详细分析失败: Target page, context or browser has been closed (进度: 0%)
2025-08-05 15:46:08 线程1：[信息] [信息] 智能检测到当前在第1页，开始智能处理... (进度: 0%)
2025-08-05 15:46:08 线程1：[信息] [信息] 🔍 等待第一页加载完成... (进度: 15%)
2025-08-05 15:46:08 线程1：[信息] [信息] ❌ 第一页执行异常: Target page, context or browser has been closed (进度: 15%)
2025-08-05 15:46:08 线程1：[信息] [信息] 继续注册失败: 第一页执行失败: Target page, context or browser has been closed (进度: 15%)
2025-08-05 15:46:08 线程1：[信息] 已继续
2025-08-05 15:46:08 [信息] 线程1已继续
2025-08-05 15:46:08 [信息] 继续了 3 个可继续的线程
2025-08-05 15:46:08 线程3：[信息] [信息] 等待完成注册页面加载... (进度: 100%)
2025-08-05 15:46:11 线程3：[信息] [信息] 完成注册页面加载完成 (进度: 100%)
2025-08-05 15:46:11 线程3：[信息] [信息] 🔍 正在查找'Go to the AWS Management Console'链接... (进度: 100%)
2025-08-05 15:46:16 [信息] 定时检查发现2个待释放手机号码，开始批量释放
2025-08-05 15:46:16 [信息] 开始释放2个手机号码
2025-08-05 15:46:16 [信息] [手机API] 开始批量释放2个手机号码
2025-08-05 15:46:16 [信息] [手机API] 释放手机号码: +522227583656
2025-08-05 15:46:17 [信息] [手机API] 手机号码释放成功: +522227583656
2025-08-05 15:46:17 线程3：[信息] [信息] 🖱️ 已点击跳转管理控制台链接 (进度: 100%)
2025-08-05 15:46:17 线程3：[信息] [信息] 🔗 通过导航进入IAM安全凭证页面... (进度: 100%)
2025-08-05 15:46:17 线程3：[信息] [信息] ⏳ 等待管理控制台页面加载，智能检测'更多'按钮（超时20秒）... (进度: 100%)
2025-08-05 15:46:17 [信息] [手机API] 释放手机号码: +526161373267
2025-08-05 15:46:17 [信息] [手机API] 手机号码释放成功: +526161373267
2025-08-05 15:46:19 [信息] [手机API] 批量释放完成: 成功2个, 失败0个
2025-08-05 15:46:19 [信息] 定时批量释放完成: 批量释放完成: 成功2个, 失败0个
2025-08-05 15:46:20 线程2：[信息] [信息] ✅ 找到'更多'按钮，页面加载完成 (进度: 100%)
2025-08-05 15:46:20 线程2：[信息] [信息] 🖱️ 正在点击更多按钮... (进度: 100%)
2025-08-05 15:46:21 线程2：[信息] [信息] ✅ 成功点击更多按钮 (进度: 100%)
2025-08-05 15:46:21 [信息] 成功点击更多按钮
2025-08-05 15:46:22 线程2：[信息] [信息] 🖱️ 正在点击账户信息按钮... (进度: 100%)
2025-08-05 15:46:22 线程2：[信息] [信息] ✅ 成功点击账户信息按钮 (进度: 100%)
2025-08-05 15:46:22 [信息] 成功点击账户信息按钮
2025-08-05 15:46:23 线程2：[信息] [信息] 🖱️ 正在点击'安全凭证'菜单项... (进度: 100%)
2025-08-05 15:46:23 线程2：[信息] [信息] 🔍 正在定位'安全凭证'链接... (进度: 100%)
2025-08-05 15:46:23 线程2：[信息] [信息] ✅ 成功定位到'安全凭证'链接 (进度: 100%)
2025-08-05 15:46:23 [信息] 成功定位到'安全凭证'链接
2025-08-05 15:46:29 线程2：[信息] [信息] ✅ 成功点击'安全凭证'菜单项 (进度: 100%)
2025-08-05 15:46:29 [信息] 成功点击'安全凭证'链接
2025-08-05 15:46:29 线程2：[信息] [信息] ⏳ 等待IAM安全凭证页面加载（20秒超时，循环检测账单问题和创建密钥按钮）... (进度: 100%)
2025-08-05 15:46:30 线程3：[信息] [信息] ✅ 找到'更多'按钮，页面加载完成 (进度: 100%)
2025-08-05 15:46:30 线程3：[信息] [信息] 🖱️ 正在点击更多按钮... (进度: 100%)
2025-08-05 15:46:31 线程3：[信息] [信息] ✅ 成功点击更多按钮 (进度: 100%)
2025-08-05 15:46:31 [信息] 成功点击更多按钮
2025-08-05 15:46:32 线程3：[信息] [信息] 🖱️ 正在点击账户信息按钮... (进度: 100%)
2025-08-05 15:46:32 线程3：[信息] [信息] ✅ 成功点击账户信息按钮 (进度: 100%)
2025-08-05 15:46:32 [信息] 成功点击账户信息按钮
2025-08-05 15:46:33 线程3：[信息] [信息] 🖱️ 正在点击'安全凭证'菜单项... (进度: 100%)
2025-08-05 15:46:33 线程3：[信息] [信息] 🔍 正在定位'安全凭证'链接... (进度: 100%)
2025-08-05 15:46:33 线程3：[信息] [信息] ✅ 成功定位到'安全凭证'链接 (进度: 100%)
2025-08-05 15:46:33 [信息] 成功定位到'安全凭证'链接
2025-08-05 15:46:41 线程3：[信息] [信息] ✅ 成功点击'安全凭证'菜单项 (进度: 100%)
2025-08-05 15:46:41 [信息] 成功点击'安全凭证'链接
2025-08-05 15:46:41 线程3：[信息] [信息] ⏳ 等待IAM安全凭证页面加载（20秒超时，循环检测账单问题和创建密钥按钮）... (进度: 100%)
2025-08-05 15:46:49 线程2：[信息] [信息] ✅ 找到'创建访问密钥'按钮，页面加载完成 (进度: 100%)
2025-08-05 15:46:49 线程2：[信息] [信息] ✅ 页面状态正常，找到创建密钥按钮，开始密钥提取流程 (进度: 100%)
2025-08-05 15:46:49 线程2：[信息] [信息] 🔍 设置页面缩放为50%... (进度: 100%)
2025-08-05 15:46:51 线程2：[信息] [信息] ✅ 页面缩放设置完成 (进度: 100%)
2025-08-05 15:46:51 [信息] 页面缩放设置为50%完成
2025-08-05 15:46:51 线程2：[信息] [信息] 🔍 正在查找并点击'下一步'按钮关闭向导... (进度: 100%)
2025-08-05 15:46:51 [信息] 开始查找并点击'下一步'按钮关闭向导
2025-08-05 15:46:51 线程2：[信息] [信息] ℹ️ 未检测到向导元素，向导可能已自动关闭 (进度: 100%)
2025-08-05 15:46:51 [信息] 未检测到向导元素，向导可能已自动关闭
2025-08-05 15:46:51 线程2：[信息] [信息] 🔑 第四阶段：开始创建和复制访问密钥 (进度: 100%)
2025-08-05 15:46:51 [信息] 开始创建和复制访问密钥
2025-08-05 15:46:51 线程2：[信息] [信息] 🔑 正在点击'创建访问密钥'按钮... (进度: 100%)
2025-08-05 15:46:51 线程2：[信息] [信息] 🔍 正在定位'创建访问密钥'按钮... (进度: 100%)
2025-08-05 15:46:51 线程2：[信息] [信息] ✅ 成功定位到'创建访问密钥'按钮 (进度: 100%)
2025-08-05 15:46:51 [信息] 成功定位到'创建访问密钥'按钮
2025-08-05 15:46:51 线程2：[信息] [信息] ✅ 成功点击'创建访问密钥'按钮 (进度: 100%)
2025-08-05 15:46:51 [信息] 成功点击'创建访问密钥'按钮
2025-08-05 15:46:53 线程2：[信息] [信息] ☑️ 正在勾选确认复选框... (进度: 100%)
2025-08-05 15:46:58 线程2：[信息] [信息] ⚠️ id属性定位失败，使用CSS类定位 (进度: 100%)
2025-08-05 15:46:58 [信息] id属性定位失败，使用CSS类定位
2025-08-05 15:47:00 线程3：[信息] [信息] ✅ 找到'创建访问密钥'按钮，页面加载完成 (进度: 100%)
2025-08-05 15:47:00 线程3：[信息] [信息] ✅ 页面状态正常，找到创建密钥按钮，开始密钥提取流程 (进度: 100%)
2025-08-05 15:47:00 线程3：[信息] [信息] 🔍 设置页面缩放为50%... (进度: 100%)
2025-08-05 15:47:00 线程2：[信息] [信息] ✅ 成功勾选确认复选框 (进度: 100%)
2025-08-05 15:47:00 [信息] 成功勾选确认复选框
2025-08-05 15:47:01 线程3：[信息] [信息] ✅ 页面缩放设置完成 (进度: 100%)
2025-08-05 15:47:01 [信息] 页面缩放设置为50%完成
2025-08-05 15:47:01 线程2：[信息] [信息] 🔑 正在点击'创建访问密钥'确认按钮... (进度: 100%)
2025-08-05 15:47:01 线程3：[信息] [信息] 🔍 正在查找并点击'下一步'按钮关闭向导... (进度: 100%)
2025-08-05 15:47:01 [信息] 开始查找并点击'下一步'按钮关闭向导
2025-08-05 15:47:01 线程3：[信息] [信息] ✅ 找到可见的下一步按钮，使用选择器: Locator(button[data-testid='awsc-nav-services-tooltip-confirm-button']) (进度: 100%)
2025-08-05 15:47:02 [信息] 找到可见的下一步按钮，使用选择器: Locator(button[data-testid='awsc-nav-services-tooltip-confirm-button'])
2025-08-05 15:47:02 线程3：[信息] [信息] ✅ 第一次点击'下一步'按钮完成 (选择器: Locator(button[data-testid='awsc-nav-services-tooltip-confirm-button'])) (进度: 100%)
2025-08-05 15:47:02 [信息] 第一次点击'下一步'按钮完成 (选择器: Locator(button[data-testid='awsc-nav-services-tooltip-confirm-button']))
2025-08-05 15:47:02 线程2：[信息] [信息] ✅ 成功点击'创建访问密钥'确认按钮 (进度: 100%)
2025-08-05 15:47:02 [信息] 成功点击'创建访问密钥'确认按钮
2025-08-05 15:47:03 线程3：[信息] [信息] ℹ️ 第二次点击时按钮已消失，向导已关闭 (进度: 100%)
2025-08-05 15:47:03 [信息] 第二次点击时按钮已消失，向导已关闭
2025-08-05 15:47:03 线程3：[信息] [信息] ✅ '下一步'按钮点击流程完成 (进度: 100%)
2025-08-05 15:47:03 [信息] '下一步'按钮点击流程完成
2025-08-05 15:47:03 线程3：[信息] [信息] 🔑 第四阶段：开始创建和复制访问密钥 (进度: 100%)
2025-08-05 15:47:03 [信息] 开始创建和复制访问密钥
2025-08-05 15:47:03 线程3：[信息] [信息] 🔑 正在点击'创建访问密钥'按钮... (进度: 100%)
2025-08-05 15:47:03 线程3：[信息] [信息] 🔍 正在定位'创建访问密钥'按钮... (进度: 100%)
2025-08-05 15:47:03 线程3：[信息] [信息] ✅ 成功定位到'创建访问密钥'按钮 (进度: 100%)
2025-08-05 15:47:03 [信息] 成功定位到'创建访问密钥'按钮
2025-08-05 15:47:03 线程3：[信息] [信息] ✅ 成功点击'创建访问密钥'按钮 (进度: 100%)
2025-08-05 15:47:03 [信息] 成功点击'创建访问密钥'按钮
2025-08-05 15:47:05 线程2：[信息] [信息] 📋 开始复制访问密钥... (进度: 100%)
2025-08-05 15:47:05 [信息] 开始复制访问密钥
2025-08-05 15:47:05 线程3：[信息] [信息] ☑️ 正在勾选确认复选框... (进度: 100%)
2025-08-05 15:47:07 线程3：[信息] [信息] ✅ 使用id属性定位到确认复选框 (进度: 100%)
2025-08-05 15:47:07 [信息] 使用id属性定位到确认复选框
2025-08-05 15:47:07 线程3：[信息] [信息] ✅ 成功勾选确认复选框 (进度: 100%)
2025-08-05 15:47:07 [信息] 成功勾选确认复选框
2025-08-05 15:47:07 线程2：[信息] [信息] 🔍 方法2找到 2 个单元格 (进度: 100%)
2025-08-05 15:47:07 [信息] 方法2找到 2 个单元格
2025-08-05 15:47:07 线程2：[信息] [信息] 🔍 总共找到 2 个单元格，开始分析... (进度: 100%)
2025-08-05 15:47:07 [信息] 总共找到 2 个单元格，开始分析
2025-08-05 15:47:07 [信息] 单元格[0]: '********************'
2025-08-05 15:47:07 [信息] 单元格[1]: '***************Mostrar'
2025-08-05 15:47:07 线程2：[信息] [信息] 📋 正在查找并复制访问密钥... (进度: 100%)
2025-08-05 15:47:07 线程2：[信息] [信息] 🔍 解决向导阻挡... (进度: 100%)
2025-08-05 15:47:07 线程2：[信息] [信息] 🔍 正在查找并点击'下一步'按钮关闭向导... (进度: 100%)
2025-08-05 15:47:07 [信息] 开始查找并点击'下一步'按钮关闭向导
2025-08-05 15:47:07 线程2：[信息] [信息] ✅ 找到可见的下一步按钮，使用选择器: Locator(button[data-testid='awsc-nav-services-tooltip-confirm-button']) (进度: 100%)
2025-08-05 15:47:07 [信息] 找到可见的下一步按钮，使用选择器: Locator(button[data-testid='awsc-nav-services-tooltip-confirm-button'])
2025-08-05 15:47:07 线程2：[信息] [信息] ✅ 第一次点击'下一步'按钮完成 (选择器: Locator(button[data-testid='awsc-nav-services-tooltip-confirm-button'])) (进度: 100%)
2025-08-05 15:47:07 [信息] 第一次点击'下一步'按钮完成 (选择器: Locator(button[data-testid='awsc-nav-services-tooltip-confirm-button']))
2025-08-05 15:47:08 线程3：[信息] [信息] 🔑 正在点击'创建访问密钥'确认按钮... (进度: 100%)
2025-08-05 15:47:08 线程3：[信息] [信息] ✅ 成功点击'创建访问密钥'确认按钮 (进度: 100%)
2025-08-05 15:47:08 [信息] 成功点击'创建访问密钥'确认按钮
2025-08-05 15:47:08 线程2：[信息] [信息] ℹ️ 第二次点击时按钮已消失，向导已关闭 (进度: 100%)
2025-08-05 15:47:08 [信息] 第二次点击时按钮已消失，向导已关闭
2025-08-05 15:47:08 线程2：[信息] [信息] ✅ '下一步'按钮点击流程完成 (进度: 100%)
2025-08-05 15:47:08 [信息] '下一步'按钮点击流程完成
2025-08-05 15:47:08 线程2：[信息] [信息] ✅ 找到访问密钥: ******************** (进度: 100%)
2025-08-05 15:47:08 [信息] 找到访问密钥: ********************
2025-08-05 15:47:11 线程3：[信息] [信息] 📋 开始复制访问密钥... (进度: 100%)
2025-08-05 15:47:11 [信息] 开始复制访问密钥
2025-08-05 15:47:13 线程2：[信息] [信息] ✅ 方法1成功点击访问密钥复制按钮 (进度: 100%)
2025-08-05 15:47:13 [信息] 方法1成功点击访问密钥复制按钮
2025-08-05 15:47:13 线程3：[信息] [信息] 🔍 方法2找到 2 个单元格 (进度: 100%)
2025-08-05 15:47:13 [信息] 方法2找到 2 个单元格
2025-08-05 15:47:13 线程3：[信息] [信息] 🔍 总共找到 2 个单元格，开始分析... (进度: 100%)
2025-08-05 15:47:13 [信息] 总共找到 2 个单元格，开始分析
2025-08-05 15:47:13 [信息] 单元格[0]: '********************'
2025-08-05 15:47:13 [信息] 单元格[1]: '***************Mostrar'
2025-08-05 15:47:13 线程3：[信息] [信息] 📋 正在查找并复制访问密钥... (进度: 100%)
2025-08-05 15:47:13 线程3：[信息] [信息] 🔍 解决向导阻挡... (进度: 100%)
2025-08-05 15:47:13 线程3：[信息] [信息] 🔍 正在查找并点击'下一步'按钮关闭向导... (进度: 100%)
2025-08-05 15:47:13 [信息] 开始查找并点击'下一步'按钮关闭向导
2025-08-05 15:47:13 线程3：[信息] [信息] ℹ️ 未检测到向导元素，向导可能已自动关闭 (进度: 100%)
2025-08-05 15:47:13 [信息] 未检测到向导元素，向导可能已自动关闭
2025-08-05 15:47:13 线程3：[信息] [信息] ✅ 找到访问密钥: ******************** (进度: 100%)
2025-08-05 15:47:13 [信息] 找到访问密钥: ********************
2025-08-05 15:47:13 线程3：[信息] [信息] ✅ 方法1成功点击访问密钥复制按钮 (进度: 100%)
2025-08-05 15:47:13 [信息] 方法1成功点击访问密钥复制按钮
2025-08-05 15:47:14 线程2：[信息] [信息] 📋 正在查找并复制秘密访问密钥... (进度: 100%)
2025-08-05 15:47:14 线程2：[信息] [信息] ✅ 找到秘密访问密钥: ***************Mostrar (进度: 100%)
2025-08-05 15:47:14 [信息] 找到秘密访问密钥: ***************Mostrar
2025-08-05 15:47:14 线程2：[信息] [信息] 🔍 解决向导阻挡... (进度: 100%)
2025-08-05 15:47:14 线程2：[信息] [信息] 🔍 正在查找并点击'下一步'按钮关闭向导... (进度: 100%)
2025-08-05 15:47:14 [信息] 开始查找并点击'下一步'按钮关闭向导
2025-08-05 15:47:14 线程2：[信息] [信息] ℹ️ 未检测到向导元素，向导可能已自动关闭 (进度: 100%)
2025-08-05 15:47:14 [信息] 未检测到向导元素，向导可能已自动关闭
2025-08-05 15:47:14 线程2：[信息] [信息] 🔍 检测到隐藏的秘密访问密钥，尝试点击显示按钮... (进度: 100%)
2025-08-05 15:47:14 [信息] 检测到隐藏的秘密访问密钥，尝试点击显示按钮
2025-08-05 15:47:14 线程2：[信息] [信息] ✅ 使用TestId定位到显示按钮 (进度: 100%)
2025-08-05 15:47:14 [信息] 使用TestId定位到显示按钮
2025-08-05 15:47:14 线程3：[信息] [信息] 📋 正在查找并复制秘密访问密钥... (进度: 100%)
2025-08-05 15:47:14 线程3：[信息] [信息] ✅ 找到秘密访问密钥: ***************Mostrar (进度: 100%)
2025-08-05 15:47:14 [信息] 找到秘密访问密钥: ***************Mostrar
2025-08-05 15:47:14 线程3：[信息] [信息] 🔍 解决向导阻挡... (进度: 100%)
2025-08-05 15:47:14 线程3：[信息] [信息] 🔍 正在查找并点击'下一步'按钮关闭向导... (进度: 100%)
2025-08-05 15:47:14 [信息] 开始查找并点击'下一步'按钮关闭向导
2025-08-05 15:47:14 线程3：[信息] [信息] ℹ️ 未检测到向导元素，向导可能已自动关闭 (进度: 100%)
2025-08-05 15:47:14 [信息] 未检测到向导元素，向导可能已自动关闭
2025-08-05 15:47:14 线程3：[信息] [信息] 🔍 检测到隐藏的秘密访问密钥，尝试点击显示按钮... (进度: 100%)
2025-08-05 15:47:14 [信息] 检测到隐藏的秘密访问密钥，尝试点击显示按钮
2025-08-05 15:47:14 线程3：[信息] [信息] ✅ 使用TestId定位到显示按钮 (进度: 100%)
2025-08-05 15:47:14 [信息] 使用TestId定位到显示按钮
2025-08-05 15:47:15 线程2：[信息] [信息] ✅ 显示按钮点击成功，新文本: BnTlIXhDAMVbd/e4JCOQiJcSrsRuIpZLsVZ08KBkOcultar (进度: 100%)
2025-08-05 15:47:15 [信息] 显示按钮点击成功，新文本: BnTlIXhDAMVbd/e4JCOQiJcSrsRuIpZLsVZ08KBkOcultar
2025-08-05 15:47:15 线程2：[信息] [信息] ✅ 直接从显示文本提取秘密访问密钥: BnTlIXhDAMVbd/e4JCOQiJcSrsRuIpZLsVZ08KBkOcultar (进度: 100%)
2025-08-05 15:47:15 [信息] 直接从显示文本提取秘密访问密钥: BnTlIXhDAMVbd/e4JCOQiJcSrsRuIpZLsVZ08KBkOcultar
2025-08-05 15:47:15 线程2：[信息] [信息] ✅ 访问密钥复制完成 (进度: 100%)
2025-08-05 15:47:15 [信息] 访问密钥复制完成 - AccessKey: ********************, SecretKey: BnTlIXhDAMVbd/e4JCOQiJcSrsRuIpZLsVZ08KBk
2025-08-05 15:47:16 线程3：[信息] [信息] ✅ 显示按钮点击成功，新文本: M7iiiMdXEPUJ3/3m06JDNOT8Vv6r1X1tw/M/weItOcultar (进度: 100%)
2025-08-05 15:47:16 [信息] 显示按钮点击成功，新文本: M7iiiMdXEPUJ3/3m06JDNOT8Vv6r1X1tw/M/weItOcultar
2025-08-05 15:47:16 线程3：[信息] [信息] ✅ 直接从显示文本提取秘密访问密钥: M7iiiMdXEPUJ3/3m06JDNOT8Vv6r1X1tw/M/weItOcultar (进度: 100%)
2025-08-05 15:47:16 [信息] 直接从显示文本提取秘密访问密钥: M7iiiMdXEPUJ3/3m06JDNOT8Vv6r1X1tw/M/weItOcultar
2025-08-05 15:47:16 线程3：[信息] [信息] ✅ 访问密钥复制完成 (进度: 100%)
2025-08-05 15:47:16 [信息] 访问密钥复制完成 - AccessKey: ********************, SecretKey: M7iiiMdXEPUJ3/3m06JDNOT8Vv6r1X1tw/M/weIt
2025-08-05 15:47:16 线程2：[信息] [信息] 🖱️ 正在点击'已完成'按钮... (进度: 100%)
2025-08-05 15:47:16 线程2：[信息] [信息] ✅ 成功点击'已完成'按钮 (进度: 100%)
2025-08-05 15:47:16 [信息] 成功点击'已完成'按钮
2025-08-05 15:47:16 线程2：[信息] [信息] ✅ 密钥已保存到数据对象 - AccessKey: ********************, SecretKey: BnTlIXhDAMVbd/e4JCOQiJcSrsRuIpZLsVZ08KBk (进度: 100%)
2025-08-05 15:47:16 [信息] 密钥已保存到数据对象 - AccessKey: ********************, SecretKey: BnTlIXhDAMVbd/e4JCOQiJcSrsRuIpZLsVZ08KBk
2025-08-05 15:47:17 线程3：[信息] [信息] 🖱️ 正在点击'已完成'按钮... (进度: 100%)
2025-08-05 15:47:18 线程2：[信息] [信息] 🖱️ 正在点击'继续'按钮... (进度: 100%)
2025-08-05 15:47:18 线程3：[信息] [信息] ✅ 成功点击'已完成'按钮 (进度: 100%)
2025-08-05 15:47:18 [信息] 成功点击'已完成'按钮
2025-08-05 15:47:18 线程3：[信息] [信息] ✅ 密钥已保存到数据对象 - AccessKey: ********************, SecretKey: M7iiiMdXEPUJ3/3m06JDNOT8Vv6r1X1tw/M/weIt (进度: 100%)
2025-08-05 15:47:18 [信息] 密钥已保存到数据对象 - AccessKey: ********************, SecretKey: M7iiiMdXEPUJ3/3m06JDNOT8Vv6r1X1tw/M/weIt
2025-08-05 15:47:19 线程3：[信息] [信息] 🖱️ 正在点击'继续'按钮... (进度: 100%)
2025-08-05 15:47:21 线程2：[信息] [信息] ❌ 点击'继续'按钮失败: Timeout 3000ms exceeded.
Call log:
  - waiting for Locator("button[data-awsui-analytics*='navigate']") to be visible (进度: 100%)
2025-08-05 15:47:21 [信息] 点击'继续'按钮失败: Timeout 3000ms exceeded.
Call log:
  - waiting for Locator("button[data-awsui-analytics*='navigate']") to be visible
2025-08-05 15:47:21 线程2：[信息] [信息] 🔐 开始设置MFA设备... (进度: 100%)
2025-08-05 15:47:21 [信息] 开始设置MFA设备
2025-08-05 15:47:21 线程2：[信息] [信息] 🖱️ 正在点击'分配 MFA 设备'按钮... (进度: 100%)
2025-08-05 15:47:21 线程2：[信息] [信息] ✅ 成功点击'分配 MFA 设备'按钮 (进度: 100%)
2025-08-05 15:47:21 [信息] 成功点击'分配 MFA 设备'按钮
2025-08-05 15:47:21 线程2：[信息] [信息] ⏳ 等待MFA设备设置页面加载（20秒超时）... (进度: 100%)
2025-08-05 15:47:22 线程3：[信息] [信息] ❌ 点击'继续'按钮失败: Timeout 3000ms exceeded.
Call log:
  - waiting for Locator("button[data-awsui-analytics*='navigate']") to be visible (进度: 100%)
2025-08-05 15:47:22 [信息] 点击'继续'按钮失败: Timeout 3000ms exceeded.
Call log:
  - waiting for Locator("button[data-awsui-analytics*='navigate']") to be visible
2025-08-05 15:47:22 线程3：[信息] [信息] 🔐 开始设置MFA设备... (进度: 100%)
2025-08-05 15:47:22 [信息] 开始设置MFA设备
2025-08-05 15:47:22 线程3：[信息] [信息] 🖱️ 正在点击'分配 MFA 设备'按钮... (进度: 100%)
2025-08-05 15:47:22 线程3：[信息] [信息] ✅ 成功点击'分配 MFA 设备'按钮 (进度: 100%)
2025-08-05 15:47:22 [信息] 成功点击'分配 MFA 设备'按钮
2025-08-05 15:47:22 线程3：[信息] [信息] ⏳ 等待MFA设备设置页面加载（20秒超时）... (进度: 100%)
2025-08-05 15:47:25 线程2：[信息] [信息] ✅ 通过第一个文本输入框找到设备名称输入框，页面加载完成 (进度: 100%)
2025-08-05 15:47:25 [信息] 通过第一个文本输入框找到设备名称输入框
2025-08-05 15:47:25 线程2：[信息] [信息] 📝 正在输入设备名称... (进度: 100%)
2025-08-05 15:47:25 线程2：[信息] [信息] ✅ 通过第一个文本输入框定位到设备名称输入框 (进度: 100%)
2025-08-05 15:47:25 线程2：[信息] [信息] ✅ 成功输入设备名称: joannkey (进度: 100%)
2025-08-05 15:47:25 [信息] 成功输入设备名称: joannkey
2025-08-05 15:47:25 线程2：[信息] [信息] ☑️ 正在选择'身份验证器应用程序'... (进度: 100%)
2025-08-05 15:47:25 线程2：[信息] [信息] ✅ 成功选择'身份验证器应用程序' (进度: 100%)
2025-08-05 15:47:25 [信息] 成功选择'身份验证器应用程序'
2025-08-05 15:47:25 线程2：[信息] [信息] 🖱️ 正在点击'下一步'按钮... (进度: 100%)
2025-08-05 15:47:26 线程2：[信息] [信息] ✅ 成功点击'下一步'按钮 (进度: 100%)
2025-08-05 15:47:26 [信息] 成功点击'下一步'按钮
2025-08-05 15:47:26 线程2：[信息] [信息] ⏳ 等待MFA密钥页面加载（20秒超时）... (进度: 100%)
2025-08-05 15:47:26 线程3：[信息] [信息] ✅ 通过第一个文本输入框找到设备名称输入框，页面加载完成 (进度: 100%)
2025-08-05 15:47:26 [信息] 通过第一个文本输入框找到设备名称输入框
2025-08-05 15:47:26 线程3：[信息] [信息] 📝 正在输入设备名称... (进度: 100%)
2025-08-05 15:47:27 线程3：[信息] [信息] ✅ 通过第一个文本输入框定位到设备名称输入框 (进度: 100%)
2025-08-05 15:47:27 线程3：[信息] [信息] ✅ 成功输入设备名称: joannkey (进度: 100%)
2025-08-05 15:47:27 [信息] 成功输入设备名称: joannkey
2025-08-05 15:47:27 线程3：[信息] [信息] ☑️ 正在选择'身份验证器应用程序'... (进度: 100%)
2025-08-05 15:47:27 线程3：[信息] [信息] ✅ 成功选择'身份验证器应用程序' (进度: 100%)
2025-08-05 15:47:27 [信息] 成功选择'身份验证器应用程序'
2025-08-05 15:47:27 线程3：[信息] [信息] 🖱️ 正在点击'下一步'按钮... (进度: 100%)
2025-08-05 15:47:28 线程3：[信息] [信息] ✅ 成功点击'下一步'按钮 (进度: 100%)
2025-08-05 15:47:28 [信息] 成功点击'下一步'按钮
2025-08-05 15:47:28 线程3：[信息] [信息] ⏳ 等待MFA密钥页面加载（20秒超时）... (进度: 100%)
2025-08-05 15:47:48 线程3：[信息] [信息] ⚠️ 20秒内未找到'显示密钥'按钮，开始抓取MFA代码元素 (进度: 100%)
2025-08-05 15:47:48 线程3：[信息] [信息] 🔍 开始抓取MFA代码1和MFA代码2输入框... (进度: 100%)
2025-08-05 15:47:48 [信息] 开始抓取MFA第二步元素
2025-08-05 15:47:48 线程3：[信息] [信息] 🔍 正在抓取MFA代码输入框... (进度: 100%)
2025-08-05 15:47:49 线程3：[信息] [信息] ⚠️ 未找到MFA代码输入框 (进度: 100%)
2025-08-05 15:47:49 线程3：[信息] [信息] ⚠️ 未抓取到任何MFA代码输入框 (进度: 100%)
2025-08-05 15:47:49 [信息] MFA第二步元素抓取结果: 未找到任何MFA代码输入框
2025-08-05 15:47:49 线程3：[信息] [信息] ⚠️ 显示密钥页面加载超时，开始元素抓取 (进度: 100%)
2025-08-05 15:47:49 [信息] 显示密钥页面加载超时，开始元素抓取
2025-08-05 15:47:49 线程3：[信息] [信息] 🔍 第一步页面加载超时，跳过元素抓取 (进度: 100%)
2025-08-05 15:47:49 [信息] 第一步页面加载超时，设备名称检测已简化，无需抓取
2025-08-05 15:47:49 线程3：[信息] [信息] 元素抓取完成，页面加载完成后，手动点击继续注册 (进度: 100%)
2025-08-05 15:47:49 线程3：[信息] [信息] MFA页面需要手动处理，等待用户操作 (进度: 100%)
2025-08-05 15:47:50 线程2：[信息] [信息] ⚠️ 20秒内未找到'显示密钥'按钮，开始抓取MFA代码元素 (进度: 100%)
2025-08-05 15:47:50 线程2：[信息] [信息] 🔍 开始抓取MFA代码1和MFA代码2输入框... (进度: 100%)
2025-08-05 15:47:50 [信息] 开始抓取MFA第二步元素
2025-08-05 15:47:50 线程2：[信息] [信息] 🔍 正在抓取MFA代码输入框... (进度: 100%)
2025-08-05 15:47:50 线程2：[信息] [信息] ⚠️ 未找到MFA代码输入框 (进度: 100%)
2025-08-05 15:47:50 线程2：[信息] [信息] ⚠️ 未抓取到任何MFA代码输入框 (进度: 100%)
2025-08-05 15:47:50 [信息] MFA第二步元素抓取结果: 未找到任何MFA代码输入框
2025-08-05 15:47:50 线程2：[信息] [信息] ⚠️ 显示密钥页面加载超时，开始元素抓取 (进度: 100%)
2025-08-05 15:47:50 [信息] 显示密钥页面加载超时，开始元素抓取
2025-08-05 15:47:50 线程2：[信息] [信息] 🔍 第一步页面加载超时，跳过元素抓取 (进度: 100%)
2025-08-05 15:47:50 [信息] 第一步页面加载超时，设备名称检测已简化，无需抓取
2025-08-05 15:47:50 线程2：[信息] [信息] 元素抓取完成，页面加载完成后，手动点击继续注册 (进度: 100%)
2025-08-05 15:47:50 线程2：[信息] [信息] MFA页面需要手动处理，等待用户操作 (进度: 100%)
2025-08-05 15:53:14 [信息] 多线程窗口引用已清理
2025-08-05 15:53:14 [信息] 主窗口已恢复正常状态，多线程窗口引用已清理
2025-08-05 15:53:14 [信息] 多线程管理窗口正在关闭
2025-08-05 15:53:59 [信息] 程序正在退出，开始清理工作...
2025-08-05 15:53:59 [信息] 程序退出，开始清理所有待拉黑千川手机号码
2025-08-05 15:53:59 [信息] 程序退出清理完成: 没有需要拉黑的千川手机号码
2025-08-05 15:53:59 [信息] 程序退出，开始清理所有待释放手机号码
2025-08-05 15:53:59 [信息] 程序退出清理完成: 没有需要释放的手机号码
2025-08-05 15:53:59 [信息] 程序退出清理工作完成
