2025-08-05 14:46:44 [信息] AWS自动注册工具启动
2025-08-05 14:46:44 [信息] 程序版本: 1.0.0.0
2025-08-05 14:46:44 [信息] 启动时间: 2025-08-05 14:46:44
2025-08-05 14:46:44 [信息] 浏览器模式切换: 无痕 Chrome 模式
2025-08-05 14:46:44 [信息] 线程数量已选择: 1
2025-08-05 14:46:44 [信息] 线程数量选择初始化完成
2025-08-05 14:46:44 [信息] 程序初始化完成
2025-08-05 14:46:47 [按钮操作] 选择文件 -> 打开文件选择对话框
2025-08-05 14:46:49 [信息] 已选择文件: C:\Users\<USER>\Desktop\2025-08-05-智利.txt
2025-08-05 14:46:50 [按钮操作] 加载信息 -> 加载数据文件: C:\Users\<USER>\Desktop\2025-08-05-智利.txt
2025-08-05 14:46:50 [信息] 成功加载 20 条数据
2025-08-05 14:46:52 [信息] 线程数量已选择: 3
2025-08-05 14:46:56 [按钮操作] 开始注册 -> 启动注册流程
2025-08-05 14:46:56 [信息] 开始启动多线程注册，线程数量: 3
2025-08-05 14:46:56 [信息] 开始启动多线程注册，线程数量: 3，数据条数: 20
2025-08-05 14:46:56 [信息] 所有线程已停止并清理
2025-08-05 14:46:56 [信息] 正在初始化多线程服务...
2025-08-05 14:46:56 [信息] 榴莲手机API服务已初始化
2025-08-05 14:46:56 [信息] 手机号码管理器已初始化，服务商: Durian，将在第一个线程完成第二页后获取手机号码
2025-08-05 14:46:56 [信息] 多线程服务初始化完成
2025-08-05 14:46:56 [信息] 数据分配完成：共20条数据分配给3个线程
2025-08-05 14:46:56 [信息] 线程1分配到7条数据
2025-08-05 14:46:56 [信息] 线程2分配到7条数据
2025-08-05 14:46:56 [信息] 线程3分配到6条数据
2025-08-05 14:46:56 [信息] 屏幕工作区域: 1280x672
2025-08-05 14:46:56 [信息] 线程1窗口布局: 位置(0, 0), 大小(384x200), 列1行1, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-05 14:46:56 [信息] 线程1获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-05 14:46:56 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-05 14:46:56 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_010, WebGL=ANGLE (AMD Radeon RX 6700 XT Direct3D11 vs_5_0 ps_5_0), CPU=24核, RAM=24 GB
2025-08-05 14:46:56 线程1：[信息] 已创建，窗口位置: (0, 0)
2025-08-05 14:46:56 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-05 14:46:56 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-05 14:46:56 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-05 14:46:56 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-05 14:46:56 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-05 14:46:56 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-05 14:46:56 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-05 14:46:56 [信息] 线程1已创建，窗口位置: (0, 0)，指纹: 国家=CL, 时区=America/Santiago
2025-08-05 14:46:56 [信息] 屏幕工作区域: 1280x672
2025-08-05 14:46:56 [信息] 线程2窗口布局: 位置(0, 219), 大小(384x200), 列1行2, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-05 14:46:56 [信息] 线程2获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-05 14:46:56 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-05 14:46:56 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_006, WebGL=ANGLE (Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0), CPU=8核, RAM=8 GB
2025-08-05 14:46:56 线程2：[信息] 已创建，窗口位置: (0, 219)
2025-08-05 14:46:56 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-05 14:46:56 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-05 14:46:56 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-05 14:46:56 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-05 14:46:56 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-05 14:46:56 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-05 14:46:56 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-05 14:46:56 [信息] 线程2已创建，窗口位置: (0, 219)，指纹: 国家=CL, 时区=America/Santiago
2025-08-05 14:46:56 [信息] 屏幕工作区域: 1280x672
2025-08-05 14:46:56 [信息] 线程3窗口布局: 位置(0, 438), 大小(384x200), 列1行3, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-05 14:46:56 [信息] 线程3获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-05 14:46:56 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-05 14:46:56 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_009, WebGL=ANGLE (NVIDIA GeForce GTX 1660 Direct3D11 vs_5_0 ps_5_0), CPU=16核, RAM=64 GB
2025-08-05 14:46:56 线程3：[信息] 已创建，窗口位置: (0, 438)
2025-08-05 14:46:56 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-05 14:46:56 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-05 14:46:56 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-05 14:46:56 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-05 14:46:56 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-05 14:46:56 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-05 14:46:56 [信息] 线程3已创建，窗口位置: (0, 438)，指纹: 国家=CL, 时区=America/Santiago
2025-08-05 14:46:56 [信息] 多线程注册启动成功，共3个线程
2025-08-05 14:46:56 线程2：[信息] 开始启动注册流程
2025-08-05 14:46:56 线程1：[信息] 开始启动注册流程
2025-08-05 14:46:56 线程3：[信息] 开始启动注册流程
2025-08-05 14:46:56 线程2：[信息] 开始启动浏览器: 位置(0, 219), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36
2025-08-05 14:46:56 线程1：[信息] 开始启动浏览器: 位置(0, 0), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36
2025-08-05 14:46:56 线程1：[信息] 启动无痕Chrome浏览器...
2025-08-05 14:46:56 线程2：[信息] 启动无痕Chrome浏览器...
2025-08-05 14:46:56 线程3：[信息] 开始启动浏览器: 位置(0, 438), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-08-05 14:46:56 线程3：[信息] 启动无痕Chrome浏览器...
2025-08-05 14:46:56 线程2：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-05 14:46:56 线程1：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-05 14:46:56 线程3：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-05 14:46:56 [信息] 多线程管理窗口已初始化
2025-08-05 14:46:56 [信息] UniformGrid列数已更新为: 1
2025-08-05 14:46:56 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 0, 列数: 1
2025-08-05 14:46:56 [信息] 多线程管理窗口已打开
2025-08-05 14:46:56 [信息] 多线程注册启动成功，共3个线程
2025-08-05 14:47:01 [信息] UniformGrid列数已更新为: 1
2025-08-05 14:47:02 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 1, 列数: 1
2025-08-05 14:47:02 线程3：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-05 14:47:02 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-05 14:47:02 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36
2025-08-05 14:47:02 线程3：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-05 14:47:02 [信息] UniformGrid列数已更新为: 1
2025-08-05 14:47:02 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-08-05 14:47:03 线程2：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-05 14:47:03 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-05 14:47:03 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36
2025-08-05 14:47:03 线程2：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-05 14:47:03 [信息] UniformGrid列数已更新为: 2
2025-08-05 14:47:03 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 3, 列数: 2
2025-08-05 14:47:03 线程1：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-05 14:47:03 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-05 14:47:03 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36
2025-08-05 14:47:03 线程1：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-05 14:47:04 线程3：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-05 14:47:04 线程2：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-05 14:47:04 线程1：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-05 14:47:06 线程3：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-05 14:47:06 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-05 14:47:06 线程3：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-05 14:47:06 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-05 14:47:06 线程3：[信息] [信息] 多线程模式使用指纹地理位置: -29.9027, -71.2519 (进度: 0%)
2025-08-05 14:47:06 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-29.9027, 经度=-71.2519
2025-08-05 14:47:06 线程2：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-05 14:47:06 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-05 14:47:06 线程2：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-05 14:47:06 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-05 14:47:06 线程2：[信息] [信息] 多线程模式使用指纹地理位置: -41.4693, -72.9424 (进度: 0%)
2025-08-05 14:47:06 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-41.4693, 经度=-72.9424
2025-08-05 14:47:06 线程3：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_009, CPU: 16核 (进度: 0%)
2025-08-05 14:47:06 [信息] 浏览器指纹注入: Canvas=canvas_fp_009, WebGL=ANGLE (NVIDIA GeForce GTX 1660 Direct3D11 vs_5_0 ps_5_0), CPU=16核, RAM=64 GB
2025-08-05 14:47:06 线程1：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-05 14:47:06 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-05 14:47:06 线程1：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-05 14:47:06 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-05 14:47:06 线程1：[信息] [信息] 多线程模式使用指纹地理位置: -41.4693, -72.9424 (进度: 0%)
2025-08-05 14:47:06 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-41.4693, 经度=-72.9424
2025-08-05 14:47:06 线程2：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_006, CPU: 8核 (进度: 0%)
2025-08-05 14:47:06 [信息] 浏览器指纹注入: Canvas=canvas_fp_006, WebGL=ANGLE (Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0), CPU=8核, RAM=8 GB
2025-08-05 14:47:06 线程1：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_010, CPU: 24核 (进度: 0%)
2025-08-05 14:47:06 [信息] 浏览器指纹注入: Canvas=canvas_fp_010, WebGL=ANGLE (AMD Radeon RX 6700 XT Direct3D11 vs_5_0 ps_5_0), CPU=24核, RAM=24 GB
2025-08-05 14:47:10 线程1：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-05 14:47:10 线程3：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-05 14:47:10 线程2：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-05 14:47:13 线程2：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 8
   • 设备内存: 8 GB
   • 平台信息: Win32
   • Do Not Track: default

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (Intel Corporation)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 1E2F3A4B
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_002
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-87EL3RX
   • MAC地址: 9A-BC-DE-F0-12-34
   • 屏幕分辨率: 1766x1146
   • 可用区域: 1766x1106

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: A5B6C7D8
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: 2g
   • 电池API支持: True
   • 电池电量: 0.52
   • 电池充电状态: False
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-05 14:47:13 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 8    • 设备内存: 8 GB    • 平台信息: Win32    • Do Not Track: default   图形渲染信息:    • WebGL渲染器: ANGLE (Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (Intel Corporation)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 1E2F3A4B    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_002    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-87EL3RX    • MAC地址: 9A-BC-DE-F0-12-34    • 屏幕分辨率: 1766x1146    • 可用区域: 1766x1106   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: A5B6C7D8    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: 2g    • 电池API支持: True    • 电池电量: 0.52    • 电池充电状态: False    • 性能API支持: True  ================================
2025-08-05 14:47:13 线程1：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 24
   • 设备内存: 24 GB
   • 平台信息: Win32
   • Do Not Track: user

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (AMD Radeon RX 6700 XT Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (Intel)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 5A6B7C8D
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_005
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-A1B2C3D
   • MAC地址: A1-B2-C3-D4-E5-F6
   • 屏幕分辨率: 1833x1050
   • 可用区域: 1833x1010

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: A7B8C9D0
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: ethernet
   • 电池API支持: True
   • 电池电量: 0.80
   • 电池充电状态: True
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-05 14:47:13 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 24    • 设备内存: 24 GB    • 平台信息: Win32    • Do Not Track: user   图形渲染信息:    • WebGL渲染器: ANGLE (AMD Radeon RX 6700 XT Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (Intel)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 5A6B7C8D    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_005    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-A1B2C3D    • MAC地址: A1-B2-C3-D4-E5-F6    • 屏幕分辨率: 1833x1050    • 可用区域: 1833x1010   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: A7B8C9D0    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: ethernet    • 电池API支持: True    • 电池电量: 0.80    • 电池充电状态: True    • 性能API支持: True  ================================
2025-08-05 14:47:13 线程3：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 16
   • 设备内存: 64 GB
   • 平台信息: Win32
   • Do Not Track: 0

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (NVIDIA GeForce GTX 1660 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (Intel Corp.)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 0426959D
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_011
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-Q3R4S5T
   • MAC地址: F0-12-34-56-78-9A
   • 屏幕分辨率: 1749x1158
   • 可用区域: 1749x1118

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: E5F6A7B8
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: slow-2g
   • 电池API支持: True
   • 电池电量: 0.80
   • 电池充电状态: True
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-05 14:47:13 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 16    • 设备内存: 64 GB    • 平台信息: Win32    • Do Not Track: 0   图形渲染信息:    • WebGL渲染器: ANGLE (NVIDIA GeForce GTX 1660 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (Intel Corp.)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 0426959D    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_011    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-Q3R4S5T    • MAC地址: F0-12-34-56-78-9A    • 屏幕分辨率: 1749x1158    • 可用区域: 1749x1118   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: E5F6A7B8    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: slow-2g    • 电池API支持: True    • 电池电量: 0.80    • 电池充电状态: True    • 性能API支持: True  ================================
2025-08-05 14:47:13 线程1：[信息] [信息] 已设置浏览器标题: 线程1 - AWS注册工具 (进度: 5%)
2025-08-05 14:47:13 线程1：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-05 14:47:13 线程1：[信息] 浏览器启动成功
2025-08-05 14:47:13 线程1：[信息] 获取下一个数据: <EMAIL>
2025-08-05 14:47:13 线程1：[信息] 开始处理账户: <EMAIL>
2025-08-05 14:47:14 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-05 14:47:14 线程1：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-05 14:47:14 线程1：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-05 14:47:14 线程1：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-05 14:47:14 线程1：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-05 14:47:14 线程2：[信息] [信息] 已设置浏览器标题: 线程2 - AWS注册工具 (进度: 5%)
2025-08-05 14:47:14 线程2：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-05 14:47:14 线程2：[信息] 浏览器启动成功
2025-08-05 14:47:14 线程2：[信息] 获取下一个数据: <EMAIL>
2025-08-05 14:47:15 线程2：[信息] 开始处理账户: <EMAIL>
2025-08-05 14:47:15 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-05 14:47:15 线程2：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-05 14:47:15 线程2：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-05 14:47:15 线程2：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-05 14:47:15 线程2：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-05 14:47:15 线程3：[信息] [信息] 已设置浏览器标题: 线程3 - AWS注册工具 (进度: 5%)
2025-08-05 14:47:15 线程3：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-05 14:47:15 线程3：[信息] 浏览器启动成功
2025-08-05 14:47:15 线程3：[信息] 获取下一个数据: <EMAIL>
2025-08-05 14:47:16 线程3：[信息] 开始处理账户: <EMAIL>
2025-08-05 14:47:16 线程3：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-05 14:47:16 线程3：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-05 14:47:16 线程3：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-05 14:47:16 线程3：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-05 14:47:16 线程3：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-05 14:47:16 线程2：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-05 14:47:16 线程1：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-05 14:47:16 线程2：[信息] [信息] 已重新设置浏览器标题: 线程2 - AWS注册工具 (进度: 98%)
2025-08-05 14:47:16 线程2：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-05 14:47:16 线程1：[信息] [信息] 已重新设置浏览器标题: 线程1 - AWS注册工具 (进度: 98%)
2025-08-05 14:47:16 线程1：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-05 14:47:16 线程1：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-05 14:47:16 线程1：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-05 14:47:16 线程1：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-05 14:47:17 线程2：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-05 14:47:17 线程2：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-05 14:47:17 线程2：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-05 14:47:17 线程3：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-05 14:47:17 线程3：[信息] [信息] 已重新设置浏览器标题: 线程3 - AWS注册工具 (进度: 98%)
2025-08-05 14:47:17 线程3：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-05 14:47:17 线程3：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-05 14:47:17 线程3：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-05 14:47:17 线程3：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-05 14:47:46 线程1：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程1 - AWS注册 (进度: 98%)
2025-08-05 14:47:46 线程1：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-05 14:47:46 线程1：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-05 14:47:46 线程1：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-05 14:47:46 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-05 14:47:47 线程1：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-05 14:47:47 线程3：[信息] [信息] 注册失败: Timeout 30000ms exceeded.
Call log:
  - navigating to "https://portal.aws.amazon.com/gp/aws/developer/registration/index.html?refid=em_127222&p=free&c=hp&z=1", waiting until "load" (进度: 98%)
2025-08-05 14:47:47 线程3：[信息] [信息] 第一页相关失败，数据保持不动 (进度: 98%)
2025-08-05 14:47:47 [信息] 第一页相关失败，数据保持不动
2025-08-05 14:47:47 线程3：[错误] 账户注册启动失败: <EMAIL>
2025-08-05 14:47:47 线程3：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-05 14:47:48 线程2：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程2 - AWS注册 (进度: 98%)
2025-08-05 14:47:48 线程2：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-05 14:47:48 线程2：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-05 14:47:48 线程2：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-05 14:47:48 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-05 14:47:48 线程2：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-05 14:47:50 线程1：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-05 14:47:50 线程1：[信息] [信息] ⚠️ 检测到错误信息，开始重试机制... (进度: 98%)
2025-08-05 14:47:50 [信息] 检测到错误信息，开始重试机制
2025-08-05 14:47:50 线程1：[信息] [信息] 🔄 第1次重试点击验证邮箱按钮... (进度: 98%)
2025-08-05 14:47:50 [信息] 第1次重试点击验证邮箱按钮
2025-08-05 14:47:51 线程2：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-05 14:47:51 线程2：[信息] [信息] ⚠️ 检测到错误信息，开始重试机制... (进度: 98%)
2025-08-05 14:47:51 [信息] 检测到错误信息，开始重试机制
2025-08-05 14:47:51 线程2：[信息] [信息] 🔄 第1次重试点击验证邮箱按钮... (进度: 98%)
2025-08-05 14:47:51 [信息] 第1次重试点击验证邮箱按钮
2025-08-05 14:47:51 线程3：[信息] [信息]  继续注册被调用，当前状态: Error，当前步骤: 1 (进度: 0%)
2025-08-05 14:47:51 线程3：[信息] [信息]  进行智能页面检测... (进度: 0%)
2025-08-05 14:47:51 线程3：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 0%)
2025-08-05 14:47:51 线程3：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 0%)
2025-08-05 14:47:51 线程3：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 0%)
2025-08-05 14:47:51 线程3：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-05 14:47:51 线程3：[信息] [信息]  智能检测到当前在第1页 (进度: 100%)
2025-08-05 14:47:51 线程3：[信息] [信息] 检测当前页面状态... (进度: 100%)
2025-08-05 14:47:51 线程3：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-05 14:47:51 线程3：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-05 14:47:51 线程3：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 100%)
2025-08-05 14:47:51 线程3：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-05 14:47:51 线程3：[信息] [信息] 智能检测到当前在第1页，继续执行... (进度: 100%)
2025-08-05 14:47:51 线程3：[信息] [信息] 🔍 等待第一页加载完成... (进度: 100%)
2025-08-05 14:47:51 线程3：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 100%)
2025-08-05 14:47:51 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-05 14:47:52 线程3：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 100%)
2025-08-05 14:47:52 线程1：[信息] [信息] ✅ 第1次重试：已点击验证邮箱按钮 (进度: 100%)
2025-08-05 14:47:52 [信息] 第1次重试：已点击验证邮箱按钮
2025-08-05 14:47:53 线程2：[信息] [信息] ✅ 第1次重试：已点击验证邮箱按钮 (进度: 100%)
2025-08-05 14:47:53 [信息] 第1次重试：已点击验证邮箱按钮
2025-08-05 14:47:54 线程1：[信息] [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']" (进度: 100%)
2025-08-05 14:47:54 [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']"
2025-08-05 14:47:54 线程1：[信息] [信息] ✅ 第1次重试成功：已到达第二页，继续正常流程 (进度: 100%)
2025-08-05 14:47:54 [信息] 第1次重试成功：已到达第二页
2025-08-05 14:47:54 线程1：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-05 14:47:54 线程1：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-05 14:47:54 线程1：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-05 14:47:54 线程1：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-05 14:47:55 线程3：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 100%)
2025-08-05 14:47:55 线程3：[信息] [信息] ⚠️ 检测到错误信息，开始重试机制... (进度: 100%)
2025-08-05 14:47:55 [信息] 检测到错误信息，开始重试机制
2025-08-05 14:47:55 线程3：[信息] [信息] 🔄 第1次重试点击验证邮箱按钮... (进度: 100%)
2025-08-05 14:47:55 [信息] 第1次重试点击验证邮箱按钮
2025-08-05 14:47:55 线程2：[信息] [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']" (进度: 100%)
2025-08-05 14:47:55 [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']"
2025-08-05 14:47:55 线程2：[信息] [信息] ✅ 第1次重试成功：已到达第二页，继续正常流程 (进度: 100%)
2025-08-05 14:47:55 [信息] 第1次重试成功：已到达第二页
2025-08-05 14:47:55 线程2：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-05 14:47:55 线程2：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-05 14:47:55 线程2：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-05 14:47:55 线程2：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-05 14:47:56 线程1：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-05 14:47:56 线程1：[信息] [信息] 🔍 第2次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-05 14:47:56 线程1：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 未发现验证码 (进度: 100%)
2025-08-05 14:47:56 线程1：[信息] [信息] ✅ 第一页未检测到图形验证码，继续流程 (进度: 100%)
2025-08-05 14:47:56 线程1：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-05 14:47:56 线程1：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-05 14:47:56 线程1：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-05 14:47:56 线程1：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-05 14:47:56 线程1：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-05 14:47:56 线程1：[信息] 账户注册流程已启动: <EMAIL>
2025-08-05 14:47:56 线程1：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-05 14:47:56 [信息] [线程1] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-05 14:47:56 [信息] [线程1] 等待2秒后开始第一次触发...
2025-08-05 14:47:57 线程3：[信息] [信息] ✅ 第1次重试：已点击验证邮箱按钮 (进度: 100%)
2025-08-05 14:47:57 [信息] 第1次重试：已点击验证邮箱按钮
2025-08-05 14:47:57 线程2：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-05 14:47:57 线程2：[信息] [信息] 🔍 第2次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-05 14:47:57 线程2：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 未发现验证码 (进度: 100%)
2025-08-05 14:47:57 线程2：[信息] [信息] ✅ 第一页未检测到图形验证码，继续流程 (进度: 100%)
2025-08-05 14:47:57 线程2：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-05 14:47:57 线程2：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-05 14:47:57 线程2：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-05 14:47:57 线程2：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-05 14:47:57 [信息] [线程2] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-05 14:47:57 线程2：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-05 14:47:57 [信息] [线程2] 已删除旧的响应文件
2025-08-05 14:47:57 [信息] [线程2] 等待2秒后开始第一次触发...
2025-08-05 14:47:57 线程2：[信息] 账户注册流程已启动: <EMAIL>
2025-08-05 14:47:57 线程2：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-05 14:47:58 [信息] [线程1] 第1次触发邮箱验证码获取...（最多20次）
2025-08-05 14:47:58 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-05 14:47:58
2025-08-05 14:47:59 线程3：[信息] [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']" (进度: 100%)
2025-08-05 14:47:59 [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']"
2025-08-05 14:47:59 线程3：[信息] [信息] ✅ 第1次重试成功：已到达第二页，继续正常流程 (进度: 100%)
2025-08-05 14:47:59 [信息] 第1次重试成功：已到达第二页
2025-08-05 14:47:59 线程3：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-05 14:47:59 线程3：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-05 14:47:59 线程3：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-05 14:47:59 线程3：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-05 14:47:59 [信息] [线程2] 第1次触发邮箱验证码获取...（最多20次）
2025-08-05 14:47:59 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-05 14:47:59
2025-08-05 14:48:01 线程3：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-05 14:48:01 线程3：[信息] [信息] 🔍 第2次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-05 14:48:01 线程3：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 未发现验证码 (进度: 100%)
2025-08-05 14:48:01 线程3：[信息] [信息] ✅ 第一页未检测到图形验证码，继续流程 (进度: 100%)
2025-08-05 14:48:01 线程3：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-05 14:48:01 线程3：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-05 14:48:01 线程3：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-05 14:48:01 线程3：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-05 14:48:01 [信息] [线程3] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-05 14:48:01 线程3：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-05 14:48:01 [信息] [线程3] 等待2秒后开始第一次触发...
2025-08-05 14:48:01 线程3：[信息] 已继续
2025-08-05 14:48:01 [信息] 线程3已继续
2025-08-05 14:48:01 [信息] [线程1] 第2次触发邮箱验证码获取...（最多20次）
2025-08-05 14:48:01 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-05 14:48:01
2025-08-05 14:48:02 [信息] [线程2] 第2次触发邮箱验证码获取...（最多20次）
2025-08-05 14:48:02 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-05 14:48:02
2025-08-05 14:48:03 [信息] [线程3] 第1次触发邮箱验证码获取...（最多20次）
2025-08-05 14:48:03 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-05 14:48:03
2025-08-05 14:48:04 [信息] [线程1] 第3次触发邮箱验证码获取...（最多20次）
2025-08-05 14:48:04 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-05 14:48:04
2025-08-05 14:48:05 [信息] [线程2] 第3次触发邮箱验证码获取...（最多20次）
2025-08-05 14:48:05 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-05 14:48:05
2025-08-05 14:48:06 [信息] [线程3] 第2次触发邮箱验证码获取...（最多20次）
2025-08-05 14:48:06 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-05 14:48:06
2025-08-05 14:48:06 [信息] [线程2] 邮箱验证码获取成功: 527302，立即停止重复请求
2025-08-05 14:48:06 [信息] [线程2] 已清理请求文件，停止重复触发
2025-08-05 14:48:06 [信息] [线程2] 已清理响应文件
2025-08-05 14:48:06 线程2：[信息] [信息] 验证码获取成功: 527302，正在自动填入... (进度: 25%)
2025-08-05 14:48:06 线程2：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 25%)
2025-08-05 14:48:06 [信息] [线程1] 邮箱验证码获取成功: 678939，立即停止重复请求
2025-08-05 14:48:06 [信息] [线程1] 已清理请求文件，停止重复触发
2025-08-05 14:48:06 [信息] [线程1] 已清理响应文件
2025-08-05 14:48:06 线程1：[信息] [信息] 验证码获取成功: 678939，正在自动填入... (进度: 25%)
2025-08-05 14:48:06 线程1：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 25%)
2025-08-05 14:48:06 线程2：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 25%)
2025-08-05 14:48:07 线程2：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 25%)
2025-08-05 14:48:07 [信息] 线程2完成第二页事件已处理
2025-08-05 14:48:07 [信息] 线程2完成第二页，开始批量获取手机号码...
2025-08-05 14:48:07 线程2：[信息] [信息] 线程2第二页验证完成，已通知管理器 (进度: 35%)
2025-08-05 14:48:07 线程1：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 25%)
2025-08-05 14:48:07 [信息] 开始批量获取3个手机号码，服务商: Durian
2025-08-05 14:48:07 [信息] [手机API] 批量获取3个手机号码，URL: https://api.durianrcs.com/out/ext_api/getMobile?name=oneone&ApiKey=NnlaR01xQm9hMlAwRnJDSVB2SG1kQT09&cuy=mx&pid=0209&num=3&noblack=0&serial=2&secret_key=null&vip=null
2025-08-05 14:48:07 线程1：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 25%)
2025-08-05 14:48:07 [信息] 线程1完成第二页事件已处理
2025-08-05 14:48:07 [信息] 线程1完成第二页，手机号码已获取，无需重复获取
2025-08-05 14:48:07 线程1：[信息] [信息] 线程1第二页验证完成，已通知管理器 (进度: 35%)
2025-08-05 14:48:09 [信息] [线程3] 第3次触发邮箱验证码获取...（最多20次）
2025-08-05 14:48:09 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-05 14:48:09
2025-08-05 14:48:09 [信息] [手机API] 批量获取响应内容: {"code":200,"msg":"Success","data":["+526463856089","+522261153806","+527222483115"]}
2025-08-05 14:48:09 [信息] [手机API] 检测到数组格式，元素数量: 3
2025-08-05 14:48:09 [信息] [手机API] 批量获取成功，获得3个手机号码
2025-08-05 14:48:09 [信息] 线程1分配榴莲手机号码: +526463856089
2025-08-05 14:48:09 [信息] 线程2分配榴莲手机号码: +522261153806
2025-08-05 14:48:09 [信息] 线程3分配榴莲手机号码: +527222483115
2025-08-05 14:48:09 [信息] 榴莲API批量获取手机号码成功，已分配给3个线程
2025-08-05 14:48:09 [信息] 批量获取3个手机号码成功
2025-08-05 14:48:10 线程2：[信息] [信息] 等待密码设置页面加载... (进度: 38%)
2025-08-05 14:48:10 线程2：[信息] [信息] 开始填写密码信息... (进度: 38%)
2025-08-05 14:48:10 线程2：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-05 14:48:10 线程1：[信息] [信息] 等待密码设置页面加载... (进度: 38%)
2025-08-05 14:48:10 线程1：[信息] [信息] 开始填写密码信息... (进度: 38%)
2025-08-05 14:48:10 线程2：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-05 14:48:10 线程2：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 38%)
2025-08-05 14:48:10 线程1：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-05 14:48:10 线程1：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-05 14:48:10 线程1：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 38%)
2025-08-05 14:48:10 [信息] [线程3] 邮箱验证码获取成功: 150821，立即停止重复请求
2025-08-05 14:48:10 [信息] [线程3] 已清理请求文件，停止重复触发
2025-08-05 14:48:10 [信息] [线程3] 已清理响应文件
2025-08-05 14:48:10 线程3：[信息] [信息] 验证码获取成功: 150821，正在自动填入... (进度: 100%)
2025-08-05 14:48:10 线程3：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 100%)
2025-08-05 14:48:10 线程3：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 100%)
2025-08-05 14:48:11 线程3：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 100%)
2025-08-05 14:48:11 [信息] 线程3完成第二页事件已处理
2025-08-05 14:48:11 [信息] 线程3完成第二页，手机号码已获取，无需重复获取
2025-08-05 14:48:11 线程3：[信息] [信息] 线程3第二页验证完成，已通知管理器 (进度: 100%)
2025-08-05 14:48:11 线程2：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 38%)
2025-08-05 14:48:11 线程1：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 38%)
2025-08-05 14:48:14 线程3：[信息] [信息] 等待密码设置页面加载... (进度: 100%)
2025-08-05 14:48:14 线程3：[信息] [信息] 开始填写密码信息... (进度: 100%)
2025-08-05 14:48:14 线程3：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 100%)
2025-08-05 14:48:14 线程3：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 100%)
2025-08-05 14:48:14 线程3：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 100%)
2025-08-05 14:48:14 线程2：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 38%)
2025-08-05 14:48:14 线程2：[信息] [信息] 等待账户类型确认页面加载... (进度: 38%)
2025-08-05 14:48:14 线程2：[信息] [信息] 开始处理账户类型确认... (进度: 38%)
2025-08-05 14:48:14 线程1：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 38%)
2025-08-05 14:48:14 线程1：[信息] [信息] 等待账户类型确认页面加载... (进度: 38%)
2025-08-05 14:48:14 线程1：[信息] [信息] 开始处理账户类型确认... (进度: 38%)
2025-08-05 14:48:15 线程3：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 100%)
2025-08-05 14:48:18 线程3：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 100%)
2025-08-05 14:48:18 线程3：[信息] [信息] 等待账户类型确认页面加载... (进度: 100%)
2025-08-05 14:48:18 线程3：[信息] [信息] 开始处理账户类型确认... (进度: 100%)
2025-08-05 14:48:21 线程1：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 38%)
2025-08-05 14:48:21 线程1：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 38%)
2025-08-05 14:48:21 线程2：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 38%)
2025-08-05 14:48:21 线程2：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 38%)
2025-08-05 14:48:28 线程3：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 100%)
2025-08-05 14:48:28 线程3：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 100%)
2025-08-05 14:48:44 线程2：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 38%)
2025-08-05 14:48:44 [信息] 线程2获取已分配的榴莲手机号码: +522261153806
2025-08-05 14:48:44 线程2：[信息] [信息] 多线程模式：使用已分配的手机号码 +522261153806 (进度: 38%)
2025-08-05 14:48:44 线程1：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 38%)
2025-08-05 14:48:44 [信息] 线程1获取已分配的榴莲手机号码: +526463856089
2025-08-05 14:48:44 线程1：[信息] [信息] 多线程模式：使用已分配的手机号码 +526463856089 (进度: 38%)
2025-08-05 14:48:44 线程2：[信息] [信息] 数据国家代码为CL，需要选择Chile (进度: 38%)
2025-08-05 14:48:45 线程1：[信息] [信息] 数据国家代码为CL，需要选择Chile (进度: 38%)
2025-08-05 14:48:45 线程2：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 38%)
2025-08-05 14:48:45 线程1：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 38%)
2025-08-05 14:48:47 线程2：[信息] [信息] 已选择国家: Chile (进度: 38%)
2025-08-05 14:48:47 线程2：[信息] [信息] 已成功选择国家: Chile (进度: 38%)
2025-08-05 14:48:47 线程2：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 38%)
2025-08-05 14:48:47 线程1：[信息] [信息] 已选择国家: Chile (进度: 38%)
2025-08-05 14:48:47 线程1：[信息] [信息] 已成功选择国家: Chile (进度: 38%)
2025-08-05 14:48:47 线程1：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 38%)
2025-08-05 14:48:47 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-08-05 14:48:47 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-08-05 14:48:51 线程2：[信息] [信息] 已选择国家代码 +52 (进度: 38%)
2025-08-05 14:48:51 线程1：[信息] [信息] 已选择国家代码 +52 (进度: 38%)
2025-08-05 14:48:52 线程2：[信息] [信息] 等待后台获取的手机号码结果... (进度: 38%)
2025-08-05 14:48:52 线程1：[信息] [信息] 等待后台获取的手机号码结果... (进度: 38%)
2025-08-05 14:48:52 线程2：[信息] [信息] 已自动获取并填入手机号码: +522261153806 (进度: 38%)
2025-08-05 14:48:52 线程1：[信息] [信息] 已自动获取并填入手机号码: +526463856089 (进度: 38%)
2025-08-05 14:48:53 线程2：[信息] [信息] 使用已获取的手机号码: +522261153806（保存本地号码: +522261153806） (进度: 38%)
2025-08-05 14:48:53 线程1：[信息] [信息] 使用已获取的手机号码: +526463856089（保存本地号码: +526463856089） (进度: 38%)
2025-08-05 14:48:53 线程2：[信息] [信息] 联系信息完成，等待页面加载... (进度: 38%)
2025-08-05 14:48:53 线程1：[信息] [信息] 联系信息完成，等待页面加载... (进度: 38%)
2025-08-05 14:48:54 线程3：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 100%)
2025-08-05 14:48:54 [信息] 线程3获取已分配的榴莲手机号码: +527222483115
2025-08-05 14:48:54 线程3：[信息] [信息] 多线程模式：使用已分配的手机号码 +527222483115 (进度: 100%)
2025-08-05 14:48:55 线程3：[信息] [信息] 数据国家代码为CL，需要选择Chile (进度: 100%)
2025-08-05 14:48:55 线程3：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 100%)
2025-08-05 14:48:56 线程2：[信息] [信息] 进入付款信息页面... (进度: 38%)
2025-08-05 14:48:56 线程1：[信息] [信息] 进入付款信息页面... (进度: 38%)
2025-08-05 14:48:57 线程3：[信息] [信息] 已选择国家: Chile (进度: 100%)
2025-08-05 14:48:57 线程3：[信息] [信息] 已成功选择国家: Chile (进度: 100%)
2025-08-05 14:48:57 线程3：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 100%)
2025-08-05 14:48:58 线程3：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-05 14:48:58 线程2：[信息] [信息] 正在选择月份: March (进度: 38%)
2025-08-05 14:48:58 线程2：[信息] [信息] 已选择月份（标准选项）: March (进度: 38%)
2025-08-05 14:48:58 线程1：[信息] [信息] 正在选择月份: April (进度: 38%)
2025-08-05 14:48:59 线程1：[信息] [信息] 已选择月份（标准选项）: April (进度: 38%)
2025-08-05 14:48:59 线程2：[信息] [信息] 正在选择年份: 2027 (进度: 38%)
2025-08-05 14:48:59 线程2：[信息] [信息] 已选择年份（标准选项）: 2027 (进度: 38%)
2025-08-05 14:49:00 线程1：[信息] [信息] 正在选择年份: 2027 (进度: 38%)
2025-08-05 14:49:01 线程1：[信息] [信息] 已选择年份（标准选项）: 2027 (进度: 38%)
2025-08-05 14:49:01 线程2：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 38%)
2025-08-05 14:49:01 线程2：[信息] [信息] 开始填写验证码验证页面... (进度: 38%)
2025-08-05 14:49:01 线程2：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 38%)
2025-08-05 14:49:01 线程1：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 38%)
2025-08-05 14:49:01 线程1：[信息] [信息] 开始填写验证码验证页面... (进度: 38%)
2025-08-05 14:49:01 线程1：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 38%)
2025-08-05 14:49:02 线程3：[信息] [信息] 已选择国家代码 +52 (进度: 100%)
2025-08-05 14:49:03 线程3：[信息] [信息] 等待后台获取的手机号码结果... (进度: 100%)
2025-08-05 14:49:03 线程3：[信息] [信息] 已自动获取并填入手机号码: +527222483115 (进度: 100%)
2025-08-05 14:49:04 线程3：[信息] [信息] 使用已获取的手机号码: +527222483115（保存本地号码: +527222483115） (进度: 100%)
2025-08-05 14:49:04 线程3：[信息] [信息] 联系信息完成，等待页面加载... (进度: 100%)
2025-08-05 14:49:06 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-08-05 14:49:06 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-08-05 14:49:10 线程1：[信息] [信息] 已选择国家代码: +52 (进度: 38%)
2025-08-05 14:49:10 线程2：[信息] [信息] 已选择国家代码: +52 (进度: 38%)
2025-08-05 14:49:11 线程1：[信息] [信息] 已清空并重新填写手机号码: +526463856089 (进度: 38%)
2025-08-05 14:49:11 线程3：[信息] [信息] 进入付款信息页面... (进度: 100%)
2025-08-05 14:49:11 线程2：[信息] [信息] 已清空并重新填写手机号码: +522261153806 (进度: 38%)
2025-08-05 14:49:11 线程2：[信息] [信息] 已点击发送验证码按钮 (进度: 38%)
2025-08-05 14:49:11 线程1：[信息] [信息] 已点击发送验证码按钮 (进度: 38%)
2025-08-05 14:49:12 线程3：[信息] [信息] 正在选择月份: October (进度: 100%)
2025-08-05 14:49:12 线程3：[信息] [信息] 已选择月份（标准选项）: October (进度: 100%)
2025-08-05 14:49:13 线程3：[信息] [信息] 正在选择年份: 2027 (进度: 100%)
2025-08-05 14:49:13 线程2：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 38%)
2025-08-05 14:49:13 线程1：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 38%)
2025-08-05 14:49:13 线程2：[信息] [信息] ⚠️ 检测到错误信息，开始重试机制... (进度: 38%)
2025-08-05 14:49:13 [信息] 检测到错误信息，开始重试机制
2025-08-05 14:49:13 线程2：[信息] [信息] 🔄 第1次重试发送验证码按钮... (进度: 38%)
2025-08-05 14:49:13 [信息] 第1次重试发送验证码按钮
2025-08-05 14:49:13 线程1：[信息] [信息] ⚠️ 检测到错误信息，开始重试机制... (进度: 38%)
2025-08-05 14:49:13 [信息] 检测到错误信息，开始重试机制
2025-08-05 14:49:13 线程1：[信息] [信息] 🔄 第1次重试发送验证码按钮... (进度: 38%)
2025-08-05 14:49:13 [信息] 第1次重试发送验证码按钮
2025-08-05 14:49:13 线程3：[信息] [信息] 已选择年份（标准选项）: 2027 (进度: 100%)
2025-08-05 14:49:14 线程3：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 100%)
2025-08-05 14:49:14 线程3：[信息] [信息] 开始填写验证码验证页面... (进度: 100%)
2025-08-05 14:49:14 线程3：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 100%)
2025-08-05 14:49:17 线程2：[信息] [信息] ✅ 第1次重试：已点击发送验证码按钮 (进度: 100%)
2025-08-05 14:49:17 [信息] 第1次重试：已点击发送验证码按钮
2025-08-05 14:49:17 线程1：[信息] [信息] ✅ 第1次重试：已点击发送验证码按钮 (进度: 100%)
2025-08-05 14:49:17 [信息] 第1次重试：已点击发送验证码按钮
2025-08-05 14:49:19 线程2：[信息] [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']" (进度: 100%)
2025-08-05 14:49:19 [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']"
2025-08-05 14:49:19 线程1：[信息] [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']" (进度: 100%)
2025-08-05 14:49:19 [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']"
2025-08-05 14:49:19 线程1：[信息] [信息] ✅ 第1次重试成功：错误信息消失，继续正常流程 (进度: 100%)
2025-08-05 14:49:19 [信息] 第1次重试成功：错误信息消失
2025-08-05 14:49:19 线程2：[信息] [信息] ❌ 第1次重试失败：错误信息仍然存在 (进度: 100%)
2025-08-05 14:49:19 [信息] 第1次重试失败：错误信息仍然存在
2025-08-05 14:49:19 线程1：[信息] [信息] 手机号码自动模式 + 图形验证码自动模式：开始自动处理... (进度: 100%)
2025-08-05 14:49:19 线程2：[信息] [信息] 🔄 第2次重试发送验证码按钮... (进度: 100%)
2025-08-05 14:49:19 [信息] 第2次重试发送验证码按钮
2025-08-05 14:49:19 线程1：[信息] [信息] 自动模式：开始处理图形验证码... (进度: 100%)
2025-08-05 14:49:19 线程1：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-05 14:49:21 线程3：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-05 14:49:22 线程1：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-05 14:49:22 线程1：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-05 14:49:22 线程3：[信息] [信息] 已选择国家代码: +52 (进度: 100%)
2025-08-05 14:49:22 线程2：[信息] [信息] ✅ 第2次重试：已点击发送验证码按钮 (进度: 100%)
2025-08-05 14:49:22 [信息] 第2次重试：已点击发送验证码按钮
2025-08-05 14:49:24 线程3：[信息] [信息] 已清空并重新填写手机号码: +527222483115 (进度: 100%)
2025-08-05 14:49:24 线程3：[信息] [信息] 已点击发送验证码按钮 (进度: 100%)
2025-08-05 14:49:24 线程2：[信息] [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']" (进度: 100%)
2025-08-05 14:49:24 [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']"
2025-08-05 14:49:24 线程2：[信息] [信息] ❌ 第2次重试失败：错误信息仍然存在 (进度: 100%)
2025-08-05 14:49:24 [信息] 第2次重试失败：错误信息仍然存在
2025-08-05 14:49:24 线程2：[信息] [信息] 🔄 第3次重试发送验证码按钮... (进度: 100%)
2025-08-05 14:49:24 [信息] 第3次重试发送验证码按钮
2025-08-05 14:49:26 线程3：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 100%)
2025-08-05 14:49:26 线程3：[信息] [信息] ⚠️ 检测到错误信息，开始重试机制... (进度: 100%)
2025-08-05 14:49:26 [信息] 检测到错误信息，开始重试机制
2025-08-05 14:49:26 线程3：[信息] [信息] 🔄 第1次重试发送验证码按钮... (进度: 100%)
2025-08-05 14:49:26 [信息] 第1次重试发送验证码按钮
2025-08-05 14:49:27 线程2：[信息] [信息] ✅ 第3次重试：已点击发送验证码按钮 (进度: 100%)
2025-08-05 14:49:27 [信息] 第3次重试：已点击发送验证码按钮
2025-08-05 14:49:28 线程1：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 35167 字节 (进度: 100%)
2025-08-05 14:49:28 线程1：[信息] [信息] ✅ 图片验证通过：201x70px，35167字节，复杂度符合要求 (进度: 100%)
2025-08-05 14:49:28 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-05 14:49:29 线程2：[信息] [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']" (进度: 100%)
2025-08-05 14:49:29 [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']"
2025-08-05 14:49:29 线程2：[信息] [信息] ❌ 第3次重试失败：错误信息仍然存在 (进度: 100%)
2025-08-05 14:49:29 [信息] 第3次重试失败：错误信息仍然存在
2025-08-05 14:49:29 线程2：[信息] [信息] 🔄 第4次重试发送验证码按钮... (进度: 100%)
2025-08-05 14:49:29 [信息] 第4次重试发送验证码按钮
2025-08-05 14:49:29 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"7fhmx2"},"taskId":"55c15ae4-71c8-11f0-be5c-d6daa566c6db"} (进度: 100%)
2025-08-05 14:49:29 线程1：[信息] [信息] 第六页第1次识别结果: 7fhmx2 → 转换为小写: 7fhmx2 (进度: 100%)
2025-08-05 14:49:29 线程1：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-05 14:49:29 线程3：[信息] [信息] ✅ 第1次重试：已点击发送验证码按钮 (进度: 100%)
2025-08-05 14:49:29 [信息] 第1次重试：已点击发送验证码按钮
2025-08-05 14:49:29 线程1：[信息] [信息] 第六页已填入验证码: 7fhmx2 (进度: 100%)
2025-08-05 14:49:30 线程1：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-05 14:49:31 线程3：[信息] [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']" (进度: 100%)
2025-08-05 14:49:31 [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']"
2025-08-05 14:49:31 线程3：[信息] [信息] ✅ 第1次重试成功：错误信息消失，继续正常流程 (进度: 100%)
2025-08-05 14:49:31 [信息] 第1次重试成功：错误信息消失
2025-08-05 14:49:31 线程3：[信息] [信息] 手机号码自动模式 + 图形验证码自动模式：开始自动处理... (进度: 100%)
2025-08-05 14:49:31 线程3：[信息] [信息] 自动模式：开始处理图形验证码... (进度: 100%)
2025-08-05 14:49:31 线程3：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-05 14:49:32 线程2：[信息] [信息] ✅ 第4次重试：已点击发送验证码按钮 (进度: 100%)
2025-08-05 14:49:32 [信息] 第4次重试：已点击发送验证码按钮
2025-08-05 14:49:34 线程1：[信息] [信息] 第1次图形验证码识别成功 (进度: 100%)
2025-08-05 14:49:34 线程1：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-08-05 14:49:34 线程3：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-05 14:49:34 线程3：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-05 14:49:34 线程2：[信息] [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']" (进度: 100%)
2025-08-05 14:49:34 [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']"
2025-08-05 14:49:34 线程2：[信息] [信息] ❌ 第4次重试失败：错误信息仍然存在 (进度: 100%)
2025-08-05 14:49:34 [信息] 第4次重试失败：错误信息仍然存在
2025-08-05 14:49:34 线程2：[信息] [信息] ❌ 4次重试均失败，确认为验证手机区号错误 (进度: 100%)
2025-08-05 14:49:34 [信息] 4次重试均失败，确认为验证手机区号错误
2025-08-05 14:49:34 线程2：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-08-05 14:49:34 线程2：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：C9988E5H ③AWS密码：Jf2glMIR ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //验证手机出现区号 (进度: 100%)
2025-08-05 14:49:34 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：C9988E5H ③AWS密码：Jf2glMIR ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //验证手机出现区号
2025-08-05 14:49:34 线程2：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：C9988E5H ③AWS密码：Jf2glMIR ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //验证手机出现区号
2025-08-05 14:49:34 [信息] 线程2请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：C9988E5H ③AWS密码：Jf2glMIR ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //验证手机出现区号
2025-08-05 14:49:34 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：C9988E5H ③AWS密码：Jf2glMIR ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //验证手机出现区号
2025-08-05 14:49:34 [信息] 线程2剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：C9988E5H ③AWS密码：Jf2glMIR ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //验证手机出现区号
2025-08-05 14:49:34 线程2：[信息] 收到失败数据保存请求: 验证手机出现区号, 数据: <EMAIL>
2025-08-05 14:49:34 [信息] 线程2请求保存失败数据: 验证手机出现区号, 数据: <EMAIL>
2025-08-05 14:49:35 [信息] 已处理失败数据: <EMAIL>, 失败原因: 验证手机出现区号
2025-08-05 14:49:35 [信息] 线程2失败数据已保存: 验证手机出现区号, 数据: <EMAIL>
2025-08-05 14:49:35 线程2：[信息] [信息] 已通知保存失败数据，失败原因: 验证手机出现区号 (进度: 0%)
2025-08-05 14:49:35 线程2：[信息] [信息] ❌ 注册失败，验证手机出现区号 (进度: 0%)
2025-08-05 14:49:35 [信息] 多线程模式：注册失败，验证手机出现区号
2025-08-05 14:49:35 线程2：[信息] [信息] 检测到验证手机区号错误，注册已终止 (进度: 0%)
2025-08-05 14:49:37 线程1：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-08-05 14:49:39 线程3：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 34735 字节 (进度: 100%)
2025-08-05 14:49:39 线程3：[信息] [信息] ✅ 图片验证通过：201x70px，34735字节，复杂度符合要求 (进度: 100%)
2025-08-05 14:49:39 线程3：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-05 14:49:40 线程1：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-05 14:49:40 线程1：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-05 14:49:40 线程1：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-05 14:49:40 线程1：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-08-05 14:49:43 线程3：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"f3mh4w"},"taskId":"5d590612-71c8-11f0-aaaf-6e28fd6820d3"} (进度: 100%)
2025-08-05 14:49:43 线程3：[信息] [信息] 第六页第1次识别结果: f3mh4w → 转换为小写: f3mh4w (进度: 100%)
2025-08-05 14:49:43 线程3：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-05 14:49:43 线程3：[信息] [信息] 第六页已填入验证码: f3mh4w (进度: 100%)
2025-08-05 14:49:43 线程3：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-05 14:49:45 线程1：[信息] [信息] 线程1开始独立获取验证码... (进度: 100%)
2025-08-05 14:49:45 线程1：[信息] [信息] 线程1开始获取验证码，最多尝试8次，每次间隔8秒 (进度: 100%)
2025-08-05 14:49:45 线程1：[信息] [信息] 线程1第1次尝试获取验证码...（剩余7次尝试） (进度: 100%)
2025-08-05 14:49:45 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-05 14:49:46 线程1：[信息] [信息] 线程1验证码获取成功: 2522 (进度: 100%)
2025-08-05 14:49:46 [信息] 线程1手机号码已加入释放队列: +526463856089 (原因: 获取验证码成功)
2025-08-05 14:49:46 线程1：[信息] [信息] 线程1验证码获取成功: 2522，立即填入验证码... (进度: 100%)
2025-08-05 14:49:46 线程1：[信息] [信息] 第七页：找到验证码输入框 (Verify code) (进度: 100%)
2025-08-05 14:49:46 线程1：[信息] [信息] 线程1已自动填入手机验证码: 2522 (进度: 100%)
2025-08-05 14:49:47 线程1：[信息] [信息] 线程1正在自动点击Continue按钮... (进度: 100%)
2025-08-05 14:49:47 线程1：[信息] [信息] 线程1手机验证码验证完成，继续执行后续步骤... (进度: 100%)
2025-08-05 14:49:48 线程3：[信息] [信息] 第1次图形验证码识别结果错误，等待新验证码 (进度: 100%)
2025-08-05 14:49:48 线程3：[信息] [信息] 第六页第1次识别异常: 验证码错误 (进度: 100%)
2025-08-05 14:49:50 线程3：[信息] [信息] 第六页第2次尝试自动识别图形验证码... (进度: 100%)
2025-08-05 14:49:51 线程1：[信息] [信息] 验证码验证完成，等待页面加载... (进度: 100%)
2025-08-05 14:49:51 线程1：[信息] [信息] 已点击完成注册按钮，正在处理... (进度: 100%)
2025-08-05 14:49:51 线程1：[信息] [信息] 自动模式验证完成，API手机号码已加入黑名单: 加入黑名单成功 (进度: 100%)
2025-08-05 14:49:52 线程1：[信息] [信息] 等待完成注册页面加载... (进度: 100%)
2025-08-05 14:49:53 线程3：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 31322 字节 (进度: 100%)
2025-08-05 14:49:53 线程3：[信息] [信息] ✅ 图片验证通过：200x70px，31322字节，复杂度符合要求 (进度: 100%)
2025-08-05 14:49:53 线程3：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-05 14:49:55 线程3：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"sn4s6m"},"taskId":"650a3ce6-71c8-11f0-be5c-d6daa566c6db"} (进度: 100%)
2025-08-05 14:49:55 线程3：[信息] [信息] 第六页第2次识别结果: sn4s6m → 转换为小写: sn4s6m (进度: 100%)
2025-08-05 14:49:55 线程3：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-05 14:49:55 线程3：[信息] [信息] 第六页已填入验证码: sn4s6m (进度: 100%)
2025-08-05 14:49:55 线程3：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-05 14:49:55 线程1：[信息] [信息] 完成注册页面加载完成 (进度: 100%)
2025-08-05 14:49:55 线程1：[信息] [信息] 🔍 正在查找'Go to the AWS Management Console'链接... (进度: 100%)
2025-08-05 14:49:56 [信息] 定时检查发现1个待释放手机号码，开始批量释放
2025-08-05 14:49:56 [信息] 开始释放1个手机号码
2025-08-05 14:49:56 [信息] [手机API] 开始批量释放1个手机号码
2025-08-05 14:49:56 [信息] [手机API] 释放手机号码: +526463856089
2025-08-05 14:49:57 [信息] [手机API] 手机号码释放成功: +526463856089
2025-08-05 14:49:58 [信息] [手机API] 批量释放完成: 成功1个, 失败0个
2025-08-05 14:49:58 [信息] 定时批量释放完成: 批量释放完成: 成功1个, 失败0个
2025-08-05 14:49:58 线程3：[信息] [信息] 第2次图形验证码识别成功 (进度: 100%)
2025-08-05 14:49:58 线程3：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-08-05 14:50:01 线程3：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-08-05 14:50:03 线程1：[信息] [信息] 🖱️ 已点击跳转管理控制台链接 (进度: 100%)
2025-08-05 14:50:03 线程1：[信息] [信息] 🔗 通过导航进入IAM安全凭证页面... (进度: 100%)
2025-08-05 14:50:03 线程1：[信息] [信息] ⏳ 等待管理控制台页面加载，智能检测'更多'按钮（超时20秒）... (进度: 100%)
2025-08-05 14:50:04 线程3：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-05 14:50:04 线程3：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-05 14:50:04 线程3：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-05 14:50:04 线程3：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-08-05 14:50:09 线程3：[信息] [信息] 线程3开始独立获取验证码... (进度: 100%)
2025-08-05 14:50:09 线程3：[信息] [信息] 线程3开始获取验证码，最多尝试8次，每次间隔8秒 (进度: 100%)
2025-08-05 14:50:09 线程3：[信息] [信息] 线程3第1次尝试获取验证码...（剩余7次尝试） (进度: 100%)
2025-08-05 14:50:09 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-05 14:50:10 线程3：[信息] [信息] 线程3验证码获取成功: 8252 (进度: 100%)
2025-08-05 14:50:10 [信息] 线程3手机号码已加入释放队列: +527222483115 (原因: 获取验证码成功)
2025-08-05 14:50:10 线程3：[信息] [信息] 线程3验证码获取成功: 8252，立即填入验证码... (进度: 100%)
2025-08-05 14:50:10 线程3：[信息] [信息] 第七页：找到验证码输入框 (Verify code) (进度: 100%)
2025-08-05 14:50:10 线程3：[信息] [信息] 线程3已自动填入手机验证码: 8252 (进度: 100%)
2025-08-05 14:50:11 线程3：[信息] [信息] 线程3正在自动点击Continue按钮... (进度: 100%)
2025-08-05 14:50:11 线程3：[信息] [信息] 线程3手机验证码验证完成，继续执行后续步骤... (进度: 100%)
2025-08-05 14:50:14 线程3：[信息] [信息] 验证码验证完成，等待页面加载... (进度: 100%)
2025-08-05 14:50:15 线程3：[信息] [信息] 已点击完成注册按钮，正在处理... (进度: 100%)
2025-08-05 14:50:16 线程3：[信息] [信息] 等待完成注册页面加载... (进度: 100%)
2025-08-05 14:50:16 线程3：[信息] [信息] 自动模式验证完成，API手机号码已加入黑名单: 加入黑名单成功 (进度: 100%)
2025-08-05 14:50:19 线程3：[信息] [信息] 完成注册页面加载完成 (进度: 100%)
2025-08-05 14:50:19 线程3：[信息] [信息] 🔍 正在查找'Go to the AWS Management Console'链接... (进度: 100%)
2025-08-05 14:50:23 线程1：[信息] [信息] ⚠️ 20秒内未找到'更多'按钮，继续执行后续流程... (进度: 100%)
2025-08-05 14:50:23 [信息] 20秒内未找到'更多'按钮，但继续执行
2025-08-05 14:50:23 线程1：[信息] [信息] 🔍 智能检测更多按钮... (进度: 100%)
2025-08-05 14:50:23 线程1：[信息] [信息] 🔍 第1次检查更多按钮... (进度: 100%)
2025-08-05 14:50:24 线程1：[信息] [信息] ⚠️ 第1次检查未找到更多按钮 (进度: 100%)
2025-08-05 14:50:24 [信息] 第1次检查未找到更多按钮
2025-08-05 14:50:24 线程1：[信息] [信息] ⏳ 等待3秒后重新检查... (进度: 100%)
2025-08-05 14:50:26 线程3：[信息] [信息] 🖱️ 已点击跳转管理控制台链接 (进度: 100%)
2025-08-05 14:50:26 线程3：[信息] [信息] 🔗 通过导航进入IAM安全凭证页面... (进度: 100%)
2025-08-05 14:50:26 线程3：[信息] [信息] ⏳ 等待管理控制台页面加载，智能检测'更多'按钮（超时20秒）... (进度: 100%)
2025-08-05 14:50:26 [信息] 定时检查发现1个待释放手机号码，开始批量释放
2025-08-05 14:50:26 [信息] 开始释放1个手机号码
2025-08-05 14:50:26 [信息] [手机API] 开始批量释放1个手机号码
2025-08-05 14:50:26 [信息] [手机API] 释放手机号码: +527222483115
2025-08-05 14:50:27 [信息] [手机API] 手机号码释放成功: +527222483115
2025-08-05 14:50:27 [信息] [手机API] 批量释放完成: 成功1个, 失败0个
2025-08-05 14:50:27 [信息] 定时批量释放完成: 批量释放完成: 成功1个, 失败0个
2025-08-05 14:50:27 线程1：[信息] [信息] 🔍 第2次检查更多按钮... (进度: 100%)
2025-08-05 14:50:27 线程1：[信息] [信息] ✅ 第2次检查成功找到更多按钮 (进度: 100%)
2025-08-05 14:50:27 [信息] 第2次检查成功找到更多按钮
2025-08-05 14:50:27 线程1：[信息] [信息] 🖱️ 正在点击更多按钮... (进度: 100%)
2025-08-05 14:50:27 线程1：[信息] [信息] ✅ 成功点击更多按钮 (进度: 100%)
2025-08-05 14:50:27 [信息] 成功点击更多按钮
2025-08-05 14:50:29 线程1：[信息] [信息] 🖱️ 正在点击账户信息按钮... (进度: 100%)
2025-08-05 14:50:29 线程1：[信息] [信息] ✅ 成功点击账户信息按钮 (进度: 100%)
2025-08-05 14:50:29 [信息] 成功点击账户信息按钮
2025-08-05 14:50:30 线程1：[信息] [信息] 🖱️ 正在点击'安全凭证'菜单项... (进度: 100%)
2025-08-05 14:50:30 线程1：[信息] [信息] 🔍 正在定位'安全凭证'链接... (进度: 100%)
2025-08-05 14:50:30 线程1：[信息] [信息] ✅ 成功定位到'安全凭证'链接 (进度: 100%)
2025-08-05 14:50:30 [信息] 成功定位到'安全凭证'链接
2025-08-05 14:50:40 线程1：[信息] [信息] ✅ 成功点击'安全凭证'菜单项 (进度: 100%)
2025-08-05 14:50:40 [信息] 成功点击'安全凭证'链接
2025-08-05 14:50:40 线程1：[信息] [信息] ⏳ 等待IAM安全凭证页面加载（20秒超时，循环检测账单问题和创建密钥按钮）... (进度: 100%)
2025-08-05 14:50:44 线程3：[信息] [信息] ✅ 找到'更多'按钮，页面加载完成 (进度: 100%)
2025-08-05 14:50:44 线程3：[信息] [信息] 🖱️ 正在点击更多按钮... (进度: 100%)
2025-08-05 14:50:44 线程3：[信息] [信息] ✅ 成功点击更多按钮 (进度: 100%)
2025-08-05 14:50:44 [信息] 成功点击更多按钮
2025-08-05 14:50:45 线程3：[信息] [信息] 🖱️ 正在点击账户信息按钮... (进度: 100%)
2025-08-05 14:50:45 线程3：[信息] [信息] ✅ 成功点击账户信息按钮 (进度: 100%)
2025-08-05 14:50:45 [信息] 成功点击账户信息按钮
2025-08-05 14:50:46 线程3：[信息] [信息] 🖱️ 正在点击'安全凭证'菜单项... (进度: 100%)
2025-08-05 14:50:46 线程3：[信息] [信息] 🔍 正在定位'安全凭证'链接... (进度: 100%)
2025-08-05 14:50:46 线程3：[信息] [信息] ✅ 成功定位到'安全凭证'链接 (进度: 100%)
2025-08-05 14:50:46 [信息] 成功定位到'安全凭证'链接
2025-08-05 14:50:53 线程3：[信息] [信息] ✅ 成功点击'安全凭证'菜单项 (进度: 100%)
2025-08-05 14:50:53 [信息] 成功点击'安全凭证'链接
2025-08-05 14:50:53 线程3：[信息] [信息] ⏳ 等待IAM安全凭证页面加载（20秒超时，循环检测账单问题和创建密钥按钮）... (进度: 100%)
2025-08-05 14:51:00 线程1：[信息] [信息] ✅ 找到'创建访问密钥'按钮，页面加载完成 (进度: 100%)
2025-08-05 14:51:00 线程1：[信息] [信息] ✅ 页面状态正常，找到创建密钥按钮，开始密钥提取流程 (进度: 100%)
2025-08-05 14:51:00 线程1：[信息] [信息] 🔍 设置页面缩放为50%... (进度: 100%)
2025-08-05 14:51:01 线程1：[信息] [信息] ✅ 页面缩放设置完成 (进度: 100%)
2025-08-05 14:51:01 [信息] 页面缩放设置为50%完成
2025-08-05 14:51:01 线程1：[信息] [信息] 🔍 正在查找并点击'下一步'按钮关闭向导... (进度: 100%)
2025-08-05 14:51:01 [信息] 开始查找并点击'下一步'按钮关闭向导
2025-08-05 14:51:01 线程1：[信息] [信息] ✅ 找到可见的下一步按钮，使用选择器: Locator(button[data-testid='awsc-nav-services-tooltip-confirm-button']) (进度: 100%)
2025-08-05 14:51:01 [信息] 找到可见的下一步按钮，使用选择器: Locator(button[data-testid='awsc-nav-services-tooltip-confirm-button'])
2025-08-05 14:51:02 线程1：[信息] [信息] ✅ 第一次点击'下一步'按钮完成 (选择器: Locator(button[data-testid='awsc-nav-services-tooltip-confirm-button'])) (进度: 100%)
2025-08-05 14:51:02 [信息] 第一次点击'下一步'按钮完成 (选择器: Locator(button[data-testid='awsc-nav-services-tooltip-confirm-button']))
2025-08-05 14:51:03 线程1：[信息] [信息] ℹ️ 第二次点击时按钮已消失，向导已关闭 (进度: 100%)
2025-08-05 14:51:03 [信息] 第二次点击时按钮已消失，向导已关闭
2025-08-05 14:51:03 线程1：[信息] [信息] ✅ '下一步'按钮点击流程完成 (进度: 100%)
2025-08-05 14:51:03 [信息] '下一步'按钮点击流程完成
2025-08-05 14:51:03 线程1：[信息] [信息] 🔑 第四阶段：开始创建和复制访问密钥 (进度: 100%)
2025-08-05 14:51:03 [信息] 开始创建和复制访问密钥
2025-08-05 14:51:03 线程1：[信息] [信息] 🔑 正在点击'创建访问密钥'按钮... (进度: 100%)
2025-08-05 14:51:03 线程1：[信息] [信息] 🔍 正在定位'创建访问密钥'按钮... (进度: 100%)
2025-08-05 14:51:03 线程1：[信息] [信息] ✅ 成功定位到'创建访问密钥'按钮 (进度: 100%)
2025-08-05 14:51:03 [信息] 成功定位到'创建访问密钥'按钮
2025-08-05 14:51:03 线程1：[信息] [信息] ✅ 成功点击'创建访问密钥'按钮 (进度: 100%)
2025-08-05 14:51:03 [信息] 成功点击'创建访问密钥'按钮
2025-08-05 14:51:04 线程3：[信息] [信息] ✅ 找到'创建访问密钥'按钮，页面加载完成 (进度: 100%)
2025-08-05 14:51:04 线程3：[信息] [信息] ✅ 页面状态正常，找到创建密钥按钮，开始密钥提取流程 (进度: 100%)
2025-08-05 14:51:04 线程3：[信息] [信息] 🔍 设置页面缩放为50%... (进度: 100%)
2025-08-05 14:51:05 线程3：[信息] [信息] ✅ 页面缩放设置完成 (进度: 100%)
2025-08-05 14:51:05 [信息] 页面缩放设置为50%完成
2025-08-05 14:51:05 线程3：[信息] [信息] 🔍 正在查找并点击'下一步'按钮关闭向导... (进度: 100%)
2025-08-05 14:51:05 [信息] 开始查找并点击'下一步'按钮关闭向导
2025-08-05 14:51:05 线程3：[信息] [信息] ℹ️ 未检测到向导元素，向导可能已自动关闭 (进度: 100%)
2025-08-05 14:51:05 [信息] 未检测到向导元素，向导可能已自动关闭
2025-08-05 14:51:05 线程3：[信息] [信息] 🔑 第四阶段：开始创建和复制访问密钥 (进度: 100%)
2025-08-05 14:51:05 [信息] 开始创建和复制访问密钥
2025-08-05 14:51:05 线程3：[信息] [信息] 🔑 正在点击'创建访问密钥'按钮... (进度: 100%)
2025-08-05 14:51:05 线程3：[信息] [信息] 🔍 正在定位'创建访问密钥'按钮... (进度: 100%)
2025-08-05 14:51:05 线程3：[信息] [信息] ✅ 成功定位到'创建访问密钥'按钮 (进度: 100%)
2025-08-05 14:51:05 [信息] 成功定位到'创建访问密钥'按钮
2025-08-05 14:51:05 线程1：[信息] [信息] ☑️ 正在勾选确认复选框... (进度: 100%)
2025-08-05 14:51:05 线程3：[信息] [信息] ✅ 成功点击'创建访问密钥'按钮 (进度: 100%)
2025-08-05 14:51:05 [信息] 成功点击'创建访问密钥'按钮
2025-08-05 14:51:07 线程1：[信息] [信息] ✅ 使用id属性定位到确认复选框 (进度: 100%)
2025-08-05 14:51:07 [信息] 使用id属性定位到确认复选框
2025-08-05 14:51:07 线程3：[信息] [信息] ☑️ 正在勾选确认复选框... (进度: 100%)
2025-08-05 14:51:07 线程1：[信息] [信息] ✅ 成功勾选确认复选框 (进度: 100%)
2025-08-05 14:51:07 [信息] 成功勾选确认复选框
2025-08-05 14:51:08 线程1：[信息] [信息] 🔑 正在点击'创建访问密钥'确认按钮... (进度: 100%)
2025-08-05 14:51:09 线程1：[信息] [信息] ✅ 成功点击'创建访问密钥'确认按钮 (进度: 100%)
2025-08-05 14:51:09 [信息] 成功点击'创建访问密钥'确认按钮
2025-08-05 14:51:11 线程3：[信息] [信息] ✅ 使用id属性定位到确认复选框 (进度: 100%)
2025-08-05 14:51:11 [信息] 使用id属性定位到确认复选框
2025-08-05 14:51:11 线程3：[信息] [信息] ✅ 成功勾选确认复选框 (进度: 100%)
2025-08-05 14:51:11 [信息] 成功勾选确认复选框
2025-08-05 14:51:12 线程1：[信息] [信息] 📋 开始复制访问密钥... (进度: 100%)
2025-08-05 14:51:12 [信息] 开始复制访问密钥
2025-08-05 14:51:12 线程3：[信息] [信息] 🔑 正在点击'创建访问密钥'确认按钮... (进度: 100%)
2025-08-05 14:51:12 线程3：[信息] [信息] ✅ 成功点击'创建访问密钥'确认按钮 (进度: 100%)
2025-08-05 14:51:12 [信息] 成功点击'创建访问密钥'确认按钮
2025-08-05 14:51:14 线程1：[信息] [信息] 🔍 方法2找到 2 个单元格 (进度: 100%)
2025-08-05 14:51:14 [信息] 方法2找到 2 个单元格
2025-08-05 14:51:14 线程1：[信息] [信息] 🔍 总共找到 2 个单元格，开始分析... (进度: 100%)
2025-08-05 14:51:14 [信息] 总共找到 2 个单元格，开始分析
2025-08-05 14:51:14 [信息] 单元格[0]: 'AKIAR2B7G3CL7NXSMOSI'
2025-08-05 14:51:14 [信息] 单元格[1]: '***************Mostrar'
2025-08-05 14:51:14 线程1：[信息] [信息] 📋 正在查找并复制访问密钥... (进度: 100%)
2025-08-05 14:51:14 线程1：[信息] [信息] 🔍 解决向导阻挡... (进度: 100%)
2025-08-05 14:51:14 线程1：[信息] [信息] 🔍 正在查找并点击'下一步'按钮关闭向导... (进度: 100%)
2025-08-05 14:51:14 [信息] 开始查找并点击'下一步'按钮关闭向导
2025-08-05 14:51:14 线程1：[信息] [信息] ℹ️ 未检测到向导元素，向导可能已自动关闭 (进度: 100%)
2025-08-05 14:51:14 [信息] 未检测到向导元素，向导可能已自动关闭
2025-08-05 14:51:14 线程1：[信息] [信息] ✅ 找到访问密钥: AKIAR2B7G3CL7NXSMOSI (进度: 100%)
2025-08-05 14:51:14 [信息] 找到访问密钥: AKIAR2B7G3CL7NXSMOSI
2025-08-05 14:51:14 线程1：[信息] [信息] ✅ 方法1成功点击访问密钥复制按钮 (进度: 100%)
2025-08-05 14:51:14 [信息] 方法1成功点击访问密钥复制按钮
2025-08-05 14:51:15 线程3：[信息] [信息] 📋 开始复制访问密钥... (进度: 100%)
2025-08-05 14:51:15 [信息] 开始复制访问密钥
2025-08-05 14:51:15 线程1：[信息] [信息] 📋 正在查找并复制秘密访问密钥... (进度: 100%)
2025-08-05 14:51:15 线程1：[信息] [信息] ✅ 找到秘密访问密钥: ***************Mostrar (进度: 100%)
2025-08-05 14:51:15 [信息] 找到秘密访问密钥: ***************Mostrar
2025-08-05 14:51:15 线程1：[信息] [信息] 🔍 解决向导阻挡... (进度: 100%)
2025-08-05 14:51:15 线程1：[信息] [信息] 🔍 正在查找并点击'下一步'按钮关闭向导... (进度: 100%)
2025-08-05 14:51:15 [信息] 开始查找并点击'下一步'按钮关闭向导
2025-08-05 14:51:15 线程1：[信息] [信息] ℹ️ 未检测到向导元素，向导可能已自动关闭 (进度: 100%)
2025-08-05 14:51:15 [信息] 未检测到向导元素，向导可能已自动关闭
2025-08-05 14:51:15 线程1：[信息] [信息] 🔍 检测到隐藏的秘密访问密钥，尝试点击显示按钮... (进度: 100%)
2025-08-05 14:51:15 [信息] 检测到隐藏的秘密访问密钥，尝试点击显示按钮
2025-08-05 14:51:16 线程1：[信息] [信息] ✅ 使用TestId定位到显示按钮 (进度: 100%)
2025-08-05 14:51:16 [信息] 使用TestId定位到显示按钮
2025-08-05 14:51:17 线程1：[信息] [信息] ✅ 显示按钮点击成功，新文本: yPJ4fx1rVSav6AHoec9ZmrJGtfJLFgGyyA67jJLrOcultar (进度: 100%)
2025-08-05 14:51:17 [信息] 显示按钮点击成功，新文本: yPJ4fx1rVSav6AHoec9ZmrJGtfJLFgGyyA67jJLrOcultar
2025-08-05 14:51:17 线程1：[信息] [信息] ✅ 直接从显示文本提取秘密访问密钥: yPJ4fx1rVSav6AHoec9ZmrJGtfJLFgGyyA67jJLrOcultar (进度: 100%)
2025-08-05 14:51:17 [信息] 直接从显示文本提取秘密访问密钥: yPJ4fx1rVSav6AHoec9ZmrJGtfJLFgGyyA67jJLrOcultar
2025-08-05 14:51:17 线程1：[信息] [信息] ✅ 访问密钥复制完成 (进度: 100%)
2025-08-05 14:51:17 [信息] 访问密钥复制完成 - AccessKey: AKIAR2B7G3CL7NXSMOSI, SecretKey: yPJ4fx1rVSav6AHoec9ZmrJGtfJLFg
2025-08-05 14:51:17 线程3：[信息] [信息] 🔍 方法2找到 2 个单元格 (进度: 100%)
2025-08-05 14:51:17 [信息] 方法2找到 2 个单元格
2025-08-05 14:51:17 线程3：[信息] [信息] 🔍 总共找到 2 个单元格，开始分析... (进度: 100%)
2025-08-05 14:51:17 [信息] 总共找到 2 个单元格，开始分析
2025-08-05 14:51:17 [信息] 单元格[0]: 'AKIAXB5BQ7VD2DO2E2LF'
2025-08-05 14:51:17 [信息] 单元格[1]: '***************Mostrar'
2025-08-05 14:51:17 线程3：[信息] [信息] 📋 正在查找并复制访问密钥... (进度: 100%)
2025-08-05 14:51:17 线程3：[信息] [信息] 🔍 解决向导阻挡... (进度: 100%)
2025-08-05 14:51:17 线程3：[信息] [信息] 🔍 正在查找并点击'下一步'按钮关闭向导... (进度: 100%)
2025-08-05 14:51:17 [信息] 开始查找并点击'下一步'按钮关闭向导
2025-08-05 14:51:17 线程3：[信息] [信息] ✅ 找到可见的下一步按钮，使用选择器: Locator(button[data-testid='awsc-nav-services-tooltip-confirm-button']) (进度: 100%)
2025-08-05 14:51:17 [信息] 找到可见的下一步按钮，使用选择器: Locator(button[data-testid='awsc-nav-services-tooltip-confirm-button'])
2025-08-05 14:51:17 线程3：[信息] [信息] ✅ 第一次点击'下一步'按钮完成 (选择器: Locator(button[data-testid='awsc-nav-services-tooltip-confirm-button'])) (进度: 100%)
2025-08-05 14:51:17 [信息] 第一次点击'下一步'按钮完成 (选择器: Locator(button[data-testid='awsc-nav-services-tooltip-confirm-button']))
2025-08-05 14:51:18 线程1：[信息] [信息] 🖱️ 正在点击'已完成'按钮... (进度: 100%)
2025-08-05 14:51:19 线程3：[信息] [信息] ℹ️ 第二次点击时按钮已消失，向导已关闭 (进度: 100%)
2025-08-05 14:51:19 [信息] 第二次点击时按钮已消失，向导已关闭
2025-08-05 14:51:19 线程3：[信息] [信息] ✅ '下一步'按钮点击流程完成 (进度: 100%)
2025-08-05 14:51:19 [信息] '下一步'按钮点击流程完成
2025-08-05 14:51:19 线程3：[信息] [信息] ✅ 找到访问密钥: AKIAXB5BQ7VD2DO2E2LF (进度: 100%)
2025-08-05 14:51:19 [信息] 找到访问密钥: AKIAXB5BQ7VD2DO2E2LF
2025-08-05 14:51:19 线程1：[信息] [信息] ✅ 成功点击'已完成'按钮 (进度: 100%)
2025-08-05 14:51:19 [信息] 成功点击'已完成'按钮
2025-08-05 14:51:19 线程1：[信息] [信息] ✅ 密钥已保存到数据对象 - AccessKey: AKIAR2B7G3CL7NXSMOSI, SecretKey: yPJ4fx1rVSav6AHoec9ZmrJGtfJLFg (进度: 100%)
2025-08-05 14:51:19 [信息] 密钥已保存到数据对象 - AccessKey: AKIAR2B7G3CL7NXSMOSI, SecretKey: yPJ4fx1rVSav6AHoec9ZmrJGtfJLFg
2025-08-05 14:51:20 线程1：[信息] [信息] 🖱️ 正在点击'继续'按钮... (进度: 100%)
2025-08-05 14:51:23 线程1：[信息] [信息] ❌ 点击'继续'按钮失败: Timeout 3000ms exceeded.
Call log:
  - waiting for Locator("button[data-awsui-analytics*='navigate']") to be visible (进度: 100%)
2025-08-05 14:51:23 [信息] 点击'继续'按钮失败: Timeout 3000ms exceeded.
Call log:
  - waiting for Locator("button[data-awsui-analytics*='navigate']") to be visible
2025-08-05 14:51:23 线程1：[信息] [信息] 🔐 开始设置MFA设备... (进度: 100%)
2025-08-05 14:51:23 [信息] 开始设置MFA设备
2025-08-05 14:51:23 线程1：[信息] [信息] 🖱️ 正在点击'分配 MFA 设备'按钮... (进度: 100%)
2025-08-05 14:51:23 线程1：[信息] [信息] ✅ 成功点击'分配 MFA 设备'按钮 (进度: 100%)
2025-08-05 14:51:23 [信息] 成功点击'分配 MFA 设备'按钮
2025-08-05 14:51:23 线程1：[信息] [信息] ⏳ 等待MFA设备设置页面加载（20秒超时）... (进度: 100%)
2025-08-05 14:51:26 线程1：[信息] [信息] ✅ 找到'设备名称'输入框，页面加载完成 (进度: 100%)
2025-08-05 14:51:26 [信息] 通过稳定元素定位找到设备名称输入框
2025-08-05 14:51:26 线程1：[信息] [信息] 📝 正在输入设备名称... (进度: 100%)
2025-08-05 14:51:26 线程1：[信息] [信息] ✅ 定位到设备名称输入框 (进度: 100%)
2025-08-05 14:51:27 线程1：[信息] [信息] ✅ 成功输入设备名称: joannkey (进度: 100%)
2025-08-05 14:51:27 [信息] 成功输入设备名称: joannkey
2025-08-05 14:51:27 线程1：[信息] [信息] ☑️ 正在选择'身份验证器应用程序'... (进度: 100%)
2025-08-05 14:51:27 线程1：[信息] [信息] ✅ 成功选择'身份验证器应用程序' (进度: 100%)
2025-08-05 14:51:27 [信息] 成功选择'身份验证器应用程序'
2025-08-05 14:51:27 线程1：[信息] [信息] 🖱️ 正在点击'下一步'按钮... (进度: 100%)
2025-08-05 14:51:27 线程1：[信息] [信息] ✅ 成功点击'下一步'按钮 (进度: 100%)
2025-08-05 14:51:27 [信息] 成功点击'下一步'按钮
2025-08-05 14:51:27 线程1：[信息] [信息] ⏳ 等待MFA密钥页面加载（20秒超时）... (进度: 100%)
2025-08-05 14:51:48 线程1：[信息] [信息] ⚠️ 20秒内未找到'显示密钥'按钮，需要手动处理 (进度: 100%)
2025-08-05 14:51:48 线程1：[信息] [信息] ⚠️ 显示密钥页面加载超时，开始元素抓取 (进度: 100%)
2025-08-05 14:51:48 [信息] 显示密钥页面加载超时，开始元素抓取
2025-08-05 14:51:48 线程1：[信息] [信息] 🔍 开始抓取MFA页面元素... (进度: 100%)
2025-08-05 14:51:48 [信息] 开始抓取MFA页面元素
2025-08-05 14:51:48 线程1：[信息] [信息] 🔍 正在抓取设备名称输入框... (进度: 100%)
2025-08-05 14:51:48 线程1：[信息] [信息] ✅ 通过AWS表单输入框找到设备名称输入框 (进度: 100%)
2025-08-05 14:51:48 线程1：[信息] [信息] ✅ 成功抓取到 1 个元素 (进度: 100%)
2025-08-05 14:51:48 [信息] MFA页面元素抓取结果:
// 设备名称输入框 - 通过AWS表单输入框找到
await page.GetByTestId("Código de MFA 1")
await page.Locator("[placeholder='Código de MFA 1']")
await page.Locator("input[type='text']")
2025-08-05 14:51:48 线程1：[信息] [信息] 元素抓取完成，页面加载完成后，手动点击继续注册 (进度: 100%)
2025-08-05 14:51:48 线程1：[信息] [信息] MFA页面需要手动处理，等待用户操作 (进度: 100%)
2025-08-05 14:51:49 [信息] 方法1失败: Timeout 30000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Cell, new() { Name = "AKIAXB5BQ7VD2DO2E2LF" }).GetByTestId("copy-button")
  -   locator resolved to <button type="submit" data-testid="copy-button" data-aws…>…</button>
  - attempting click action
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <span id="awsc-feedback" title="Comentarios">Comentarios</span> from <div id="f" role="contentinfo" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #1
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div class="_awsc-footer__inner__content__center_112v…>…</div> from <div id="f" role="contentinfo" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #2
  -   waiting 20ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div class="globalNav-2220">…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #3
  -   waiting 100ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <a target="_top" id="nav-home-link" data-testid="na…>…</a> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #4
  -   waiting 100ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <a target="_top" id="nav-home-link" data-testid="na…>…</a> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #5
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div class="_awsc-footer__inner__content__center_112v…>…</div> from <div id="f" role="contentinfo" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #6
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div class="globalNav-2220">…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #7
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <a target="_top" id="nav-home-link" data-testid="na…>…</a> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #8
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <a target="_top" id="nav-home-link" data-testid="na…>…</a> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #9
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div class="_awsc-footer__inner__content__center_112v…>…</div> from <div id="f" role="contentinfo" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #10
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div class="globalNav-2220">…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #11
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <a target="_top" id="nav-home-link" data-testid="na…>…</a> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #12
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <a target="_top" id="nav-home-link" data-testid="na…>…</a> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #13
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div class="_awsc-footer__inner__content__center_112v…>…</div> from <div id="f" role="contentinfo" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #14
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div class="globalNav-2220">…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #15
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <a target="_top" id="nav-home-link" data-testid="na…>…</a> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #16
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <a target="_top" id="nav-home-link" data-testid="na…>…</a> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #17
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div class="_awsc-footer__inner__content__center_112v…>…</div> from <div id="f" role="contentinfo" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #18
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div class="globalNav-2220">…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #19
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <a target="_top" id="nav-home-link" data-testid="na…>…</a> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #20
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <a target="_top" id="nav-home-link" data-testid="na…>…</a> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #21
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div class="_awsc-footer__inner__content__center_112v…>…</div> from <div id="f" role="contentinfo" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #22
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div class="globalNav-2220">…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #23
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <a target="_top" id="nav-home-link" data-testid="na…>…</a> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #24
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <a target="_top" id="nav-home-link" data-testid="na…>…</a> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #25
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div class="_awsc-footer__inner__content__center_112v…>…</div> from <div id="f" role="contentinfo" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #26
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div class="globalNav-2220">…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #27
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <a target="_top" id="nav-home-link" data-testid="na…>…</a> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #28
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <a target="_top" id="nav-home-link" data-testid="na…>…</a> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #29
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div class="_awsc-footer__inner__content__center_112v…>…</div> from <div id="f" role="contentinfo" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #30
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div class="globalNav-2220">…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #31
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <a target="_top" id="nav-home-link" data-testid="na…>…</a> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #32
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <a target="_top" id="nav-home-link" data-testid="na…>…</a> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #33
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div class="_awsc-footer__inner__content__center_112v…>…</div> from <div id="f" role="contentinfo" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #34
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div class="globalNav-2220">…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #35
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <a target="_top" id="nav-home-link" data-testid="na…>…</a> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #36
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <a target="_top" id="nav-home-link" data-testid="na…>…</a> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #37
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div class="_awsc-footer__inner__content__center_112v…>…</div> from <div id="f" role="contentinfo" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #38
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div class="globalNav-2220">…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #39
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <a target="_top" id="nav-home-link" data-testid="na…>…</a> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #40
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <a target="_top" id="nav-home-link" data-testid="na…>…</a> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #41
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div class="_awsc-footer__inner__content__center_112v…>…</div> from <div id="f" role="contentinfo" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #42
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div class="globalNav-2220">…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #43
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <a target="_top" id="nav-home-link" data-testid="na…>…</a> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #44
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <a target="_top" id="nav-home-link" data-testid="na…>…</a> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #45
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div class="_awsc-footer__inner__content__center_112v…>…</div> from <div id="f" role="contentinfo" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #46
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div class="globalNav-2220">…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #47
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <a target="_top" id="nav-home-link" data-testid="na…>…</a> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #48
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <a target="_top" id="nav-home-link" data-testid="na…>…</a> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #49
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div class="_awsc-footer__inner__content__center_112v…>…</div> from <div id="f" role="contentinfo" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #50
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div class="globalNav-2220">…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #51
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <a target="_top" id="nav-home-link" data-testid="na…>…</a> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #52
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <a target="_top" id="nav-home-link" data-testid="na…>…</a> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #53
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div class="_awsc-footer__inner__content__center_112v…>…</div> from <div id="f" role="contentinfo" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #54
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div class="globalNav-2220">…</div> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #55
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <a target="_top" id="nav-home-link" data-testid="na…>…</a> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #56
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <a target="_top" id="nav-home-link" data-testid="na…>…</a> from <div id="h" role="navigation" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #57
  -   waiting 500ms
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   <div class="_awsc-footer__inner__content__center_112v…>…</div> from <div id="f" role="contentinfo" ng-non-bindable="">…</div> subtree intercepts pointer events
  - retrying click action, attempt #58
  -   waiting 500ms
2025-08-05 14:51:50 [信息] 方法2失败: Error: strict mode violation: Locator("td").First.Locator("xpath=ancestor::tr[1]").GetByTestId("copy-button") resolved to 2 elements:
    1) <button type="submit" data-testid="copy-button" data-aws…>…</button> aka GetByRole(AriaRole.Cell, new() { Name = "AKIAXB5BQ7VD2DO2E2LF" }).GetByTestId("copy-button")
    2) <button type="submit" data-testid="copy-button" data-aws…>…</button> aka GetByRole(AriaRole.Cell, new() { Name = "*************** Mostrar" }).GetByTestId("copy-button")

Call log:
  - waiting for Locator("td").First.Locator("xpath=ancestor::tr[1]").GetByTestId("copy-button")
2025-08-05 14:51:50 线程3：[信息] [信息] 🔍 解决向导阻挡... (进度: 100%)
2025-08-05 14:51:50 线程3：[信息] [信息] 🔍 正在查找并点击'下一步'按钮关闭向导... (进度: 100%)
2025-08-05 14:51:50 [信息] 开始查找并点击'下一步'按钮关闭向导
2025-08-05 14:51:50 线程3：[信息] [信息] ℹ️ 未检测到向导元素，向导可能已自动关闭 (进度: 100%)
2025-08-05 14:51:50 [信息] 未检测到向导元素，向导可能已自动关闭
2025-08-05 14:51:50 线程3：[信息] [信息] ❌ 未找到访问密钥，值将留空 (进度: 100%)
2025-08-05 14:51:50 [信息] 未找到访问密钥，值将留空
2025-08-05 14:51:51 线程3：[信息] [信息] 📋 正在查找并复制秘密访问密钥... (进度: 100%)
2025-08-05 14:51:51 线程3：[信息] [信息] ✅ 找到秘密访问密钥: ***************Mostrar (进度: 100%)
2025-08-05 14:51:51 [信息] 找到秘密访问密钥: ***************Mostrar
2025-08-05 14:51:51 线程3：[信息] [信息] 🔍 解决向导阻挡... (进度: 100%)
2025-08-05 14:51:51 线程3：[信息] [信息] 🔍 正在查找并点击'下一步'按钮关闭向导... (进度: 100%)
2025-08-05 14:51:51 [信息] 开始查找并点击'下一步'按钮关闭向导
2025-08-05 14:51:51 线程3：[信息] [信息] ℹ️ 未检测到向导元素，向导可能已自动关闭 (进度: 100%)
2025-08-05 14:51:51 [信息] 未检测到向导元素，向导可能已自动关闭
2025-08-05 14:51:51 线程3：[信息] [信息] 🔍 检测到隐藏的秘密访问密钥，尝试点击显示按钮... (进度: 100%)
2025-08-05 14:51:51 [信息] 检测到隐藏的秘密访问密钥，尝试点击显示按钮
2025-08-05 14:51:51 线程3：[信息] [信息] ✅ 使用TestId定位到显示按钮 (进度: 100%)
2025-08-05 14:51:51 [信息] 使用TestId定位到显示按钮
2025-08-05 14:52:09 线程3：[信息] [信息] ✅ 显示按钮点击成功，新文本: KjaZz1kmi9jvbHk+HJPK+nEkOGqmVR3tYlLPSGpROcultar (进度: 100%)
2025-08-05 14:52:09 [信息] 显示按钮点击成功，新文本: KjaZz1kmi9jvbHk+HJPK+nEkOGqmVR3tYlLPSGpROcultar
2025-08-05 14:52:09 线程3：[信息] [信息] ✅ 直接从显示文本提取秘密访问密钥: KjaZz1kmi9jvbHk+HJPK+nEkOGqmVR3tYlLPSGpROcultar (进度: 100%)
2025-08-05 14:52:09 [信息] 直接从显示文本提取秘密访问密钥: KjaZz1kmi9jvbHk+HJPK+nEkOGqmVR3tYlLPSGpROcultar
2025-08-05 14:52:09 线程3：[信息] [信息] ✅ 访问密钥复制完成 (进度: 100%)
2025-08-05 14:52:09 [信息] 访问密钥复制完成 - AccessKey: , SecretKey: KjaZz1kmi9jvbHk+HJPK+nEkOGqmVR
2025-08-05 14:52:10 线程3：[信息] [信息] 🖱️ 正在点击'已完成'按钮... (进度: 100%)
2025-08-05 14:52:11 线程3：[信息] [信息] ✅ 成功点击'已完成'按钮 (进度: 100%)
2025-08-05 14:52:11 [信息] 成功点击'已完成'按钮
2025-08-05 14:52:11 线程3：[信息] [信息] ✅ 密钥已保存到数据对象 - AccessKey: , SecretKey: KjaZz1kmi9jvbHk+HJPK+nEkOGqmVR (进度: 100%)
2025-08-05 14:52:11 [信息] 密钥已保存到数据对象 - AccessKey: , SecretKey: KjaZz1kmi9jvbHk+HJPK+nEkOGqmVR
2025-08-05 14:52:12 线程3：[信息] [信息] 🖱️ 正在点击'继续'按钮... (进度: 100%)
2025-08-05 14:52:15 线程3：[信息] [信息] ❌ 点击'继续'按钮失败: Timeout 3000ms exceeded.
Call log:
  - waiting for Locator("button[data-awsui-analytics*='navigate']") to be visible (进度: 100%)
2025-08-05 14:52:15 [信息] 点击'继续'按钮失败: Timeout 3000ms exceeded.
Call log:
  - waiting for Locator("button[data-awsui-analytics*='navigate']") to be visible
2025-08-05 14:52:15 线程3：[信息] [信息] 🔐 开始设置MFA设备... (进度: 100%)
2025-08-05 14:52:15 [信息] 开始设置MFA设备
2025-08-05 14:52:15 线程3：[信息] [信息] 🖱️ 正在点击'分配 MFA 设备'按钮... (进度: 100%)
2025-08-05 14:52:15 线程3：[信息] [信息] ✅ 成功点击'分配 MFA 设备'按钮 (进度: 100%)
2025-08-05 14:52:15 [信息] 成功点击'分配 MFA 设备'按钮
2025-08-05 14:52:15 线程3：[信息] [信息] ⏳ 等待MFA设备设置页面加载（20秒超时）... (进度: 100%)
2025-08-05 14:52:17 线程3：[信息] [信息] ✅ 通过AWS UI找到'设备名称'输入框，页面加载完成 (进度: 100%)
2025-08-05 14:52:17 [信息] 通过AWS UI元素定位找到设备名称输入框
2025-08-05 14:52:17 线程3：[信息] [信息] 📝 正在输入设备名称... (进度: 100%)
2025-08-05 14:52:17 线程3：[信息] [信息] ✅ 定位到设备名称输入框 (进度: 100%)
2025-08-05 14:52:18 线程3：[信息] [信息] ✅ 成功输入设备名称: joannkey (进度: 100%)
2025-08-05 14:52:18 [信息] 成功输入设备名称: joannkey
2025-08-05 14:52:18 线程3：[信息] [信息] ☑️ 正在选择'身份验证器应用程序'... (进度: 100%)
2025-08-05 14:52:18 线程3：[信息] [信息] ✅ 成功选择'身份验证器应用程序' (进度: 100%)
2025-08-05 14:52:18 [信息] 成功选择'身份验证器应用程序'
2025-08-05 14:52:18 线程3：[信息] [信息] 🖱️ 正在点击'下一步'按钮... (进度: 100%)
2025-08-05 14:52:18 线程3：[信息] [信息] ✅ 成功点击'下一步'按钮 (进度: 100%)
2025-08-05 14:52:18 [信息] 成功点击'下一步'按钮
2025-08-05 14:52:18 线程3：[信息] [信息] ⏳ 等待MFA密钥页面加载（20秒超时）... (进度: 100%)
2025-08-05 14:52:27 [信息] 获取线程1当前数据: <EMAIL>
2025-08-05 14:52:27 线程1：[信息] 终止时正在处理的数据: <EMAIL>
2025-08-05 14:52:27 线程1：[信息] 数据详情: <EMAIL>|8QvsaWd2|Gonzalez Jairo|CAP|Calle san Martn # 568|Antofagasta|Taltal|1300000|4757741009487469|04|27|935|Gonzalez Jairo|O392G6j070|CL
2025-08-05 14:52:27 线程1：[信息] 多线程终止 - 复制密钥信息到剪贴板
2025-08-05 14:52:27 线程1：[信息] [信息] 检测到密钥信息，复制完整注册数据 (进度: 100%)
2025-08-05 14:52:27 [信息] 手动终止 - 检测到密钥信息，复制完整注册数据
2025-08-05 14:52:27 线程1：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：O392G6j070 ③AWS密码：8QvsaWd2 ④访问密钥：AKIAR2B7G3CL7NXSMOSI ⑤秘密访问密钥：yPJ4fx1rVSav6AHoec9ZmrJGtfJLFg ⑥MFA信息：   //手动终止 (进度: 100%)
2025-08-05 14:52:27 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：O392G6j070 ③AWS密码：8QvsaWd2 ④访问密钥：AKIAR2B7G3CL7NXSMOSI ⑤秘密访问密钥：yPJ4fx1rVSav6AHoec9ZmrJGtfJLFg ⑥MFA信息：   //手动终止
2025-08-05 14:52:27 线程1：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：O392G6j070 ③AWS密码：8QvsaWd2 ④访问密钥：AKIAR2B7G3CL7NXSMOSI ⑤秘密访问密钥：yPJ4fx1rVSav6AHoec9ZmrJGtfJLFg ⑥MFA信息：   //手动终止
2025-08-05 14:52:27 [信息] 线程1请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：O392G6j070 ③AWS密码：8QvsaWd2 ④访问密钥：AKIAR2B7G3CL7NXSMOSI ⑤秘密访问密钥：yPJ4fx1rVSav6AHoec9ZmrJGtfJLFg ⑥MFA信息：   //手动终止
2025-08-05 14:52:27 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：O392G6j070 ③AWS密码：8QvsaWd2 ④访问密钥：AKIAR2B7G3CL7NXSMOSI ⑤秘密访问密钥：yPJ4fx1rVSav6AHoec9ZmrJGtfJLFg ⑥MFA信息：   //手动终止
2025-08-05 14:52:27 [信息] 线程1剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：O392G6j070 ③AWS密码：8QvsaWd2 ④访问密钥：AKIAR2B7G3CL7NXSMOSI ⑤秘密访问密钥：yPJ4fx1rVSav6AHoec9ZmrJGtfJLFg ⑥MFA信息：   //手动终止
2025-08-05 14:52:27 线程1：[信息] [信息] 注册已终止 (进度: 100%)
2025-08-05 14:52:27 线程1：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-08-05 14:52:27 线程1：[信息] [信息] 多线程模式 - 跳过重复数据复制 (进度: 100%)
2025-08-05 14:52:27 [信息] 多线程模式 - 跳过重复数据复制，数据已在CopyTerminatedRegistrationInfoAsync中处理
2025-08-05 14:52:27 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-08-05 14:52:27 线程1：[信息] 临时目录已清理: C:\Users\<USER>\AppData\Local\Temp\AWS_Thread_1_20250805_144656
2025-08-05 14:52:27 线程1：[信息] 已终止
2025-08-05 14:52:27 [信息] 线程1已终止
2025-08-05 14:52:27 [信息] 开始处理线程1终止数据，共1个数据
2025-08-05 14:52:27 [信息] 处理线程1终止数据: <EMAIL>
2025-08-05 14:52:27 [信息] 从注册数据列表中移除: <EMAIL>
2025-08-05 14:52:27 [信息] 线程1终止 - 数据已移动到终止列表: <EMAIL>
2025-08-05 14:52:27 [信息] 线程1终止数据处理完成，成功移动1个数据
2025-08-05 14:52:27 [信息] UniformGrid列数已更新为: 1
2025-08-05 14:52:27 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-08-05 14:52:27 [信息] 线程1已终止
2025-08-05 14:52:31 [信息] 获取线程3当前数据: <EMAIL>
2025-08-05 14:52:31 线程3：[信息] 终止时正在处理的数据: <EMAIL>
2025-08-05 14:52:31 线程3：[信息] 数据详情: <EMAIL>|d8mxrZxJ|Gonzalez Jairo|Banco Santander Chile|Calle san Martn # 568|Antofagasta|Taltal|1300000|5331870012302969|10|27|728|Gonzalez Jairo|86V3xou5XO|CL
2025-08-05 14:52:31 线程3：[信息] 多线程终止 - 复制密钥信息到剪贴板
2025-08-05 14:52:31 线程3：[信息] [信息] 检测到密钥信息，复制完整注册数据 (进度: 100%)
2025-08-05 14:52:31 [信息] 手动终止 - 检测到密钥信息，复制完整注册数据
2025-08-05 14:52:31 线程3：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：86V3xou5XO ③AWS密码：d8mxrZxJ ④访问密钥： ⑤秘密访问密钥：KjaZz1kmi9jvbHk+HJPK+nEkOGqmVR ⑥MFA信息：   //手动终止 (进度: 100%)
2025-08-05 14:52:31 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：86V3xou5XO ③AWS密码：d8mxrZxJ ④访问密钥： ⑤秘密访问密钥：KjaZz1kmi9jvbHk+HJPK+nEkOGqmVR ⑥MFA信息：   //手动终止
2025-08-05 14:52:31 线程3：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：86V3xou5XO ③AWS密码：d8mxrZxJ ④访问密钥： ⑤秘密访问密钥：KjaZz1kmi9jvbHk+HJPK+nEkOGqmVR ⑥MFA信息：   //手动终止
2025-08-05 14:52:31 [信息] 线程3请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：86V3xou5XO ③AWS密码：d8mxrZxJ ④访问密钥： ⑤秘密访问密钥：KjaZz1kmi9jvbHk+HJPK+nEkOGqmVR ⑥MFA信息：   //手动终止
2025-08-05 14:52:31 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：86V3xou5XO ③AWS密码：d8mxrZxJ ④访问密钥： ⑤秘密访问密钥：KjaZz1kmi9jvbHk+HJPK+nEkOGqmVR ⑥MFA信息：   //手动终止
2025-08-05 14:52:31 [信息] 线程3剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：86V3xou5XO ③AWS密码：d8mxrZxJ ④访问密钥： ⑤秘密访问密钥：KjaZz1kmi9jvbHk+HJPK+nEkOGqmVR ⑥MFA信息：   //手动终止
2025-08-05 14:52:31 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-05 14:52:31 [信息] 多线程状态已重置
2025-08-05 14:52:31 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-05 14:52:31 [信息] 多线程状态已重置
2025-08-05 14:52:31 线程3：[信息] [信息] 注册已终止 (进度: 100%)
2025-08-05 14:52:31 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-05 14:52:31 [信息] 多线程状态已重置
2025-08-05 14:52:31 线程3：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-08-05 14:52:31 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-05 14:52:31 [信息] 多线程状态已重置
2025-08-05 14:52:31 线程3：[信息] [信息] 多线程模式 - 跳过重复数据复制 (进度: 100%)
2025-08-05 14:52:31 [信息] 多线程模式 - 跳过重复数据复制，数据已在CopyTerminatedRegistrationInfoAsync中处理
2025-08-05 14:52:31 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-05 14:52:31 [信息] 多线程状态已重置
2025-08-05 14:52:31 线程3：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-08-05 14:52:31 线程3：[信息] 临时目录已清理: C:\Users\<USER>\AppData\Local\Temp\AWS_Thread_3_20250805_144656
2025-08-05 14:52:31 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-05 14:52:31 [信息] 多线程状态已重置
2025-08-05 14:52:31 线程3：[信息] 已终止
2025-08-05 14:52:31 [信息] 线程3已终止
2025-08-05 14:52:31 [信息] 开始处理线程3终止数据，共1个数据
2025-08-05 14:52:31 [信息] 处理线程3终止数据: <EMAIL>
2025-08-05 14:52:31 [信息] 从注册数据列表中移除: <EMAIL>
2025-08-05 14:52:31 [信息] 线程3终止 - 数据已移动到终止列表: <EMAIL>
2025-08-05 14:52:31 [信息] 线程3终止数据处理完成，成功移动1个数据
2025-08-05 14:52:31 [信息] UniformGrid列数已更新为: 1
2025-08-05 14:52:31 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 1, 列数: 1
2025-08-05 14:52:31 [信息] 线程3已终止
2025-08-05 14:52:38 线程3：[信息] [信息] ⚠️ 20秒内未找到'显示密钥'按钮，需要手动处理 (进度: 98%)
2025-08-05 14:52:38 线程3：[信息] [信息] ⚠️ 显示密钥页面加载超时，开始元素抓取 (进度: 98%)
2025-08-05 14:52:38 [信息] 显示密钥页面加载超时，开始元素抓取
2025-08-05 14:52:38 线程3：[信息] [信息] 🔍 开始抓取MFA页面元素... (进度: 98%)
2025-08-05 14:52:38 [信息] 开始抓取MFA页面元素
2025-08-05 14:52:38 线程3：[信息] [信息] 🔍 正在抓取设备名称输入框... (进度: 98%)
2025-08-05 14:52:39 线程3：[信息] [信息] ✅ 通过AWS表单输入框找到设备名称输入框 (进度: 100%)
2025-08-05 14:52:39 线程3：[信息] [信息] ✅ 成功抓取到 1 个元素 (进度: 100%)
2025-08-05 14:52:39 [信息] MFA页面元素抓取结果:
// 设备名称输入框 - 通过AWS表单输入框找到
await page.GetByTestId("Código de MFA 1")
await page.Locator("[placeholder='Código de MFA 1']")
await page.Locator("input[type='text']")
2025-08-05 14:52:39 线程3：[信息] [信息] 元素抓取完成，页面加载完成后，手动点击继续注册 (进度: 100%)
2025-08-05 14:52:39 线程3：[信息] [信息] MFA页面需要手动处理，等待用户操作 (进度: 100%)
2025-08-05 14:53:46 [信息] 多线程窗口引用已清理
2025-08-05 14:53:46 [信息] 主窗口已恢复正常状态，多线程窗口引用已清理
2025-08-05 14:53:46 [信息] 多线程管理窗口正在关闭
2025-08-05 14:53:47 [信息] 程序正在退出，开始清理工作...
2025-08-05 14:53:47 [信息] 程序退出，开始清理所有待拉黑千川手机号码
2025-08-05 14:53:47 [信息] 程序退出清理完成: 没有需要拉黑的千川手机号码
2025-08-05 14:53:47 [信息] 程序退出，开始清理所有待释放手机号码
2025-08-05 14:53:47 [信息] 程序退出清理完成: 没有需要释放的手机号码
2025-08-05 14:53:47 [信息] 程序退出清理工作完成
