2025-08-04 18:05:59 [信息] AWS自动注册工具启动
2025-08-04 18:05:59 [信息] 程序版本: 1.0.0.0
2025-08-04 18:05:59 [信息] 启动时间: 2025-08-04 18:05:59
2025-08-04 18:05:59 [信息] 浏览器模式切换: 无痕 Chrome 模式
2025-08-04 18:05:59 [信息] 线程数量已选择: 1
2025-08-04 18:05:59 [信息] 线程数量选择初始化完成
2025-08-04 18:05:59 [信息] 程序初始化完成
2025-08-04 18:06:01 [按钮操作] 选择文件 -> 打开文件选择对话框
2025-08-04 18:06:03 [信息] 已选择文件: C:\Users\<USER>\Desktop\2025-08-04-智利.txt
2025-08-04 18:06:03 [按钮操作] 加载信息 -> 加载数据文件: C:\Users\<USER>\Desktop\2025-08-04-智利.txt
2025-08-04 18:06:04 [信息] 成功加载 12 条数据
2025-08-04 18:06:59 [信息] 线程数量已选择: 3
2025-08-04 18:07:01 [按钮操作] 开始注册 -> 启动注册流程
2025-08-04 18:07:01 [信息] 开始启动多线程注册，线程数量: 3
2025-08-04 18:07:01 [信息] 开始启动多线程注册，线程数量: 3，数据条数: 12
2025-08-04 18:07:01 [信息] 所有线程已停止并清理
2025-08-04 18:07:01 [信息] 正在初始化多线程服务...
2025-08-04 18:07:01 [信息] 榴莲手机API服务已初始化
2025-08-04 18:07:01 [信息] 手机号码管理器已初始化，服务商: Durian，将在第一个线程完成第二页后获取手机号码
2025-08-04 18:07:01 [信息] 多线程服务初始化完成
2025-08-04 18:07:01 [信息] 数据分配完成：共12条数据分配给3个线程
2025-08-04 18:07:01 [信息] 线程1分配到4条数据
2025-08-04 18:07:01 [信息] 线程2分配到4条数据
2025-08-04 18:07:01 [信息] 线程3分配到4条数据
2025-08-04 18:07:01 [信息] 屏幕工作区域: 1280x672
2025-08-04 18:07:01 [信息] 线程1窗口布局: 位置(0, 0), 大小(384x200), 列1行1, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-04 18:07:01 [信息] 线程1获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-04 18:07:01 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 18:07:01 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_012, WebGL=ANGLE (AMD Radeon RX 6600 Direct3D11 vs_5_0 ps_5_0), CPU=8核, RAM=32 GB
2025-08-04 18:07:01 线程1：[信息] 已创建，窗口位置: (0, 0)
2025-08-04 18:07:01 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-04 18:07:01 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-04 18:07:01 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-04 18:07:01 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-04 18:07:01 [信息] 线程1已创建，窗口位置: (0, 0)，指纹: 国家=CL, 时区=America/Santiago
2025-08-04 18:07:01 [信息] 屏幕工作区域: 1280x672
2025-08-04 18:07:01 [信息] 线程2窗口布局: 位置(0, 219), 大小(384x200), 列1行2, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-04 18:07:01 [信息] 线程2获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-04 18:07:01 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 18:07:01 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_003, WebGL=ANGLE (NVIDIA GeForce RTX 3080 Direct3D11 vs_5_0 ps_5_0), CPU=6核, RAM=16 GB
2025-08-04 18:07:01 线程2：[信息] 已创建，窗口位置: (0, 219)
2025-08-04 18:07:01 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-04 18:07:01 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-04 18:07:01 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-04 18:07:01 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-04 18:07:01 [信息] 线程2已创建，窗口位置: (0, 219)，指纹: 国家=CL, 时区=America/Santiago
2025-08-04 18:07:01 [信息] 屏幕工作区域: 1280x672
2025-08-04 18:07:01 [信息] 线程3窗口布局: 位置(0, 438), 大小(384x200), 列1行3, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-04 18:07:01 [信息] 线程3获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-04 18:07:01 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 18:07:01 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_003, WebGL=ANGLE (NVIDIA GeForce RTX 3080 Direct3D11 vs_5_0 ps_5_0), CPU=16核, RAM=14 GB
2025-08-04 18:07:01 线程3：[信息] 已创建，窗口位置: (0, 438)
2025-08-04 18:07:01 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-04 18:07:01 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-04 18:07:01 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-04 18:07:01 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-04 18:07:01 [信息] 线程3已创建，窗口位置: (0, 438)，指纹: 国家=CL, 时区=America/Santiago
2025-08-04 18:07:01 [信息] 多线程注册启动成功，共3个线程
2025-08-04 18:07:01 线程1：[信息] 开始启动注册流程
2025-08-04 18:07:01 线程2：[信息] 开始启动注册流程
2025-08-04 18:07:01 线程3：[信息] 开始启动注册流程
2025-08-04 18:07:01 线程1：[信息] 开始启动浏览器: 位置(0, 0), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36
2025-08-04 18:07:01 线程2：[信息] 开始启动浏览器: 位置(0, 219), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36
2025-08-04 18:07:01 线程2：[信息] 启动无痕Chrome浏览器...
2025-08-04 18:07:01 线程1：[信息] 启动无痕Chrome浏览器...
2025-08-04 18:07:01 线程3：[信息] 开始启动浏览器: 位置(0, 438), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0
2025-08-04 18:07:01 线程3：[信息] 启动无痕Chrome浏览器...
2025-08-04 18:07:01 线程3：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-04 18:07:01 线程2：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-04 18:07:01 线程1：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-04 18:07:01 [信息] 多线程管理窗口已初始化
2025-08-04 18:07:01 [信息] UniformGrid列数已更新为: 1
2025-08-04 18:07:01 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 0, 列数: 1
2025-08-04 18:07:01 [信息] 多线程管理窗口已打开
2025-08-04 18:07:01 [信息] 多线程注册启动成功，共3个线程
2025-08-04 18:07:04 [信息] UniformGrid列数已更新为: 1
2025-08-04 18:07:04 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 1, 列数: 1
2025-08-04 18:07:04 线程1：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-04 18:07:04 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-04 18:07:04 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36 Edg/119.0.0.0
2025-08-04 18:07:04 线程1：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-04 18:07:05 [信息] UniformGrid列数已更新为: 1
2025-08-04 18:07:05 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-08-04 18:07:05 线程2：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-04 18:07:05 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-04 18:07:05 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0) Gecko/20100101 Firefox/119.0
2025-08-04 18:07:05 线程2：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-04 18:07:05 [信息] UniformGrid列数已更新为: 2
2025-08-04 18:07:05 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 3, 列数: 2
2025-08-04 18:07:05 线程3：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-04 18:07:05 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-04 18:07:05 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36 Edg/119.0.0.0
2025-08-04 18:07:05 线程3：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-04 18:07:07 线程3：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-04 18:07:07 线程1：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-04 18:07:07 线程2：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-04 18:07:09 线程3：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-04 18:07:09 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-04 18:07:09 线程3：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-04 18:07:09 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 18:07:09 线程3：[信息] [信息] 多线程模式使用指纹地理位置: -36.8201, -73.0444 (进度: 0%)
2025-08-04 18:07:09 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-36.8201, 经度=-73.0444
2025-08-04 18:07:09 线程3：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_003, CPU: 16核 (进度: 0%)
2025-08-04 18:07:09 [信息] 浏览器指纹注入: Canvas=canvas_fp_003, WebGL=ANGLE (NVIDIA GeForce RTX 3080 Direct3D11 vs_5_0 ps_5_0), CPU=16核, RAM=14 GB
2025-08-04 18:07:09 线程3：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-04 18:07:10 线程2：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-04 18:07:10 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-04 18:07:10 线程2：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-04 18:07:10 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 18:07:10 线程2：[信息] [信息] 多线程模式使用指纹地理位置: -29.9027, -71.2519 (进度: 0%)
2025-08-04 18:07:10 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-29.9027, 经度=-71.2519
2025-08-04 18:07:10 线程1：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-04 18:07:10 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-04 18:07:10 线程1：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-04 18:07:10 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 18:07:10 线程1：[信息] [信息] 多线程模式使用指纹地理位置: -41.4693, -72.9424 (进度: 0%)
2025-08-04 18:07:10 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-41.4693, 经度=-72.9424
2025-08-04 18:07:10 线程2：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_003, CPU: 6核 (进度: 0%)
2025-08-04 18:07:10 [信息] 浏览器指纹注入: Canvas=canvas_fp_003, WebGL=ANGLE (NVIDIA GeForce RTX 3080 Direct3D11 vs_5_0 ps_5_0), CPU=6核, RAM=16 GB
2025-08-04 18:07:10 线程1：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_012, CPU: 8核 (进度: 0%)
2025-08-04 18:07:10 [信息] 浏览器指纹注入: Canvas=canvas_fp_012, WebGL=ANGLE (AMD Radeon RX 6600 Direct3D11 vs_5_0 ps_5_0), CPU=8核, RAM=32 GB
2025-08-04 18:07:11 线程2：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-04 18:07:11 线程1：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-04 18:07:11 线程3：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 16
   • 设备内存: 14 GB
   • 平台信息: Win32
   • Do Not Track: 1

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 3080 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (AMD Technologies)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 0426959D
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_007
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-I7J8K9L
   • MAC地址: 56-78-9A-BC-DE-F0
   • 屏幕分辨率: 1758x1142
   • 可用区域: 1758x1102

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: E5F6A7B8
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: wimax
   • 电池API支持: True
   • 电池电量: 0.55
   • 电池充电状态: True
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-04 18:07:11 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 16    • 设备内存: 14 GB    • 平台信息: Win32    • Do Not Track: 1   图形渲染信息:    • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 3080 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (AMD Technologies)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 0426959D    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_007    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-I7J8K9L    • MAC地址: 56-78-9A-BC-DE-F0    • 屏幕分辨率: 1758x1142    • 可用区域: 1758x1102   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: E5F6A7B8    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: wimax    • 电池API支持: True    • 电池电量: 0.55    • 电池充电状态: True    • 性能API支持: True  ================================
2025-08-04 18:07:11 线程3：[信息] [信息] 已设置浏览器标题: 线程3 - AWS注册工具 (进度: 5%)
2025-08-04 18:07:11 线程3：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-04 18:07:11 线程3：[信息] 浏览器启动成功
2025-08-04 18:07:11 线程3：[信息] 获取下一个数据: <EMAIL>
2025-08-04 18:07:11 线程3：[信息] 开始处理账户: <EMAIL>
2025-08-04 18:07:11 线程3：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-04 18:07:11 线程3：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-04 18:07:11 线程3：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-04 18:07:11 线程3：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-04 18:07:11 线程3：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-04 18:07:12 线程3：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-04 18:07:12 线程3：[信息] [信息] 已重新设置浏览器标题: 线程3 - AWS注册工具 (进度: 98%)
2025-08-04 18:07:12 线程3：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-04 18:07:12 线程3：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-04 18:07:12 线程3：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-04 18:07:12 线程3：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-04 18:07:12 线程1：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 8
   • 设备内存: 32 GB
   • 平台信息: Win32
   • Do Not Track: undefined

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (AMD Radeon RX 6600 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (AMD Inc.)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 1C2D3E4F
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_005
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-M0N1O2P
   • MAC地址: E4-42-A6-5D-13-98
   • 屏幕分辨率: 1886x1158
   • 可用区域: 1886x1118

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: E9F0A1B2
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: 4g
   • 电池API支持: True
   • 电池电量: 0.52
   • 电池充电状态: False
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-04 18:07:12 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 8    • 设备内存: 32 GB    • 平台信息: Win32    • Do Not Track: undefined   图形渲染信息:    • WebGL渲染器: ANGLE (AMD Radeon RX 6600 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (AMD Inc.)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 1C2D3E4F    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_005    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-M0N1O2P    • MAC地址: E4-42-A6-5D-13-98    • 屏幕分辨率: 1886x1158    • 可用区域: 1886x1118   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: E9F0A1B2    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: 4g    • 电池API支持: True    • 电池电量: 0.52    • 电池充电状态: False    • 性能API支持: True  ================================
2025-08-04 18:07:13 线程2：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 6
   • 设备内存: 16 GB
   • 平台信息: Win32
   • Do Not Track: 0

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 3080 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (Intel Technologies)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 0426959D
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_002
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-I7J8K9L
   • MAC地址: 56-78-9A-BC-DE-F0
   • 屏幕分辨率: 2097x990
   • 可用区域: 2097x950

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: E5F6A7B8
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: slow-2g
   • 电池API支持: True
   • 电池电量: 0.34
   • 电池充电状态: False
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-04 18:07:13 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 6    • 设备内存: 16 GB    • 平台信息: Win32    • Do Not Track: 0   图形渲染信息:    • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 3080 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (Intel Technologies)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 0426959D    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_002    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-I7J8K9L    • MAC地址: 56-78-9A-BC-DE-F0    • 屏幕分辨率: 2097x990    • 可用区域: 2097x950   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: E5F6A7B8    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: slow-2g    • 电池API支持: True    • 电池电量: 0.34    • 电池充电状态: False    • 性能API支持: True  ================================
2025-08-04 18:07:14 线程1：[信息] [信息] 已设置浏览器标题: 线程1 - AWS注册工具 (进度: 5%)
2025-08-04 18:07:14 线程1：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-04 18:07:14 线程1：[信息] 浏览器启动成功
2025-08-04 18:07:14 线程1：[信息] 获取下一个数据: <EMAIL>
2025-08-04 18:07:14 线程1：[信息] 开始处理账户: <EMAIL>
2025-08-04 18:07:14 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-04 18:07:14 线程1：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-04 18:07:14 线程1：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-04 18:07:14 线程1：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-04 18:07:14 线程1：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-04 18:07:14 线程2：[信息] [信息] 已设置浏览器标题: 线程2 - AWS注册工具 (进度: 5%)
2025-08-04 18:07:14 线程2：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-04 18:07:14 线程2：[信息] 浏览器启动成功
2025-08-04 18:07:14 线程2：[信息] 获取下一个数据: <EMAIL>
2025-08-04 18:07:14 线程2：[信息] 开始处理账户: <EMAIL>
2025-08-04 18:07:14 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-04 18:07:14 线程2：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-04 18:07:14 线程2：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-04 18:07:14 线程2：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-04 18:07:14 线程2：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-04 18:07:14 线程1：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-04 18:07:14 线程1：[信息] [信息] 已重新设置浏览器标题: 线程1 - AWS注册工具 (进度: 98%)
2025-08-04 18:07:14 线程1：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-04 18:07:14 线程1：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-04 18:07:14 线程1：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-04 18:07:14 线程1：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-04 18:07:15 线程2：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-04 18:07:15 线程2：[信息] [信息] 已重新设置浏览器标题: 线程2 - AWS注册工具 (进度: 98%)
2025-08-04 18:07:15 线程2：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-04 18:07:15 线程2：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-04 18:07:15 线程2：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0... (进度: 98%)
2025-08-04 18:07:15 线程2：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-04 18:07:42 线程3：[信息] [信息] 注册失败: Timeout 30000ms exceeded.
Call log:
  - navigating to "https://portal.aws.amazon.com/gp/aws/developer/registration/index.html?refid=em_127222&p=free&c=hp&z=1", waiting until "load" (进度: 98%)
2025-08-04 18:07:42 线程3：[信息] [信息] 第一页相关失败，数据保持不动 (进度: 98%)
2025-08-04 18:07:42 [信息] 第一页相关失败，数据保持不动
2025-08-04 18:07:42 线程3：[错误] 账户注册启动失败: <EMAIL>
2025-08-04 18:07:42 线程3：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-04 18:07:44 线程1：[信息] [信息] 注册失败: Timeout 30000ms exceeded.
Call log:
  - navigating to "https://portal.aws.amazon.com/gp/aws/developer/registration/index.html?refid=em_127222&p=free&c=hp&z=1", waiting until "load" (进度: 98%)
2025-08-04 18:07:44 线程1：[信息] [信息] 第一页相关失败，数据保持不动 (进度: 98%)
2025-08-04 18:07:44 [信息] 第一页相关失败，数据保持不动
2025-08-04 18:07:44 线程1：[错误] 账户注册启动失败: <EMAIL>
2025-08-04 18:07:44 线程1：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-04 18:07:45 线程2：[信息] [信息] 注册失败: Timeout 30000ms exceeded.
Call log:
  - navigating to "https://portal.aws.amazon.com/gp/aws/developer/registration/index.html?refid=em_127222&p=free&c=hp&z=1", waiting until "load" (进度: 98%)
2025-08-04 18:07:45 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 18:07:45 [信息] 多线程状态已重置
2025-08-04 18:07:45 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 18:07:45 [信息] 多线程状态已重置
2025-08-04 18:07:45 线程2：[信息] [信息] 第一页相关失败，数据保持不动 (进度: 98%)
2025-08-04 18:07:45 [信息] 第一页相关失败，数据保持不动
2025-08-04 18:07:45 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 18:07:45 [信息] 多线程状态已重置
2025-08-04 18:07:45 线程2：[错误] 账户注册启动失败: <EMAIL>
2025-08-04 18:07:45 线程2：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-04 18:07:54 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 18:07:54 [信息] 多线程状态已重置
2025-08-04 18:07:54 线程1：[信息] [信息]  继续注册被调用，当前状态: Error，当前步骤: 1 (进度: 0%)
2025-08-04 18:07:54 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 18:07:54 [信息] 多线程状态已重置
2025-08-04 18:07:54 线程1：[信息] [信息]  进行智能页面检测... (进度: 0%)
2025-08-04 18:07:54 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 18:07:54 [信息] 多线程状态已重置
2025-08-04 18:07:54 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 0%)
2025-08-04 18:07:54 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 18:07:54 [信息] 多线程状态已重置
2025-08-04 18:07:54 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 0%)
2025-08-04 18:07:54 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 18:07:54 [信息] 多线程状态已重置
2025-08-04 18:07:54 线程2：[信息] [信息]  继续注册被调用，当前状态: Error，当前步骤: 1 (进度: 0%)
2025-08-04 18:07:54 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 18:07:54 [信息] 多线程状态已重置
2025-08-04 18:07:54 线程2：[信息] [信息]  进行智能页面检测... (进度: 0%)
2025-08-04 18:07:54 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 18:07:54 [信息] 多线程状态已重置
2025-08-04 18:07:54 线程2：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 0%)
2025-08-04 18:07:54 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 18:07:54 [信息] 多线程状态已重置
2025-08-04 18:07:54 线程2：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 0%)
2025-08-04 18:07:54 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 18:07:54 [信息] 多线程状态已重置
2025-08-04 18:07:54 线程3：[信息] [信息]  继续注册被调用，当前状态: Error，当前步骤: 1 (进度: 0%)
2025-08-04 18:07:54 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 18:07:54 [信息] 多线程状态已重置
2025-08-04 18:07:54 线程3：[信息] [信息]  进行智能页面检测... (进度: 0%)
2025-08-04 18:07:54 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 18:07:54 [信息] 多线程状态已重置
2025-08-04 18:07:54 线程3：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 0%)
2025-08-04 18:07:54 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 18:07:54 [信息] 多线程状态已重置
2025-08-04 18:07:54 线程3：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 0%)
2025-08-04 18:07:54 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 18:07:54 [信息] 多线程状态已重置
2025-08-04 18:07:54 线程1：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 0%)
2025-08-04 18:07:54 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 18:07:54 [信息] 多线程状态已重置
2025-08-04 18:07:54 线程1：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-04 18:07:54 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 18:07:54 [信息] 多线程状态已重置
2025-08-04 18:07:54 线程1：[信息] [信息]  智能检测到当前在第1页 (进度: 100%)
2025-08-04 18:07:54 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 18:07:54 [信息] 多线程状态已重置
2025-08-04 18:07:54 线程1：[信息] [信息] 检测当前页面状态... (进度: 100%)
2025-08-04 18:07:54 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-04 18:07:54 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-04 18:07:57 线程1：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 100%)
2025-08-04 18:07:57 线程1：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-04 18:07:57 线程1：[信息] [信息] 智能检测到当前在第1页，继续执行... (进度: 100%)
2025-08-04 18:07:57 线程1：[信息] [信息] 🔍 等待第一页加载完成... (进度: 100%)
2025-08-04 18:07:59 线程3：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 0%)
2025-08-04 18:07:59 线程3：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-04 18:07:59 线程3：[信息] [信息]  智能检测到当前在第1页 (进度: 100%)
2025-08-04 18:07:59 线程3：[信息] [信息] 检测当前页面状态... (进度: 100%)
2025-08-04 18:07:59 线程3：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-04 18:07:59 线程3：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-04 18:07:59 线程2：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 0%)
2025-08-04 18:07:59 线程2：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-04 18:07:59 线程2：[信息] [信息]  智能检测到当前在第1页 (进度: 100%)
2025-08-04 18:07:59 线程2：[信息] [信息] 检测当前页面状态... (进度: 100%)
2025-08-04 18:07:59 线程2：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-04 18:07:59 线程2：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-04 18:07:59 线程1：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 100%)
2025-08-04 18:07:59 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-04 18:07:59 线程3：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 100%)
2025-08-04 18:07:59 线程3：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-04 18:07:59 线程3：[信息] [信息] 智能检测到当前在第1页，继续执行... (进度: 100%)
2025-08-04 18:07:59 线程3：[信息] [信息] 🔍 等待第一页加载完成... (进度: 100%)
2025-08-04 18:07:59 线程2：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 100%)
2025-08-04 18:07:59 线程2：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-04 18:07:59 线程2：[信息] [信息] 智能检测到当前在第1页，继续执行... (进度: 100%)
2025-08-04 18:07:59 线程2：[信息] [信息] 🔍 等待第一页加载完成... (进度: 100%)
2025-08-04 18:07:59 线程3：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 100%)
2025-08-04 18:07:59 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-04 18:07:59 线程2：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 100%)
2025-08-04 18:07:59 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-04 18:07:59 线程3：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 100%)
2025-08-04 18:07:59 线程1：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 100%)
2025-08-04 18:07:59 线程2：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 100%)
2025-08-04 18:08:02 线程1：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 100%)
2025-08-04 18:08:02 线程3：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 100%)
2025-08-04 18:08:02 线程1：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-04 18:08:02 线程1：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-04 18:08:02 线程1：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-04 18:08:02 线程3：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-04 18:08:02 线程3：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-04 18:08:02 线程3：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-04 18:08:02 线程1：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 18:08:02 线程1：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-04 18:08:02 线程2：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 100%)
2025-08-04 18:08:02 线程3：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 18:08:02 线程3：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-04 18:08:02 线程2：[信息] [信息] ⚠️ 检测到错误信息，开始重试机制... (进度: 100%)
2025-08-04 18:08:02 [信息] 检测到错误信息，开始重试机制
2025-08-04 18:08:02 线程2：[信息] [信息] 🔄 第1次重试点击验证邮箱按钮... (进度: 100%)
2025-08-04 18:08:02 [信息] 第1次重试点击验证邮箱按钮
2025-08-04 18:08:04 线程1：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-04 18:08:04 线程3：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-04 18:08:04 线程1：[信息] [信息] 🔍 第2次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 18:08:04 线程1：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 未发现验证码 (进度: 100%)
2025-08-04 18:08:04 线程1：[信息] [信息] ✅ 第一页未检测到图形验证码，继续流程 (进度: 100%)
2025-08-04 18:08:04 线程1：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-04 18:08:04 线程1：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-04 18:08:04 线程3：[信息] [信息] 🔍 第2次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 18:08:04 线程3：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 未发现验证码 (进度: 100%)
2025-08-04 18:08:04 线程3：[信息] [信息] ✅ 第一页未检测到图形验证码，继续流程 (进度: 100%)
2025-08-04 18:08:04 线程3：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-04 18:08:04 线程3：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-04 18:08:04 线程1：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-04 18:08:04 线程1：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-04 18:08:04 线程1：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-04 18:08:04 线程1：[信息] 已继续
2025-08-04 18:08:04 [信息] 线程1已继续
2025-08-04 18:08:04 [信息] [线程1] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-04 18:08:04 [信息] [线程1] 等待2秒后开始第一次触发...
2025-08-04 18:08:05 线程3：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-04 18:08:05 线程3：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-04 18:08:05 线程3：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-04 18:08:05 [信息] [线程3] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-04 18:08:05 线程3：[信息] 已继续
2025-08-04 18:08:05 [信息] [线程3] 等待2秒后开始第一次触发...
2025-08-04 18:08:05 [信息] 线程3已继续
2025-08-04 18:08:05 线程2：[信息] [信息] ✅ 第1次重试：已点击验证邮箱按钮 (进度: 100%)
2025-08-04 18:08:05 [信息] 第1次重试：已点击验证邮箱按钮
2025-08-04 18:08:06 [信息] [线程1] 第1次触发邮箱验证码获取...（最多20次）
2025-08-04 18:08:07 [信息] [线程3] 第1次触发邮箱验证码获取...（最多20次）
2025-08-04 18:08:07 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 18:08:06
2025-08-04 18:08:07 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-04 18:08:07
2025-08-04 18:08:07 线程2：[信息] [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']" (进度: 100%)
2025-08-04 18:08:07 [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']"
2025-08-04 18:08:07 线程2：[信息] [信息] ✅ 第1次重试成功：已到达第二页，继续正常流程 (进度: 100%)
2025-08-04 18:08:07 [信息] 第1次重试成功：已到达第二页
2025-08-04 18:08:07 线程2：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-04 18:08:07 线程2：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-04 18:08:07 线程2：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 18:08:07 线程2：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-04 18:08:09 线程2：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-04 18:08:09 线程2：[信息] [信息] 🔍 第2次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 18:08:09 线程2：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 未发现验证码 (进度: 100%)
2025-08-04 18:08:09 线程2：[信息] [信息] ✅ 第一页未检测到图形验证码，继续流程 (进度: 100%)
2025-08-04 18:08:09 线程2：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-04 18:08:09 线程2：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-04 18:08:09 线程2：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-04 18:08:09 线程2：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-04 18:08:09 线程2：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-04 18:08:09 [信息] [线程2] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-04 18:08:09 线程2：[信息] 已继续
2025-08-04 18:08:09 [信息] [线程2] 等待2秒后开始第一次触发...
2025-08-04 18:08:09 [信息] 线程2已继续
2025-08-04 18:08:09 [信息] 继续了 3 个可继续的线程
2025-08-04 18:08:10 [信息] [线程1] 第2次触发邮箱验证码获取...（最多20次）
2025-08-04 18:08:10 [信息] [线程3] 第2次触发邮箱验证码获取...（最多20次）
2025-08-04 18:08:10 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 18:08:10
2025-08-04 18:08:10 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-04 18:08:10
2025-08-04 18:08:11 [信息] [线程2] 第1次触发邮箱验证码获取...（最多20次）
2025-08-04 18:08:11 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-04 18:08:11
2025-08-04 18:08:13 [信息] [线程3] 第3次触发邮箱验证码获取...（最多20次）
2025-08-04 18:08:13 [信息] [线程1] 第3次触发邮箱验证码获取...（最多20次）
2025-08-04 18:08:13 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-04 18:08:13
2025-08-04 18:08:13 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 18:08:13
2025-08-04 18:08:14 [信息] [线程2] 第2次触发邮箱验证码获取...（最多20次）
2025-08-04 18:08:14 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-04 18:08:14
2025-08-04 18:08:16 [信息] [线程3] 第4次触发邮箱验证码获取...（最多20次）
2025-08-04 18:08:16 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-04 18:08:16
2025-08-04 18:08:16 [信息] [线程1] 第4次触发邮箱验证码获取...（最多20次）
2025-08-04 18:08:16 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 18:08:16
2025-08-04 18:08:16 [信息] [线程1] 邮箱验证码获取成功: 048680，立即停止重复请求
2025-08-04 18:08:16 [信息] [线程1] 已清理请求文件，停止重复触发
2025-08-04 18:08:16 [信息] [线程1] 已清理响应文件
2025-08-04 18:08:16 线程1：[信息] [信息] 验证码获取成功: 048680，正在自动填入... (进度: 100%)
2025-08-04 18:08:16 线程1：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 100%)
2025-08-04 18:08:18 [信息] [线程2] 第3次触发邮箱验证码获取...（最多20次）
2025-08-04 18:08:18 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-04 18:08:18
2025-08-04 18:08:18 线程1：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 100%)
2025-08-04 18:08:18 线程1：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 100%)
2025-08-04 18:08:18 [信息] 线程1完成第二页事件已处理
2025-08-04 18:08:18 [信息] 线程1完成第二页，开始批量获取手机号码...
2025-08-04 18:08:18 线程1：[信息] [信息] 线程1第二页验证完成，已通知管理器 (进度: 100%)
2025-08-04 18:08:18 [信息] 开始批量获取3个手机号码，服务商: Durian
2025-08-04 18:08:18 [信息] [手机API] 批量获取3个手机号码，URL: https://api.durianrcs.com/out/ext_api/getMobile?name=oneone&ApiKey=NnlaR01xQm9hMlAwRnJDSVB2SG1kQT09&cuy=mx&pid=0209&num=3&noblack=0&serial=2&secret_key=null&vip=null
2025-08-04 18:08:19 [信息] [线程3] 第5次触发邮箱验证码获取...（最多20次）
2025-08-04 18:08:19 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-04 18:08:19
2025-08-04 18:08:20 [信息] [线程3] 邮箱验证码获取成功: 619596，立即停止重复请求
2025-08-04 18:08:20 [信息] [线程3] 已清理请求文件，停止重复触发
2025-08-04 18:08:20 [信息] [线程3] 已清理响应文件
2025-08-04 18:08:20 线程3：[信息] [信息] 验证码获取成功: 619596，正在自动填入... (进度: 100%)
2025-08-04 18:08:20 线程3：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 100%)
2025-08-04 18:08:21 线程3：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 100%)
2025-08-04 18:08:21 线程3：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 100%)
2025-08-04 18:08:21 [信息] 线程3完成第二页事件已处理
2025-08-04 18:08:21 [信息] 线程3完成第二页，手机号码已获取，无需重复获取
2025-08-04 18:08:21 线程3：[信息] [信息] 线程3第二页验证完成，已通知管理器 (进度: 100%)
2025-08-04 18:08:21 [信息] [线程2] 邮箱验证码获取成功: 248159，立即停止重复请求
2025-08-04 18:08:21 [信息] [线程2] 已清理请求文件，停止重复触发
2025-08-04 18:08:21 [信息] [线程2] 已清理响应文件
2025-08-04 18:08:21 线程2：[信息] [信息] 验证码获取成功: 248159，正在自动填入... (进度: 100%)
2025-08-04 18:08:21 线程2：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 100%)
2025-08-04 18:08:21 线程2：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 100%)
2025-08-04 18:08:21 线程2：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 100%)
2025-08-04 18:08:21 [信息] 线程2完成第二页事件已处理
2025-08-04 18:08:21 [信息] 线程2完成第二页，手机号码已获取，无需重复获取
2025-08-04 18:08:21 线程2：[信息] [信息] 线程2第二页验证完成，已通知管理器 (进度: 100%)
2025-08-04 18:08:21 线程1：[信息] [信息] 等待密码设置页面加载... (进度: 100%)
2025-08-04 18:08:21 线程1：[信息] [信息] 开始填写密码信息... (进度: 100%)
2025-08-04 18:08:22 线程1：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 100%)
2025-08-04 18:08:22 线程1：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 100%)
2025-08-04 18:08:22 线程1：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 100%)
2025-08-04 18:08:23 线程1：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 100%)
2025-08-04 18:08:24 线程3：[信息] [信息] 等待密码设置页面加载... (进度: 100%)
2025-08-04 18:08:24 线程3：[信息] [信息] 开始填写密码信息... (进度: 100%)
2025-08-04 18:08:24 线程3：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 100%)
2025-08-04 18:08:24 线程3：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 100%)
2025-08-04 18:08:24 线程3：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 100%)
2025-08-04 18:08:24 线程2：[信息] [信息] 等待密码设置页面加载... (进度: 100%)
2025-08-04 18:08:24 线程2：[信息] [信息] 开始填写密码信息... (进度: 100%)
2025-08-04 18:08:24 线程2：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 100%)
2025-08-04 18:08:24 线程2：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 100%)
2025-08-04 18:08:24 线程2：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 100%)
2025-08-04 18:08:25 线程3：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 100%)
2025-08-04 18:08:25 [信息] [手机API] 批量获取响应内容: {"code":200,"msg":"Success","data":["+529656978287","+523316975124","+523231350170"]}
2025-08-04 18:08:25 [信息] [手机API] 检测到数组格式，元素数量: 3
2025-08-04 18:08:25 [信息] [手机API] 批量获取成功，获得3个手机号码
2025-08-04 18:08:25 [信息] 线程1分配榴莲手机号码: +529656978287
2025-08-04 18:08:25 [信息] 线程2分配榴莲手机号码: +523316975124
2025-08-04 18:08:25 [信息] 线程3分配榴莲手机号码: +523231350170
2025-08-04 18:08:25 [信息] 榴莲API批量获取手机号码成功，已分配给3个线程
2025-08-04 18:08:25 [信息] 批量获取3个手机号码成功
2025-08-04 18:08:26 线程2：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 100%)
2025-08-04 18:08:26 线程1：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 100%)
2025-08-04 18:08:26 线程1：[信息] [信息] 等待账户类型确认页面加载... (进度: 100%)
2025-08-04 18:08:26 线程1：[信息] [信息] 开始处理账户类型确认... (进度: 100%)
2025-08-04 18:08:28 线程3：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 100%)
2025-08-04 18:08:28 线程3：[信息] [信息] 等待账户类型确认页面加载... (进度: 100%)
2025-08-04 18:08:28 线程3：[信息] [信息] 开始处理账户类型确认... (进度: 100%)
2025-08-04 18:08:29 线程2：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 100%)
2025-08-04 18:08:29 线程2：[信息] [信息] 等待账户类型确认页面加载... (进度: 100%)
2025-08-04 18:08:29 线程2：[信息] [信息] 开始处理账户类型确认... (进度: 100%)
2025-08-04 18:08:35 线程3：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 100%)
2025-08-04 18:08:35 线程3：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 100%)
2025-08-04 18:08:37 线程2：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 100%)
2025-08-04 18:08:37 线程2：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 100%)
2025-08-04 18:08:45 线程1：[信息] [信息] 第3.5页执行失败: Timeout 15000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Button, new() { Name = "Choose paid plan" }) to be visible (进度: 100%)
2025-08-04 18:08:56 线程1：[信息] [信息]  继续注册被调用，当前状态: Paused，当前步骤: 3 (进度: 100%)
2025-08-04 18:08:56 线程1：[信息] [信息]  进行智能页面检测... (进度: 100%)
2025-08-04 18:08:56 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-04 18:08:56 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-04 18:09:00 线程1：[信息] [信息] 🎯 找到匹配按钮: 'Choose paid plan' → 第3页 (进度: 100%)
2025-08-04 18:09:00 线程1：[信息] [信息] 检测到第3.5页（账户类型确认页面），直接点击Choose paid plan按钮... (进度: 100%)
2025-08-04 18:09:00 线程1：[信息] [信息] 账户类型确认完成，进入第4页（联系信息页面）... (进度: 100%)
2025-08-04 18:09:02 线程2：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 100%)
2025-08-04 18:09:02 [信息] 线程2获取已分配的榴莲手机号码: +523316975124
2025-08-04 18:09:02 线程2：[信息] [信息] 多线程模式：使用已分配的手机号码 +523316975124 (进度: 100%)
2025-08-04 18:09:03 线程2：[信息] [信息] 数据国家代码为CL，需要选择Chile (进度: 100%)
2025-08-04 18:09:03 线程2：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 100%)
2025-08-04 18:09:04 线程3：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 100%)
2025-08-04 18:09:04 [信息] 线程3获取已分配的榴莲手机号码: +523231350170
2025-08-04 18:09:04 线程3：[信息] [信息] 多线程模式：使用已分配的手机号码 +523231350170 (进度: 100%)
2025-08-04 18:09:04 线程3：[信息] [信息] 数据国家代码为CL，需要选择Chile (进度: 100%)
2025-08-04 18:09:04 线程3：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 100%)
2025-08-04 18:09:05 线程2：[信息] [信息] 已选择国家: Chile (进度: 100%)
2025-08-04 18:09:05 线程2：[信息] [信息] 已成功选择国家: Chile (进度: 100%)
2025-08-04 18:09:05 线程2：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 100%)
2025-08-04 18:09:05 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-04 18:09:06 线程3：[信息] [信息] 已选择国家: Chile (进度: 100%)
2025-08-04 18:09:06 线程3：[信息] [信息] 已成功选择国家: Chile (进度: 100%)
2025-08-04 18:09:06 线程3：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 100%)
2025-08-04 18:09:06 线程3：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-04 18:09:09 线程2：[信息] [信息] 已选择国家代码 +52 (进度: 100%)
2025-08-04 18:09:10 线程2：[信息] [信息] 等待后台获取的手机号码结果... (进度: 100%)
2025-08-04 18:09:10 线程3：[信息] [信息] 已选择国家代码 +52 (进度: 100%)
2025-08-04 18:09:10 线程2：[信息] [信息] 已自动获取并填入手机号码: +523316975124 (进度: 100%)
2025-08-04 18:09:10 线程3：[信息] [信息] 等待后台获取的手机号码结果... (进度: 100%)
2025-08-04 18:09:10 线程3：[信息] [信息] 已自动获取并填入手机号码: +523231350170 (进度: 100%)
2025-08-04 18:09:11 线程2：[信息] [信息] 使用已获取的手机号码: +523316975124（保存本地号码: +523316975124） (进度: 100%)
2025-08-04 18:09:11 线程2：[信息] [信息] 联系信息完成，等待页面加载... (进度: 100%)
2025-08-04 18:09:11 线程3：[信息] [信息] 使用已获取的手机号码: +523231350170（保存本地号码: +523231350170） (进度: 100%)
2025-08-04 18:09:12 线程3：[信息] [信息] 联系信息完成，等待页面加载... (进度: 100%)
2025-08-04 18:09:14 线程2：[信息] [信息] 进入付款信息页面... (进度: 100%)
2025-08-04 18:09:15 线程3：[信息] [信息] 进入付款信息页面... (进度: 100%)
2025-08-04 18:09:15 线程2：[信息] [信息] 正在选择月份: August (进度: 100%)
2025-08-04 18:09:15 线程2：[信息] [信息] 已选择月份（标准选项）: August (进度: 100%)
2025-08-04 18:09:16 线程3：[信息] [信息] 正在选择月份: July (进度: 100%)
2025-08-04 18:09:16 线程3：[信息] [信息] 已选择月份（标准选项）: July (进度: 100%)
2025-08-04 18:09:16 线程2：[信息] [信息] 正在选择年份: 2029 (进度: 100%)
2025-08-04 18:09:16 线程2：[信息] [信息] 已选择年份（标准选项）: 2029 (进度: 100%)
2025-08-04 18:09:17 线程3：[信息] [信息] 正在选择年份: 2029 (进度: 100%)
2025-08-04 18:09:17 线程3：[信息] [信息] 已选择年份（标准选项）: 2029 (进度: 100%)
2025-08-04 18:09:17 线程2：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 100%)
2025-08-04 18:09:17 线程2：[信息] [信息] 开始填写验证码验证页面... (进度: 100%)
2025-08-04 18:09:17 线程2：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 100%)
2025-08-04 18:09:18 线程3：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 100%)
2025-08-04 18:09:18 线程3：[信息] [信息] 开始填写验证码验证页面... (进度: 100%)
2025-08-04 18:09:18 线程3：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 100%)
2025-08-04 18:09:21 线程1：[信息] [信息]  继续注册被调用，当前状态: Paused，当前步骤: 3 (进度: 100%)
2025-08-04 18:09:21 线程1：[信息] [信息]  进行智能页面检测... (进度: 100%)
2025-08-04 18:09:21 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-04 18:09:21 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-04 18:09:21 线程1：[信息] [信息] 🎯 找到匹配按钮: 'Agree and Continue (step 2 of 5)' → 第4页 (进度: 100%)
2025-08-04 18:09:21 线程1：[信息] [信息] ✅ 直接确认为第4页 (进度: 100%)
2025-08-04 18:09:21 线程1：[信息] [信息]  智能检测到当前在第4页 (进度: 100%)
2025-08-04 18:09:21 线程1：[信息] [信息] 智能检测到当前在第4页，开始智能处理... (进度: 100%)
2025-08-04 18:09:23 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-04 18:09:24 线程1：[信息] [信息] 第3.5页完成，继续执行第4页操作 (进度: 100%)
2025-08-04 18:09:24 [信息] 线程1获取已分配的榴莲手机号码: +529656978287
2025-08-04 18:09:24 线程1：[信息] [信息] 多线程模式：使用已分配的手机号码 +529656978287 (进度: 100%)
2025-08-04 18:09:24 [信息] 线程1获取已分配的榴莲手机号码: +529656978287
2025-08-04 18:09:24 线程1：[信息] [信息] 多线程模式：使用已分配的手机号码 +529656978287 (进度: 100%)
2025-08-04 18:09:25 线程2：[信息] [信息] 已选择国家代码: +52 (进度: 100%)
2025-08-04 18:09:25 线程1：[信息] [信息] 数据国家代码为CL，需要选择Chile (进度: 100%)
2025-08-04 18:09:25 线程1：[信息] [信息] 数据国家代码为CL，需要选择Chile (进度: 100%)
2025-08-04 18:09:25 线程2：[信息] [信息] 已清空并重新填写手机号码: +523316975124 (进度: 100%)
2025-08-04 18:09:25 线程2：[信息] [信息] 已点击发送验证码按钮 (进度: 100%)
2025-08-04 18:09:25 线程1：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 100%)
2025-08-04 18:09:25 线程1：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 100%)
2025-08-04 18:09:26 线程3：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-04 18:09:26 线程1：[信息] [信息] 选择国家选项时出错: Unknown engine "text*" while parsing selector text*='Chile' (进度: 100%)
2025-08-04 18:09:26 线程1：[信息] [信息] 自动选择国家失败，请手动选择Chile (进度: 100%)
2025-08-04 18:09:26 线程1：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 100%)
2025-08-04 18:09:26 线程1：[信息] [信息] 选择国家选项时出错: Unknown engine "text*" while parsing selector text*='Chile' (进度: 100%)
2025-08-04 18:09:26 线程1：[信息] [信息] 自动选择国家失败，请手动选择Chile (进度: 100%)
2025-08-04 18:09:26 线程1：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 100%)
2025-08-04 18:09:27 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-04 18:09:27 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-04 18:09:27 线程2：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 100%)
2025-08-04 18:09:27 线程2：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-08-04 18:09:27 线程2：[信息] [信息] 手机号码自动模式 + 图形验证码自动模式：开始自动处理... (进度: 100%)
2025-08-04 18:09:27 线程2：[信息] [信息] 自动模式：开始处理图形验证码... (进度: 100%)
2025-08-04 18:09:27 线程2：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-04 18:09:27 线程3：[信息] [信息] 已选择国家代码: +52 (进度: 100%)
2025-08-04 18:09:27 线程3：[信息] [信息] 已清空并重新填写手机号码: +523231350170 (进度: 100%)
2025-08-04 18:09:28 线程3：[信息] [信息] 已点击发送验证码按钮 (进度: 100%)
2025-08-04 18:09:28 线程1：[信息] [信息] 自动选择国家代码失败，请手动选择+52 (进度: 100%)
2025-08-04 18:09:28 线程1：[信息] [信息] 自动选择国家代码失败，请手动选择+52 (进度: 100%)
2025-08-04 18:09:28 线程1：[信息] [信息] 继续注册失败: 第四页执行失败: Clicking the checkbox did not change its state
Call log:
  - waiting for GetByRole(AriaRole.Checkbox, new() { Name = "AWS Customer Agreement checkbox" })
  -   locator resolved to <input type="checkbox" name="agreement" override-focus=…/>
  - attempting click action
  -   waiting for element to be visible, enabled and stable
  -   element is visible, enabled and stable
  -   scrolling into view if needed
  -   done scrolling
  -   performing click action
  -   click action done
  -   waiting for scheduled navigations to finish
  -   navigations have finished (进度: 100%)
2025-08-04 18:09:28 线程1：[信息] 已继续
2025-08-04 18:09:28 [信息] 线程1已继续
2025-08-04 18:09:30 线程3：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 100%)
2025-08-04 18:09:30 线程3：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-08-04 18:09:30 线程3：[信息] [信息] 手机号码自动模式 + 图形验证码自动模式：开始自动处理... (进度: 100%)
2025-08-04 18:09:30 线程3：[信息] [信息] 自动模式：开始处理图形验证码... (进度: 100%)
2025-08-04 18:09:30 线程3：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-04 18:09:30 线程2：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-04 18:09:30 线程2：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 18:09:33 线程3：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-04 18:09:33 线程3：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 18:09:41 线程2：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 34328 字节 (进度: 100%)
2025-08-04 18:09:41 线程2：[信息] [信息] ✅ 图片验证通过：201x70px，34328字节，复杂度符合要求 (进度: 100%)
2025-08-04 18:09:41 线程2：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 18:09:44 线程2：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"gf6mm2"},"taskId":"24c05790-711b-11f0-948a-9ab6db23b9b7"} (进度: 100%)
2025-08-04 18:09:44 线程2：[信息] [信息] 第六页第1次识别结果: gf6mm2 → 转换为小写: gf6mm2 (进度: 100%)
2025-08-04 18:09:44 线程2：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 18:09:44 线程2：[信息] [信息] 第六页已填入验证码: gf6mm2 (进度: 100%)
2025-08-04 18:09:44 线程2：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 18:09:46 线程3：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 34962 字节 (进度: 100%)
2025-08-04 18:09:46 线程3：[信息] [信息] ✅ 图片验证通过：201x70px，34962字节，复杂度符合要求 (进度: 100%)
2025-08-04 18:09:46 线程3：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 18:09:47 线程2：[信息] [信息] 第1次图形验证码识别成功 (进度: 100%)
2025-08-04 18:09:47 线程2：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-08-04 18:09:50 线程2：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-08-04 18:09:51 线程3：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"32mt4b"},"taskId":"288cb9c2-711b-11f0-8267-9ab6db23b9b7"} (进度: 100%)
2025-08-04 18:09:51 线程3：[信息] [信息] 第六页第1次识别结果: 32mt4b → 转换为小写: 32mt4b (进度: 100%)
2025-08-04 18:09:51 线程3：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 18:09:51 线程3：[信息] [信息] 第六页已填入验证码: 32mt4b (进度: 100%)
2025-08-04 18:09:51 线程3：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 18:09:53 线程2：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-04 18:09:53 线程2：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 18:09:53 线程2：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 18:09:53 线程2：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-08-04 18:09:55 线程3：[信息] [信息] 第1次图形验证码识别成功 (进度: 100%)
2025-08-04 18:09:55 线程3：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-08-04 18:09:58 线程3：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-08-04 18:09:58 线程2：[信息] [信息] 线程2开始独立获取验证码... (进度: 100%)
2025-08-04 18:09:58 线程2：[信息] [信息] 线程2开始获取验证码，最多尝试8次，每次间隔8秒 (进度: 100%)
2025-08-04 18:09:58 线程2：[信息] [信息] 线程2第1次尝试获取验证码...（剩余7次尝试） (进度: 100%)
2025-08-04 18:09:58 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-04 18:10:00 线程2：[信息] [信息] 线程2验证码获取成功: 7956 (进度: 100%)
2025-08-04 18:10:00 [信息] 线程2手机号码已加入释放队列: +523316975124 (原因: 获取验证码成功)
2025-08-04 18:10:00 线程2：[信息] [信息] 线程2验证码获取成功: 7956，立即填入验证码... (进度: 100%)
2025-08-04 18:10:00 线程2：[信息] [信息] 第七页：找到验证码输入框 (Verify code) (进度: 100%)
2025-08-04 18:10:00 线程2：[信息] [信息] 线程2已自动填入手机验证码: 7956 (进度: 100%)
2025-08-04 18:10:01 [信息] 定时检查发现1个待释放手机号码，开始批量释放
2025-08-04 18:10:01 [信息] 开始释放1个手机号码
2025-08-04 18:10:01 [信息] [手机API] 开始批量释放1个手机号码
2025-08-04 18:10:01 [信息] [手机API] 释放手机号码: +523316975124
2025-08-04 18:10:01 线程2：[信息] [信息] 线程2正在自动点击Continue按钮... (进度: 100%)
2025-08-04 18:10:01 线程3：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-04 18:10:01 线程3：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 18:10:01 线程3：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 18:10:01 线程3：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-08-04 18:10:01 [信息] [手机API] 手机号码释放成功: +523316975124
2025-08-04 18:10:01 线程2：[信息] [信息] 线程2手机验证码验证完成，继续执行后续步骤... (进度: 100%)
2025-08-04 18:10:02 [信息] [手机API] 批量释放完成: 成功1个, 失败0个
2025-08-04 18:10:02 [信息] 定时批量释放完成: 批量释放完成: 成功1个, 失败0个
2025-08-04 18:10:04 线程2：[信息] [信息] 验证码验证完成，等待页面加载... (进度: 100%)
2025-08-04 18:10:05 线程2：[信息] [信息] 已点击完成注册按钮，正在处理... (进度: 100%)
2025-08-04 18:10:06 线程2：[信息] [信息] 等待完成注册页面加载... (进度: 100%)
2025-08-04 18:10:06 线程3：[信息] [信息] 线程3开始独立获取验证码... (进度: 100%)
2025-08-04 18:10:06 线程3：[信息] [信息] 线程3开始获取验证码，最多尝试8次，每次间隔8秒 (进度: 100%)
2025-08-04 18:10:06 线程3：[信息] [信息] 线程3第1次尝试获取验证码...（剩余7次尝试） (进度: 100%)
2025-08-04 18:10:06 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-04 18:10:07 线程3：[信息] [信息] 线程3验证码获取成功: 4382 (进度: 100%)
2025-08-04 18:10:07 [信息] 线程3手机号码已加入释放队列: +523231350170 (原因: 获取验证码成功)
2025-08-04 18:10:07 线程3：[信息] [信息] 线程3验证码获取成功: 4382，立即填入验证码... (进度: 100%)
2025-08-04 18:10:07 线程3：[信息] [信息] 第七页：找到验证码输入框 (Verify code) (进度: 100%)
2025-08-04 18:10:07 线程3：[信息] [信息] 线程3已自动填入手机验证码: 4382 (进度: 100%)
2025-08-04 18:10:08 线程3：[信息] [信息] 线程3正在自动点击Continue按钮... (进度: 100%)
2025-08-04 18:10:08 线程3：[信息] [信息] 线程3手机验证码验证完成，继续执行后续步骤... (进度: 100%)
2025-08-04 18:10:09 线程2：[信息] [信息] 完成注册页面加载完成 (进度: 100%)
2025-08-04 18:10:09 线程2：[信息] [信息] 🔍 正在查找'Go to the AWS Management Console'链接... (进度: 100%)
2025-08-04 18:10:10 线程2：[信息] [信息] 自动模式验证完成，API手机号码已加入黑名单: 加入黑名单成功 (进度: 100%)
2025-08-04 18:10:11 线程3：[信息] [信息] 验证码验证完成，等待页面加载... (进度: 100%)
2025-08-04 18:10:13 线程3：[信息] [信息] 自动模式验证完成，API手机号码已加入黑名单: 加入黑名单成功 (进度: 100%)
2025-08-04 18:10:19 线程2：[信息] [信息] 🖱️ 已点击跳转管理控制台链接 (进度: 100%)
2025-08-04 18:10:19 线程2：[信息] [信息] 🔗 通过导航进入IAM安全凭证页面... (进度: 100%)
2025-08-04 18:10:19 线程2：[信息] [信息] ⏳ 等待管理控制台页面加载，智能检测'更多'按钮（超时20秒）... (进度: 100%)
2025-08-04 18:10:31 [信息] 定时检查发现1个待释放手机号码，开始批量释放
2025-08-04 18:10:31 [信息] 开始释放1个手机号码
2025-08-04 18:10:31 [信息] [手机API] 开始批量释放1个手机号码
2025-08-04 18:10:31 [信息] [手机API] 释放手机号码: +523231350170
2025-08-04 18:10:32 [信息] [手机API] 手机号码释放成功: +523231350170
2025-08-04 18:10:32 [信息] [手机API] 批量释放完成: 成功1个, 失败0个
2025-08-04 18:10:32 [信息] 定时批量释放完成: 批量释放完成: 成功1个, 失败0个
2025-08-04 18:10:39 线程2：[信息] [信息] ⚠️ 20秒内未找到'更多'按钮，继续执行后续流程... (进度: 100%)
2025-08-04 18:10:39 [信息] 20秒内未找到'更多'按钮，但继续执行
2025-08-04 18:10:39 线程2：[信息] [信息] 🔍 智能检测更多按钮... (进度: 100%)
2025-08-04 18:10:39 线程2：[信息] [信息] 🔍 第1次检查更多按钮... (进度: 100%)
2025-08-04 18:10:40 线程2：[信息] [信息] ⚠️ 第1次检查未找到更多按钮 (进度: 100%)
2025-08-04 18:10:40 [信息] 第1次检查未找到更多按钮
2025-08-04 18:10:40 线程2：[信息] [信息] ⏳ 等待3秒后重新检查... (进度: 100%)
2025-08-04 18:10:41 线程3：[信息] [信息] 点击完成注册按钮失败: Timeout 30000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Button, new() { Name = "Complete sign up" }) (进度: 100%)
2025-08-04 18:10:41 线程3：[信息] [信息] 等待完成注册页面加载... (进度: 100%)
2025-08-04 18:10:43 线程2：[信息] [信息] 🔍 第2次检查更多按钮... (进度: 100%)
2025-08-04 18:10:43 线程2：[信息] [信息] ✅ 第2次检查成功找到更多按钮 (进度: 100%)
2025-08-04 18:10:43 [信息] 第2次检查成功找到更多按钮
2025-08-04 18:10:43 线程2：[信息] [信息] 🖱️ 正在点击更多按钮... (进度: 100%)
2025-08-04 18:10:43 线程2：[信息] [信息] ✅ 成功点击更多按钮 (进度: 100%)
2025-08-04 18:10:43 [信息] 成功点击更多按钮
2025-08-04 18:10:44 线程3：[信息] [信息] 完成注册页面加载完成 (进度: 100%)
2025-08-04 18:10:44 线程3：[信息] [信息] 🔍 正在查找'Go to the AWS Management Console'链接... (进度: 100%)
2025-08-04 18:10:44 线程2：[信息] [信息] 🖱️ 正在点击账户信息按钮... (进度: 100%)
2025-08-04 18:10:45 线程2：[信息] [信息] ✅ 成功点击账户信息按钮 (进度: 100%)
2025-08-04 18:10:45 [信息] 成功点击账户信息按钮
2025-08-04 18:10:46 线程2：[信息] [信息] 🖱️ 正在点击'安全凭证'菜单项... (进度: 100%)
2025-08-04 18:10:46 线程2：[信息] [信息] 🔍 正在定位'安全凭证'链接... (进度: 100%)
2025-08-04 18:10:46 线程2：[信息] [信息] ✅ 成功定位到'安全凭证'链接 (进度: 100%)
2025-08-04 18:10:46 [信息] 成功定位到'安全凭证'链接
2025-08-04 18:10:49 线程3：[信息] [信息] ❌ 控制台按钮处理失败: Timeout 5000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Link, new() { Name = "Go to the AWS Management Console" }) to be visible (进度: 100%)
2025-08-04 18:10:49 [信息] 控制台按钮处理失败: Timeout 5000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Link, new() { Name = "Go to the AWS Management Console" }) to be visible
2025-08-04 18:10:49 线程3：[信息] [信息] ⚠️ 密钥提取失败，按原本逻辑完成注册 (进度: 100%)
2025-08-04 18:10:49 [信息] 密钥提取失败，按原本逻辑完成注册
2025-08-04 18:10:49 线程3：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：dD8DAE5p ③AWS密码：tIPT8hu9 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息： (进度: 100%)
2025-08-04 18:10:49 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：dD8DAE5p ③AWS密码：tIPT8hu9 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 18:10:49 线程3：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：dD8DAE5p ③AWS密码：tIPT8hu9 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 18:10:49 [信息] 线程3请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：dD8DAE5p ③AWS密码：tIPT8hu9 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 18:10:49 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：dD8DAE5p ③AWS密码：tIPT8hu9 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 18:10:49 [信息] 线程3剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：dD8DAE5p ③AWS密码：tIPT8hu9 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 18:10:49 线程3：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-08-04 18:10:49 线程3：[信息] 账户注册完成(无密钥)，等待后续操作: <EMAIL>
2025-08-04 18:10:49 [信息] 开始处理数据完成事件: <EMAIL>
2025-08-04 18:10:49 [信息] 已将完成数据移动到注册成功区域: <EMAIL>
2025-08-04 18:10:49 [信息] 已完成数据移除: <EMAIL>
2025-08-04 18:10:49 [信息] 数据完成事件处理完毕: <EMAIL>
2025-08-04 18:10:49 线程3：[信息] [信息] ✅ 注册成功（密钥提取失败） (进度: 98%)
2025-08-04 18:10:49 [信息] 注册完成（密钥提取失败）
2025-08-04 18:10:49 线程3：[信息] 最终完成: 注册完成，无法提取密钥: <EMAIL> (类型: WithoutKeys)
2025-08-04 18:10:49 [信息] 线程3数据完成事件已通知主窗口并最终完成: <EMAIL>
2025-08-04 18:11:00 线程2：[信息] [信息] ✅ 成功点击'安全凭证'菜单项 (进度: 100%)
2025-08-04 18:11:00 [信息] 成功点击'安全凭证'链接
2025-08-04 18:11:00 线程2：[信息] [信息] ⏳ 等待IAM安全凭证页面加载（20秒超时，循环检测账单问题和创建密钥按钮）... (进度: 100%)
2025-08-04 18:11:25 线程2：[信息] [信息] ⚠️ 20秒超时未找到创建密钥按钮，尝试通用页面状态检查 (进度: 100%)
2025-08-04 18:11:25 线程2：[信息] [信息] 🔍 检查页面状态... (进度: 100%)
2025-08-04 18:11:25 线程2：[信息] [信息] 当前页面URL: https://us-east-1.console.aws.amazon.com/iam/home?region=ap-southeast-2#/security_credentials (进度: 100%)
2025-08-04 18:11:25 线程2：[信息] [信息] ✅ 页面状态正常，开始密钥提取流程 (进度: 100%)
2025-08-04 18:11:25 线程2：[信息] [信息] 🔍 设置页面缩放为50%... (进度: 100%)
2025-08-04 18:11:26 线程2：[信息] [信息] ✅ 页面缩放设置完成 (进度: 100%)
2025-08-04 18:11:26 [信息] 页面缩放设置为50%完成
2025-08-04 18:11:26 线程2：[信息] [信息] 🔍 正在查找并点击'下一步'按钮关闭向导... (进度: 100%)
2025-08-04 18:11:26 [信息] 开始查找并点击'下一步'按钮关闭向导
2025-08-04 18:11:26 线程2：[信息] [信息] ℹ️ 未检测到向导元素，向导可能已自动关闭 (进度: 100%)
2025-08-04 18:11:26 [信息] 未检测到向导元素，向导可能已自动关闭
2025-08-04 18:11:26 线程2：[信息] [信息] 🔑 第四阶段：开始创建和复制访问密钥 (进度: 100%)
2025-08-04 18:11:26 [信息] 开始创建和复制访问密钥
2025-08-04 18:11:26 线程2：[信息] [信息] 🔑 正在点击'创建访问密钥'按钮... (进度: 100%)
2025-08-04 18:11:26 线程2：[信息] [信息] 🔍 正在定位'创建访问密钥'按钮... (进度: 100%)
2025-08-04 18:11:28 线程1：[信息] [信息] ⚠️ 未找到匹配的页面按钮或链接 (进度: 100%)
2025-08-04 18:11:28 线程1：[信息] [信息] ⚠️ 智能检测失败，启用详细页面分析... (进度: 100%)
2025-08-04 18:11:28 线程1：[信息] [信息] 🔬 执行详细页面分析... (进度: 100%)
2025-08-04 18:11:28 线程1：[信息] [信息] 📄 页面URL: https://portal.aws.amazon.com/billing/signup?type=register&refid=em_127222&p=free&c=hp&z=1&redirect_url=https%3A%2F%2Faws.amazon.com%2Fregistration-confirmation#/account (进度: 100%)
2025-08-04 18:11:28 线程1：[信息] [信息] 📋 页面标题: AWS Console - Signup (进度: 100%)
2025-08-04 18:11:29 线程1：[信息] [信息] 📊 分析结果: 第四页-联系信息(4/4个元素匹配), 第六页-手机验证(1/3个元素匹配) (进度: 100%)
2025-08-04 18:11:29 线程1：[信息] [信息]  智能检测到当前在第6页 (进度: 100%)
2025-08-04 18:11:29 线程1：[信息] [信息] 检测当前页面状态... (进度: 100%)
2025-08-04 18:11:29 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-04 18:11:29 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-04 18:11:29 线程1：[信息] [信息] 🎯 找到匹配按钮: 'Agree and Continue (step 2 of 5)' → 第4页 (进度: 100%)
2025-08-04 18:11:29 线程1：[信息] [信息] ✅ 直接确认为第4页 (进度: 100%)
2025-08-04 18:11:29 线程1：[信息] [信息] 智能检测到当前在第4页，继续执行... (进度: 100%)
2025-08-04 18:11:29 [信息] 线程1获取已分配的榴莲手机号码: +529656978287
2025-08-04 18:11:29 线程1：[信息] [信息] 多线程模式：使用已分配的手机号码 +529656978287 (进度: 100%)
2025-08-04 18:11:29 线程1：[信息] [信息] 数据国家代码为CL，需要选择Chile (进度: 100%)
2025-08-04 18:11:29 线程1：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 100%)
2025-08-04 18:11:30 线程2：[信息] [信息] ✅ 成功定位到'创建访问密钥'按钮 (进度: 100%)
2025-08-04 18:11:30 [信息] 成功定位到'创建访问密钥'按钮
2025-08-04 18:11:30 线程2：[信息] [信息] ✅ 成功点击'创建访问密钥'按钮 (进度: 100%)
2025-08-04 18:11:30 [信息] 成功点击'创建访问密钥'按钮
2025-08-04 18:11:31 线程1：[信息] [信息] 已选择国家: Chile (进度: 100%)
2025-08-04 18:11:31 线程1：[信息] [信息] 已成功选择国家: Chile (进度: 100%)
2025-08-04 18:11:31 线程1：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 100%)
2025-08-04 18:11:31 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-04 18:11:32 线程2：[信息] [信息] ☑️ 正在勾选确认复选框... (进度: 100%)
2025-08-04 18:11:34 线程1：[信息] [信息] 已选择国家代码 +52 (进度: 100%)
2025-08-04 18:11:35 线程1：[信息] [信息] 等待后台获取的手机号码结果... (进度: 100%)
2025-08-04 18:11:35 线程1：[信息] [信息] 已自动获取并填入手机号码: +529656978287 (进度: 100%)
2025-08-04 18:11:36 线程1：[信息] [信息] 使用已获取的手机号码: +529656978287（保存本地号码: +529656978287） (进度: 100%)
2025-08-04 18:11:36 线程1：[信息] [信息] 联系信息完成，等待页面加载... (进度: 100%)
2025-08-04 18:11:37 线程2：[信息] [信息] ⚠️ id属性定位失败，使用CSS类定位 (进度: 100%)
2025-08-04 18:11:37 [信息] id属性定位失败，使用CSS类定位
2025-08-04 18:11:37 线程2：[信息] [信息] ✅ 成功勾选确认复选框 (进度: 100%)
2025-08-04 18:11:37 [信息] 成功勾选确认复选框
2025-08-04 18:11:38 线程2：[信息] [信息] 🔑 正在点击'创建访问密钥'确认按钮... (进度: 100%)
2025-08-04 18:11:39 线程2：[信息] [信息] ✅ 成功点击'创建访问密钥'确认按钮 (进度: 100%)
2025-08-04 18:11:39 [信息] 成功点击'创建访问密钥'确认按钮
2025-08-04 18:11:40 线程1：[信息] [信息] 进入付款信息页面... (进度: 100%)
2025-08-04 18:11:41 线程1：[信息] [信息] 正在选择月份: July (进度: 100%)
2025-08-04 18:11:41 线程1：[信息] [信息] 已选择月份（标准选项）: July (进度: 100%)
2025-08-04 18:11:42 线程2：[信息] [信息] 📋 开始复制访问密钥... (进度: 100%)
2025-08-04 18:11:42 [信息] 开始复制访问密钥
2025-08-04 18:11:42 线程1：[信息] [信息] 正在选择年份: 2027 (进度: 100%)
2025-08-04 18:11:42 线程1：[信息] [信息] 已选择年份（标准选项）: 2027 (进度: 100%)
2025-08-04 18:11:43 线程1：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 100%)
2025-08-04 18:11:43 线程1：[信息] [信息] 开始填写验证码验证页面... (进度: 100%)
2025-08-04 18:11:43 线程1：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 100%)
2025-08-04 18:11:44 线程2：[信息] [信息] 🔍 方法2找到 2 个单元格 (进度: 100%)
2025-08-04 18:11:44 [信息] 方法2找到 2 个单元格
2025-08-04 18:11:44 线程2：[信息] [信息] 🔍 总共找到 2 个单元格，开始分析... (进度: 100%)
2025-08-04 18:11:44 [信息] 总共找到 2 个单元格，开始分析
2025-08-04 18:11:44 [信息] 单元格[0]: 'AKIAVJYWVQRHXDBTNOQY'
2025-08-04 18:11:44 [信息] 单元格[1]: '***************Mostrar'
2025-08-04 18:11:44 线程2：[信息] [信息] 📋 正在查找并复制访问密钥... (进度: 100%)
2025-08-04 18:11:44 线程2：[信息] [信息] ✅ 找到访问密钥: AKIAVJYWVQRHXDBTNOQY (进度: 100%)
2025-08-04 18:11:44 [信息] 找到访问密钥: AKIAVJYWVQRHXDBTNOQY
2025-08-04 18:11:48 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-04 18:11:49 线程1：[信息] [信息] 已选择国家代码: +52 (进度: 100%)
2025-08-04 18:11:49 线程1：[信息] [信息] 已清空并重新填写手机号码: +529656978287 (进度: 100%)
2025-08-04 18:11:49 线程1：[信息] [信息] 已点击发送验证码按钮 (进度: 100%)
2025-08-04 18:11:51 线程1：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 100%)
2025-08-04 18:11:51 线程1：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-08-04 18:11:51 线程1：[信息] [信息] 手机号码自动模式 + 图形验证码自动模式：开始自动处理... (进度: 100%)
2025-08-04 18:11:51 线程1：[信息] [信息] 自动模式：开始处理图形验证码... (进度: 100%)
2025-08-04 18:11:51 线程1：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-04 18:11:54 线程1：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-04 18:11:54 线程1：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 18:12:02 线程1：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 35046 字节 (进度: 100%)
2025-08-04 18:12:02 线程1：[信息] [信息] ✅ 图片验证通过：201x70px，35046字节，复杂度符合要求 (进度: 100%)
2025-08-04 18:12:02 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 18:12:08 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"hsfgr3"},"taskId":"7a1356e8-711b-11f0-9768-ba03bdd70631"} (进度: 100%)
2025-08-04 18:12:08 线程1：[信息] [信息] 第六页第1次识别结果: hsfgr3 → 转换为小写: hsfgr3 (进度: 100%)
2025-08-04 18:12:08 线程1：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 18:12:08 线程1：[信息] [信息] 第六页已填入验证码: hsfgr3 (进度: 100%)
2025-08-04 18:12:08 线程1：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 18:12:11 线程1：[信息] [信息] 第1次图形验证码识别成功 (进度: 100%)
2025-08-04 18:12:11 线程1：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-08-04 18:12:13 线程2：[信息] [信息] ✅ 方法1成功点击访问密钥复制按钮 (进度: 100%)
2025-08-04 18:12:13 [信息] 方法1成功点击访问密钥复制按钮
2025-08-04 18:12:14 线程2：[信息] [信息] 📋 正在查找并复制秘密访问密钥... (进度: 100%)
2025-08-04 18:12:14 线程1：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-08-04 18:12:14 线程2：[信息] [信息] ✅ 找到秘密访问密钥: ***************Mostrar (进度: 100%)
2025-08-04 18:12:14 [信息] 找到秘密访问密钥: ***************Mostrar
2025-08-04 18:12:14 线程2：[信息] [信息] 🔍 检测到隐藏的秘密访问密钥，尝试点击显示按钮... (进度: 100%)
2025-08-04 18:12:14 [信息] 检测到隐藏的秘密访问密钥，尝试点击显示按钮
2025-08-04 18:12:14 线程2：[信息] [信息] ✅ 使用TestId定位到显示按钮 (进度: 100%)
2025-08-04 18:12:14 [信息] 使用TestId定位到显示按钮
2025-08-04 18:12:15 线程2：[信息] [信息] ✅ 显示按钮点击成功，新文本: LMQT/hnOalMxTvKMcJAJmRhwtixbGHF5Z/Ioi5aNOcultar (进度: 100%)
2025-08-04 18:12:15 [信息] 显示按钮点击成功，新文本: LMQT/hnOalMxTvKMcJAJmRhwtixbGHF5Z/Ioi5aNOcultar
2025-08-04 18:12:16 线程2：[信息] [信息] ✅ 直接从显示文本提取秘密访问密钥: LMQT/hnOalMxTvKMcJAJmRhwtixbGHF5Z/Ioi5aNOcultar (进度: 100%)
2025-08-04 18:12:16 [信息] 直接从显示文本提取秘密访问密钥: LMQT/hnOalMxTvKMcJAJmRhwtixbGHF5Z/Ioi5aNOcultar
2025-08-04 18:12:16 线程2：[信息] [信息] ✅ 访问密钥复制完成 (进度: 100%)
2025-08-04 18:12:16 [信息] 访问密钥复制完成 - AccessKey: AKIAVJYWVQRHXDBTNOQY, SecretKey: LMQT/hnOalMxTvKMcJAJmRhwtixbGHF5Z/Ioi5aNOcultar
2025-08-04 18:12:17 线程2：[信息] [信息] 🖱️ 正在点击'已完成'按钮... (进度: 100%)
2025-08-04 18:12:17 线程2：[信息] [信息] ✅ 成功点击'已完成'按钮 (进度: 100%)
2025-08-04 18:12:17 [信息] 成功点击'已完成'按钮
2025-08-04 18:12:17 线程2：[信息] [信息] ✅ 密钥已保存到数据对象 - AccessKey: AKIAVJYWVQRHXDBTNOQY, SecretKey: LMQT/hnOalMxTvKMcJAJmRhwtixbGHF5Z/Ioi5aNOcultar (进度: 100%)
2025-08-04 18:12:17 [信息] 密钥已保存到数据对象 - AccessKey: AKIAVJYWVQRHXDBTNOQY, SecretKey: LMQT/hnOalMxTvKMcJAJmRhwtixbGHF5Z/Ioi5aNOcultar
2025-08-04 18:12:17 线程1：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-04 18:12:17 线程1：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 18:12:17 线程1：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 18:12:17 线程1：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-08-04 18:12:18 线程2：[信息] [信息] 🖱️ 正在点击'继续'按钮... (进度: 100%)
2025-08-04 18:12:21 线程2：[信息] [信息] ❌ 点击'继续'按钮失败: Timeout 3000ms exceeded.
Call log:
  - waiting for Locator("button[data-awsui-analytics*='navigate']") to be visible (进度: 100%)
2025-08-04 18:12:21 [信息] 点击'继续'按钮失败: Timeout 3000ms exceeded.
Call log:
  - waiting for Locator("button[data-awsui-analytics*='navigate']") to be visible
2025-08-04 18:12:21 线程2：[信息] [信息] 🔐 开始设置MFA设备... (进度: 100%)
2025-08-04 18:12:21 [信息] 开始设置MFA设备
2025-08-04 18:12:21 线程2：[信息] [信息] 🖱️ 正在点击'分配 MFA 设备'按钮... (进度: 100%)
2025-08-04 18:12:21 线程2：[信息] [信息] ✅ 成功点击'分配 MFA 设备'按钮 (进度: 100%)
2025-08-04 18:12:21 [信息] 成功点击'分配 MFA 设备'按钮
2025-08-04 18:12:21 线程2：[信息] [信息] ⏳ 等待MFA设备设置页面加载（20秒超时）... (进度: 100%)
2025-08-04 18:12:22 线程1：[信息] [信息] 线程1开始独立获取验证码... (进度: 100%)
2025-08-04 18:12:22 线程1：[信息] [信息] 线程1开始获取验证码，最多尝试8次，每次间隔8秒 (进度: 100%)
2025-08-04 18:12:22 线程1：[信息] [信息] 线程1第1次尝试获取验证码...（剩余7次尝试） (进度: 100%)
2025-08-04 18:12:22 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-04 18:12:24 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-04 18:12:24 线程1：[信息] [信息] 线程1第1次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-04 18:12:24 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-08-04 18:12:32 线程1：[信息] [信息] 线程1第2次尝试获取验证码...（剩余6次尝试） (进度: 100%)
2025-08-04 18:12:32 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-04 18:12:33 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-04 18:12:33 线程1：[信息] [信息] 线程1第2次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-04 18:12:33 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-08-04 18:12:41 线程1：[信息] [信息] 线程1第3次尝试获取验证码...（剩余5次尝试） (进度: 100%)
2025-08-04 18:12:41 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-04 18:12:42 线程2：[信息] [信息] ⚠️ 20秒内未找到'设备名称'输入框，需要手动处理 (进度: 100%)
2025-08-04 18:12:42 线程2：[信息] [信息] ⚠️ 设备名称页面加载超时，需要手动处理 (进度: 100%)
2025-08-04 18:12:42 [信息] 设备名称页面加载超时，需要手动处理
2025-08-04 18:12:42 线程2：[信息] [信息] 页面加载完成后，手动点击继续注册 (进度: 100%)
2025-08-04 18:12:42 线程2：[信息] [信息] MFA页面需要手动处理，等待用户操作 (进度: 100%)
2025-08-04 18:12:42 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-04 18:12:42 线程1：[信息] [信息] 线程1第3次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-04 18:12:42 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-08-04 18:12:50 线程1：[信息] [信息] 线程1第4次尝试获取验证码...（剩余4次尝试） (进度: 100%)
2025-08-04 18:12:50 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-04 18:12:51 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-04 18:12:51 线程1：[信息] [信息] 线程1第4次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-04 18:12:51 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-08-04 18:12:59 线程1：[信息] [信息] 线程1第5次尝试获取验证码...（剩余3次尝试） (进度: 100%)
2025-08-04 18:12:59 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-04 18:12:59 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-04 18:12:59 线程1：[信息] [信息] 线程1第5次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-04 18:12:59 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-08-04 18:13:07 线程1：[信息] [信息] 线程1第6次尝试获取验证码...（剩余2次尝试） (进度: 100%)
2025-08-04 18:13:07 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-04 18:13:08 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-04 18:13:08 线程1：[信息] [信息] 线程1第6次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-04 18:13:08 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-08-04 18:13:16 线程1：[信息] [信息] 线程1第7次尝试获取验证码...（剩余1次尝试） (进度: 100%)
2025-08-04 18:13:16 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-04 18:13:16 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-04 18:13:16 线程1：[信息] [信息] 线程1第7次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-04 18:13:16 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-08-04 18:13:24 线程1：[信息] [信息] 线程1第8次尝试获取验证码...（剩余0次尝试） (进度: 100%)
2025-08-04 18:13:24 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-04 18:13:25 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-04 18:13:25 线程1：[信息] [信息] 线程1第8次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-04 18:13:25 线程1：[信息] [信息] 线程1验证码获取失败，已尝试8次 (进度: 100%)
2025-08-04 18:13:25 [信息] 线程1手机号码已加入释放队列: +529656978287 (原因: 验证码获取失败)
2025-08-04 18:13:25 线程1：[信息] [信息] 线程1验证码获取失败: 获取失败，已尝试8次 (进度: 100%)
2025-08-04 18:13:25 线程1：[信息] [信息] 验证码获取失败，第1次重试... (进度: 100%)
2025-08-04 18:13:25 线程1：[信息] [信息] 正在自动重试第1次，同时加入黑名单和获取新号码... (进度: 100%)
2025-08-04 18:13:25 线程1：[信息] [信息] 正在返回上一页... (进度: 100%)
2025-08-04 18:13:25 线程1：[信息] [信息] 已点击返回按钮 (进度: 100%)
2025-08-04 18:13:26 线程1：[信息] [信息] 超时号码已加入黑名单 (进度: 100%)
2025-08-04 18:13:28 线程1：[信息] [信息] 正在选择国家代码: +52 (进度: 100%)
2025-08-04 18:13:30 线程1：[信息] [信息] 已打开国家代码下拉列表 (进度: 100%)
2025-08-04 18:13:31 [信息] 定时检查发现1个待释放手机号码，开始批量释放
2025-08-04 18:13:31 [信息] 开始释放1个手机号码
2025-08-04 18:13:31 [信息] [手机API] 开始批量释放1个手机号码
2025-08-04 18:13:31 [信息] [手机API] 释放手机号码: +529656978287
2025-08-04 18:13:31 线程1：[信息] [信息] 已选择国家代码: +52 (进度: 100%) (进度: 100%)
2025-08-04 18:13:31 线程1：[信息] [信息] 后台获取新手机号码... (进度: 100%)
2025-08-04 18:13:31 [信息] [榴莲API] 获取手机号码，尝试 1/4
2025-08-04 18:13:32 [信息] [手机API] 手机号码释放成功: +529656978287
2025-08-04 18:13:32 线程1：[信息] [信息] 后台获取新手机号码成功: +529131010207，已保存到注册数据 (进度: 100%)
2025-08-04 18:13:32 线程1：[信息] [信息] 正在清空并填入新的手机号码... (进度: 100%)
2025-08-04 18:13:32 线程1：[信息] [信息] 已填入新手机号码: +529131010207 (进度: 100%)
2025-08-04 18:13:32 线程1：[信息] [信息] 正在点击发送短信按钮... (进度: 100%)
2025-08-04 18:13:32 [信息] [手机API] 批量释放完成: 成功1个, 失败0个
2025-08-04 18:13:32 [信息] 定时批量释放完成: 批量释放完成: 成功1个, 失败0个
2025-08-04 18:13:34 线程1：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 100%)
2025-08-04 18:13:34 线程1：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-08-04 18:13:35 线程1：[信息] [信息] 新手机号码已填入，自动模式：开始处理图形验证码... (进度: 100%)
2025-08-04 18:13:35 线程1：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-04 18:13:38 线程1：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-04 18:13:38 线程1：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 18:13:39 [信息] 获取线程2当前数据: <EMAIL>
2025-08-04 18:13:39 线程2：[信息] 终止时正在处理的数据: <EMAIL>
2025-08-04 18:13:39 线程2：[信息] 数据详情: <EMAIL>|xWjS4da1|villalobos orlando|Codelco|peuelas norte 186 torre 1 dep|Coquimbo|Coquimbo|1780000|5331870085516446|08|29|658|villalobos orlando|85c6lB591K|CL
2025-08-04 18:13:39 线程2：[信息] 多线程终止 - 复制密钥信息到剪贴板
2025-08-04 18:13:39 线程2：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：85c6lB591K ③AWS密码：xWjS4da1 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止 (进度: 100%)
2025-08-04 18:13:39 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：85c6lB591K ③AWS密码：xWjS4da1 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 18:13:39 线程2：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：85c6lB591K ③AWS密码：xWjS4da1 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 18:13:39 [信息] 线程2请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：85c6lB591K ③AWS密码：xWjS4da1 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 18:13:39 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：85c6lB591K ③AWS密码：xWjS4da1 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 18:13:39 [信息] 线程2剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：85c6lB591K ③AWS密码：xWjS4da1 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 18:13:39 线程2：[信息] [信息] 注册已终止 (进度: 100%)
2025-08-04 18:13:39 线程2：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-08-04 18:13:40 线程2：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：85c6lB591K ③AWS密码：xWjS4da1 ④访问密钥：AKIAVJYWVQRHXDBTNOQY ⑤秘密访问密钥：LMQT/hnOalMxTvKMcJAJmRhwtixbGHF5Z/Ioi5aNOcultar ⑥MFA信息：   //手动终止 (进度: 100%)
2025-08-04 18:13:40 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：85c6lB591K ③AWS密码：xWjS4da1 ④访问密钥：AKIAVJYWVQRHXDBTNOQY ⑤秘密访问密钥：LMQT/hnOalMxTvKMcJAJmRhwtixbGHF5Z/Ioi5aNOcultar ⑥MFA信息：   //手动终止
2025-08-04 18:13:40 线程2：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：85c6lB591K ③AWS密码：xWjS4da1 ④访问密钥：AKIAVJYWVQRHXDBTNOQY ⑤秘密访问密钥：LMQT/hnOalMxTvKMcJAJmRhwtixbGHF5Z/Ioi5aNOcultar ⑥MFA信息：   //手动终止
2025-08-04 18:13:40 [信息] 线程2请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：85c6lB591K ③AWS密码：xWjS4da1 ④访问密钥：AKIAVJYWVQRHXDBTNOQY ⑤秘密访问密钥：LMQT/hnOalMxTvKMcJAJmRhwtixbGHF5Z/Ioi5aNOcultar ⑥MFA信息：   //手动终止
2025-08-04 18:13:40 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：85c6lB591K ③AWS密码：xWjS4da1 ④访问密钥：AKIAVJYWVQRHXDBTNOQY ⑤秘密访问密钥：LMQT/hnOalMxTvKMcJAJmRhwtixbGHF5Z/Ioi5aNOcultar ⑥MFA信息：   //手动终止
2025-08-04 18:13:40 [信息] 线程2剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：85c6lB591K ③AWS密码：xWjS4da1 ④访问密钥：AKIAVJYWVQRHXDBTNOQY ⑤秘密访问密钥：LMQT/hnOalMxTvKMcJAJmRhwtixbGHF5Z/Ioi5aNOcultar ⑥MFA信息：   //手动终止
2025-08-04 18:13:40 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-08-04 18:13:40 线程2：[信息] 临时目录已清理: C:\Users\<USER>\AppData\Local\Temp\AWS_Thread_2_20250804_180701
2025-08-04 18:13:40 线程2：[信息] 已终止
2025-08-04 18:13:40 [信息] 线程2已终止
2025-08-04 18:13:40 [信息] 开始处理线程2终止数据，共1个数据
2025-08-04 18:13:40 [信息] 处理线程2终止数据: <EMAIL>
2025-08-04 18:13:40 [信息] 从注册数据列表中移除: <EMAIL>
2025-08-04 18:13:40 [信息] 线程2终止 - 数据已移动到终止列表: <EMAIL>
2025-08-04 18:13:40 [信息] 线程2终止数据处理完成，成功移动1个数据
2025-08-04 18:13:40 [信息] UniformGrid列数已更新为: 1
2025-08-04 18:13:40 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-08-04 18:13:40 [信息] 线程2已终止
2025-08-04 18:13:42 线程1：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 35463 字节 (进度: 100%)
2025-08-04 18:13:42 线程1：[信息] [信息] ✅ 图片验证通过：201x70px，35463字节，复杂度符合要求 (进度: 100%)
2025-08-04 18:13:42 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 18:13:46 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"rx6s6gs"},"taskId":"b52f06b4-711b-11f0-b896-029e46bc98e3"} (进度: 100%)
2025-08-04 18:13:46 线程1：[信息] [信息] 第六页第1次识别结果: rx6s6gs → 转换为小写: rx6s6gs (进度: 100%)
2025-08-04 18:13:46 线程1：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 18:13:46 线程1：[信息] [信息] 第六页已填入验证码: rx6s6gs (进度: 100%)
2025-08-04 18:13:47 线程1：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 18:13:51 线程1：[信息] [信息] 第1次图形验证码识别结果错误，等待新验证码 (进度: 100%)
2025-08-04 18:13:51 线程1：[信息] [信息] 第六页第1次识别异常: 验证码错误 (进度: 100%)
2025-08-04 18:13:53 线程1：[信息] [信息] 第六页第2次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 18:14:26 线程1：[信息] [信息] 第六页验证码元素等待超时，转为手动模式 (进度: 100%)
2025-08-04 18:14:26 线程1：[信息] [信息] 第六页第2次识别异常: 验证码元素等待超时，需要手动处理 (进度: 100%)
2025-08-04 18:14:26 线程1：[信息] [信息] 第六页验证码元素等待超时，直接转为手动模式 (进度: 100%)
2025-08-04 18:14:26 线程1：[信息] [信息] 当前验证码抓取出错，需要手动完成图形验证码，并点击继续注册 (进度: 100%)
2025-08-04 18:14:26 线程1：[信息] 已继续
2025-08-04 18:14:26 [信息] 线程1已继续
2025-08-04 18:14:57 线程1：[信息] [信息]  继续注册被调用，当前状态: WaitingForSMSVerification，当前步骤: 7 (进度: 100%)
2025-08-04 18:14:57 线程1：[信息] [信息] 🔧 [DEBUG] 检测到第七页特征: *:has-text('step 4 of 5') (进度: 100%)
2025-08-04 18:14:57 线程1：[信息] [信息] 第七页手动验证码已输入，检测当前页面状态... (进度: 100%)
2025-08-04 18:14:57 线程1：[信息] [信息] 第七页：智能检测当前页面状态... (进度: 100%)
2025-08-04 18:14:57 线程1：[信息] [信息] 🔧 [DEBUG] 使用修改后的HandleStep7SmartContinue方法 - 版本2025-07-28 (进度: 100%)
2025-08-04 18:14:57 线程1：[信息] [信息] 🔧 [DEBUG] 开始检测第七页手机验证码是否已填写... (进度: 100%)
2025-08-04 18:14:57 线程1：[信息] [信息] 🔧 [DEBUG] 检测到第七页特征: *:has-text('step 4 of 5') (进度: 100%)
2025-08-04 18:14:57 线程1：[信息] [信息] 🔧 [DEBUG] 通过父元素文本确认为第七页验证码输入框 (进度: 100%)
2025-08-04 18:14:57 线程1：[信息] [信息] ✅ 检测到第七页手机验证码已填写: 6809位 (进度: 100%)
2025-08-04 18:14:57 线程1：[信息] [信息] 第七页：检测到用户已输入验证码，直接执行手动模式处理流程... (进度: 100%)
2025-08-04 18:14:57 线程1：[信息] [信息] 🔧 [DEBUG] 开始检测第七页手机验证码是否已填写... (进度: 100%)
2025-08-04 18:14:57 线程1：[信息] [信息] 🔧 [DEBUG] 检测到第七页特征: *:has-text('step 4 of 5') (进度: 100%)
2025-08-04 18:14:57 线程1：[信息] [信息] 🔧 [DEBUG] 通过父元素文本确认为第七页验证码输入框 (进度: 100%)
2025-08-04 18:14:57 线程1：[信息] [信息] ✅ 检测到第七页手机验证码已填写: 6809位 (进度: 100%)
2025-08-04 18:14:57 线程1：[信息] [信息] 第七页：检测到验证码已填写，查找Continue按钮... (进度: 100%)
2025-08-04 18:14:57 线程1：[信息] [信息] 第七页：检测到Continue (step 4 of 5)按钮，自动点击... (进度: 100%)
2025-08-04 18:14:57 线程1：[信息] [信息] 第七页：已点击Continue (step 4 of 5)按钮，等待页面跳转... (进度: 100%)
2025-08-04 18:14:59 线程1：[信息] [信息]  继续注册被调用，当前状态: WaitingForSMSVerification，当前步骤: 7 (进度: 100%)
2025-08-04 18:14:59 线程1：[信息] [信息] 🔧 [DEBUG] 检测到第七页特征: *:has-text('step 4 of 5') (进度: 100%)
2025-08-04 18:14:59 线程1：[信息] [信息] 第七页手动验证码已输入，检测当前页面状态... (进度: 100%)
2025-08-04 18:14:59 线程1：[信息] [信息] 第七页：智能检测当前页面状态... (进度: 100%)
2025-08-04 18:14:59 线程1：[信息] [信息] 🔧 [DEBUG] 使用修改后的HandleStep7SmartContinue方法 - 版本2025-07-28 (进度: 100%)
2025-08-04 18:14:59 线程1：[信息] [信息] 🔧 [DEBUG] 开始检测第七页手机验证码是否已填写... (进度: 100%)
2025-08-04 18:14:59 线程1：[信息] [信息] 🔧 [DEBUG] 检测到第七页特征: *:has-text('step 4 of 5') (进度: 100%)
2025-08-04 18:14:59 线程1：[信息] [信息] 🔧 [DEBUG] 通过父元素文本确认为第七页验证码输入框 (进度: 100%)
2025-08-04 18:14:59 线程1：[信息] [信息] ✅ 检测到第七页手机验证码已填写: 6849位 (进度: 100%)
2025-08-04 18:14:59 线程1：[信息] [信息] 第七页：检测到用户已输入验证码，直接执行手动模式处理流程... (进度: 100%)
2025-08-04 18:14:59 线程1：[信息] [信息] 🔧 [DEBUG] 开始检测第七页手机验证码是否已填写... (进度: 100%)
2025-08-04 18:14:59 线程1：[信息] [信息] 🔧 [DEBUG] 检测到第七页特征: *:has-text('step 4 of 5') (进度: 100%)
2025-08-04 18:15:00 线程1：[信息] [信息] 🔧 [DEBUG] 通过父元素文本确认为第七页验证码输入框 (进度: 100%)
2025-08-04 18:15:00 线程1：[信息] [信息] ✅ 检测到第七页手机验证码已填写: 6849位 (进度: 100%)
2025-08-04 18:15:00 线程1：[信息] [信息] 第七页：检测到验证码已填写，查找Continue按钮... (进度: 100%)
2025-08-04 18:15:00 线程1：[信息] [信息] 第七页：检测到Continue (step 4 of 5)按钮，自动点击... (进度: 100%)
2025-08-04 18:15:00 线程1：[信息] [信息] 第七页：已点击Continue (step 4 of 5)按钮，等待页面跳转... (进度: 100%)
2025-08-04 18:15:00 线程1：[信息] [信息] 第七页Continue按钮处理完成，开始执行后续注册流程... (进度: 100%)
2025-08-04 18:15:00 线程1：[信息] [信息] 验证码验证完成，等待页面加载... (进度: 100%)
2025-08-04 18:15:03 线程1：[信息] [信息] 自动模式验证完成，API手机号码已加入黑名单: 加入黑名单成功 (进度: 100%)
2025-08-04 18:15:03 线程1：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-08-04 18:15:03 线程1：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 100%)
2025-08-04 18:15:03 线程1：[信息] 已暂停
2025-08-04 18:15:03 [信息] 线程1已暂停
2025-08-04 18:15:03 [信息] 线程1已暂停
2025-08-04 18:15:03 线程1：[信息] [信息] 第七页Continue按钮处理完成，开始执行后续注册流程... (进度: 100%)
2025-08-04 18:15:03 线程1：[信息] [信息] 验证码验证完成，等待页面加载... (进度: 100%)
2025-08-04 18:15:03 线程1：[信息] [信息] 自动模式验证完成，API手机号码已加入黑名单: 加入黑名单失败: 请勿重复操作 (进度: 100%)
2025-08-04 18:15:04 线程1：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-08-04 18:15:04 线程1：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 100%)
2025-08-04 18:15:04 线程1：[信息] 已暂停
2025-08-04 18:15:04 [信息] 线程1已暂停
2025-08-04 18:15:04 [信息] 线程1已暂停
2025-08-04 18:15:30 线程1：[信息] [信息]  继续注册被调用，当前状态: Paused，当前步骤: 7 (进度: 100%)
2025-08-04 18:15:30 线程1：[信息] [信息]  进行智能页面检测... (进度: 100%)
2025-08-04 18:15:30 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-04 18:15:30 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-04 18:15:30 线程1：[信息] [信息] 🎯 找到匹配按钮: 'Continue (step 4 of 5)' → 第7页 (进度: 100%)
2025-08-04 18:15:30 线程1：[信息] [信息] ✅ 直接确认为第7页 (进度: 100%)
2025-08-04 18:15:30 线程1：[信息] [信息]  智能检测到当前在第7页 (进度: 100%)
2025-08-04 18:15:30 线程1：[信息] [信息] 智能检测到当前在第7页，开始智能处理... (进度: 100%)
2025-08-04 18:15:30 线程1：[信息] 已继续
2025-08-04 18:15:30 [信息] 线程1已继续
2025-08-04 18:15:30 线程1：[信息] [信息] 点击完成注册按钮失败: Timeout 30000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Button, new() { Name = "Complete sign up" }) (进度: 100%)
2025-08-04 18:15:30 线程1：[信息] [信息] 等待完成注册页面加载... (进度: 100%)
2025-08-04 18:15:36 线程1：[信息] [信息] 完成注册页面加载完成 (进度: 100%)
2025-08-04 18:15:36 线程1：[信息] [信息] 🔍 正在查找'Go to the AWS Management Console'链接... (进度: 100%)
2025-08-04 18:15:36 线程1：[信息] [信息] 点击完成注册按钮失败: Timeout 30000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Button, new() { Name = "Complete sign up" }) (进度: 100%)
2025-08-04 18:15:36 线程1：[信息] [信息] 等待完成注册页面加载... (进度: 100%)
2025-08-04 18:15:37 线程1：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-08-04 18:15:37 线程1：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 100%)
2025-08-04 18:15:37 线程1：[信息] 已暂停
2025-08-04 18:15:37 [信息] 线程1已暂停
2025-08-04 18:15:37 [信息] 线程1已暂停
2025-08-04 18:15:38 线程1：[信息] [信息]  继续注册被调用，当前状态: Paused，当前步骤: 7 (进度: 100%)
2025-08-04 18:15:38 线程1：[信息] [信息]  进行智能页面检测... (进度: 100%)
2025-08-04 18:15:38 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-04 18:15:38 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-04 18:15:39 线程1：[信息] [信息] 完成注册页面加载完成 (进度: 100%)
2025-08-04 18:15:39 线程1：[信息] [信息] 🔍 正在查找'Go to the AWS Management Console'链接... (进度: 100%)
2025-08-04 18:15:39 线程1：[信息] [信息] 🎯 找到匹配按钮: 'Complete sign up' → 第8页 (进度: 100%)
2025-08-04 18:15:39 线程1：[信息] [信息] ✅ 直接确认为第8页 (进度: 100%)
2025-08-04 18:15:39 线程1：[信息] [信息]  智能检测到当前在第8页 (进度: 100%)
2025-08-04 18:15:39 线程1：[信息] [信息] 智能检测到当前在第8页，开始智能处理... (进度: 100%)
2025-08-04 18:15:39 线程1：[信息] 已继续
2025-08-04 18:15:39 [信息] 线程1已继续
2025-08-04 18:15:41 线程1：[信息] [信息] ❌ 控制台按钮处理失败: Timeout 5000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Link, new() { Name = "Go to the AWS Management Console" }) to be visible (进度: 100%)
2025-08-04 18:15:41 [信息] 控制台按钮处理失败: Timeout 5000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Link, new() { Name = "Go to the AWS Management Console" }) to be visible
2025-08-04 18:15:41 线程1：[信息] [信息] ⚠️ 密钥提取失败，按原本逻辑完成注册 (进度: 100%)
2025-08-04 18:15:41 [信息] 密钥提取失败，按原本逻辑完成注册
2025-08-04 18:15:41 线程1：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：9F9H22YJ ③AWS密码：mc2oPiqW ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息： (进度: 100%)
2025-08-04 18:15:41 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：9F9H22YJ ③AWS密码：mc2oPiqW ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 18:15:41 线程1：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：9F9H22YJ ③AWS密码：mc2oPiqW ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 18:15:41 [信息] 线程1请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：9F9H22YJ ③AWS密码：mc2oPiqW ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 18:15:41 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：9F9H22YJ ③AWS密码：mc2oPiqW ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 18:15:41 [信息] 线程1剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：9F9H22YJ ③AWS密码：mc2oPiqW ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 18:15:41 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-08-04 18:15:41 线程1：[信息] 账户注册完成(无密钥)，等待后续操作: <EMAIL>
2025-08-04 18:15:41 线程1：[信息] [信息] ✅ 注册成功（密钥提取失败） (进度: 98%)
2025-08-04 18:15:41 [信息] 注册完成（密钥提取失败）
2025-08-04 18:15:41 [信息] 开始处理数据完成事件: <EMAIL>
2025-08-04 18:15:41 [信息] 已将完成数据移动到注册成功区域: <EMAIL>
2025-08-04 18:15:41 [信息] 已完成数据移除: <EMAIL>
2025-08-04 18:15:41 [信息] 数据完成事件处理完毕: <EMAIL>
2025-08-04 18:15:41 线程1：[信息] 最终完成: 注册完成，无法提取密钥: <EMAIL> (类型: WithoutKeys)
2025-08-04 18:15:41 [信息] 线程1数据完成事件已通知主窗口并最终完成: <EMAIL>
2025-08-04 18:15:41 线程1：[信息] 已继续
2025-08-04 18:15:41 [信息] 线程1已继续
2025-08-04 18:15:44 线程1：[信息] [信息] ❌ 控制台按钮处理失败: Timeout 5000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Link, new() { Name = "Go to the AWS Management Console" }) to be visible (进度: 100%)
2025-08-04 18:15:44 [信息] 控制台按钮处理失败: Timeout 5000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Link, new() { Name = "Go to the AWS Management Console" }) to be visible
2025-08-04 18:15:44 线程1：[信息] [信息] ⚠️ 密钥提取失败，按原本逻辑完成注册 (进度: 100%)
2025-08-04 18:15:44 [信息] 密钥提取失败，按原本逻辑完成注册
2025-08-04 18:15:44 线程1：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：9F9H22YJ ③AWS密码：mc2oPiqW ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息： (进度: 100%)
2025-08-04 18:15:44 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：9F9H22YJ ③AWS密码：mc2oPiqW ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 18:15:44 线程1：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：9F9H22YJ ③AWS密码：mc2oPiqW ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 18:15:44 [信息] 线程1请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：9F9H22YJ ③AWS密码：mc2oPiqW ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 18:15:44 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：9F9H22YJ ③AWS密码：mc2oPiqW ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 18:15:44 [信息] 线程1剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：9F9H22YJ ③AWS密码：mc2oPiqW ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 18:15:44 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-08-04 18:15:44 线程1：[信息] 账户注册完成(无密钥)，等待后续操作: <EMAIL>
2025-08-04 18:15:44 线程1：[信息] [信息] ✅ 注册成功（密钥提取失败） (进度: 98%)
2025-08-04 18:15:44 [信息] 注册完成（密钥提取失败）
2025-08-04 18:15:44 [信息] 开始处理数据完成事件: <EMAIL>
2025-08-04 18:15:44 [信息] 已将完成数据移动到注册成功区域: <EMAIL>
2025-08-04 18:15:44 [信息] 数据完成事件处理完毕: <EMAIL>
2025-08-04 18:15:44 线程1：[信息] 最终完成: 注册完成，无法提取密钥: <EMAIL> (类型: WithoutKeys)
2025-08-04 18:15:44 [信息] 线程1数据完成事件已通知主窗口并最终完成: <EMAIL>
2025-08-04 18:15:44 线程1：[信息] 已继续
2025-08-04 18:15:44 [信息] 线程1已继续
2025-08-04 18:18:30 [信息] 多线程窗口引用已清理
2025-08-04 18:18:30 [信息] 主窗口已恢复正常状态，多线程窗口引用已清理
2025-08-04 18:18:30 [信息] 多线程管理窗口正在关闭
2025-08-04 18:18:31 [信息] 程序正在退出，开始清理工作...
2025-08-04 18:18:31 [信息] 程序退出，开始清理所有待拉黑千川手机号码
2025-08-04 18:18:31 [信息] 程序退出清理完成: 没有需要拉黑的千川手机号码
2025-08-04 18:18:31 [信息] 程序退出，开始清理所有待释放手机号码
2025-08-04 18:18:31 [信息] 程序退出清理完成: 没有需要释放的手机号码
2025-08-04 18:18:31 [信息] 程序退出清理工作完成
