2025-08-04 21:49:22 [信息] AWS自动注册工具启动
2025-08-04 21:49:22 [信息] 程序版本: 1.0.0.0
2025-08-04 21:49:22 [信息] 启动时间: 2025-08-04 21:49:22
2025-08-04 21:49:22 [信息] 浏览器模式切换: 无痕 Chrome 模式
2025-08-04 21:49:22 [信息] 线程数量已选择: 1
2025-08-04 21:49:22 [信息] 线程数量选择初始化完成
2025-08-04 21:49:22 [信息] 程序初始化完成
2025-08-04 21:49:25 [按钮操作] 选择文件 -> 打开文件选择对话框
2025-08-04 21:49:28 [信息] 已选择文件: C:\Users\<USER>\Desktop\2025-08-04-智利.txt
2025-08-04 21:49:28 [按钮操作] 加载信息 -> 加载数据文件: C:\Users\<USER>\Desktop\2025-08-04-智利.txt
2025-08-04 21:49:28 [信息] 成功加载 5 条数据
2025-08-04 21:51:42 [按钮操作] 开始注册 -> 启动注册流程
2025-08-04 21:51:43 [系统状态] 正在检测Chrome浏览器(无痕模式)...
2025-08-04 21:51:44 [系统状态] 使用默认浏览器语言: English (United States)
2025-08-04 21:51:44 [信息] 浏览器语言设置: 使用默认语言 en-US
2025-08-04 21:51:44 [信息] 单线程模式窗口布局: 使用固定位置(0, 0), 大小(600x400)
2025-08-04 21:51:44 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0) Gecko/20100101 Firefox/119.0
2025-08-04 21:51:44 [系统状态] 尝试启动系统Chrome(无痕模式)...
2025-08-04 21:51:45 [信息] 程序正在退出，开始清理工作...
2025-08-04 21:51:45 [信息] 程序退出，开始清理所有待拉黑千川手机号码
2025-08-04 21:51:45 [信息] 程序退出清理完成: 没有需要拉黑的千川手机号码
2025-08-04 21:51:45 [信息] 程序退出，开始清理所有待释放手机号码
2025-08-04 21:51:45 [信息] 程序退出清理完成: 没有需要释放的手机号码
2025-08-04 21:51:45 [信息] 程序退出清理工作完成
2025-08-04 21:51:45 [系统状态] 创建无痕模式上下文...
2025-08-04 21:51:47 [信息] AWS自动注册工具启动
2025-08-04 21:51:47 [信息] 程序版本: 1.0.0.0
2025-08-04 21:51:47 [信息] 启动时间: 2025-08-04 21:51:47
2025-08-04 21:51:47 [信息] 浏览器模式切换: 无痕 Chrome 模式
2025-08-04 21:51:47 [信息] 线程数量已选择: 1
2025-08-04 21:51:47 [信息] 线程数量选择初始化完成
2025-08-04 21:51:47 [信息] 程序初始化完成
2025-08-04 21:51:51 [信息] 线程数量已选择: 3
2025-08-04 21:51:51 [按钮操作] 选择文件 -> 打开文件选择对话框
2025-08-04 21:51:53 [信息] 已选择文件: C:\Users\<USER>\Desktop\2025-08-04-智利.txt
2025-08-04 21:51:54 [按钮操作] 加载信息 -> 加载数据文件: C:\Users\<USER>\Desktop\2025-08-04-智利.txt
2025-08-04 21:51:54 [信息] 成功加载 5 条数据
2025-08-04 21:52:00 [按钮操作] 开始注册 -> 启动注册流程
2025-08-04 21:52:00 [信息] 开始启动多线程注册，线程数量: 3
2025-08-04 21:52:00 [信息] 开始启动多线程注册，线程数量: 3，数据条数: 5
2025-08-04 21:52:00 [信息] 所有线程已停止并清理
2025-08-04 21:52:00 [信息] 正在初始化多线程服务...
2025-08-04 21:52:00 [信息] 榴莲手机API服务已初始化
2025-08-04 21:52:00 [信息] 手机号码管理器已初始化，服务商: Durian，将在第一个线程完成第二页后获取手机号码
2025-08-04 21:52:00 [信息] 多线程服务初始化完成
2025-08-04 21:52:00 [信息] 数据分配完成：共5条数据分配给3个线程
2025-08-04 21:52:00 [信息] 线程1分配到2条数据
2025-08-04 21:52:00 [信息] 线程2分配到2条数据
2025-08-04 21:52:00 [信息] 线程3分配到1条数据
2025-08-04 21:52:00 [信息] 屏幕工作区域: 1280x672
2025-08-04 21:52:00 [信息] 线程1窗口布局: 位置(0, 0), 大小(384x200), 列1行1, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-04 21:52:00 [信息] 线程1获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-04 21:52:00 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 21:52:00 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_003, WebGL=ANGLE (NVIDIA GeForce RTX 3070 Direct3D11 vs_5_0 ps_5_0), CPU=20核, RAM=12 GB
2025-08-04 21:52:00 线程1：[信息] 已创建，窗口位置: (0, 0)
2025-08-04 21:52:00 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-04 21:52:00 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-04 21:52:00 [信息] 线程1已创建，窗口位置: (0, 0)，指纹: 国家=CL, 时区=America/Santiago
2025-08-04 21:52:00 [信息] 屏幕工作区域: 1280x672
2025-08-04 21:52:00 [信息] 线程2窗口布局: 位置(0, 219), 大小(384x200), 列1行2, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-04 21:52:00 [信息] 线程2获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-04 21:52:00 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 21:52:00 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_008, WebGL=ANGLE (AMD Radeon RX 6700 XT Direct3D11 vs_5_0 ps_5_0), CPU=20核, RAM=12 GB
2025-08-04 21:52:00 线程2：[信息] 已创建，窗口位置: (0, 219)
2025-08-04 21:52:00 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-04 21:52:00 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-04 21:52:00 [信息] 线程2已创建，窗口位置: (0, 219)，指纹: 国家=CL, 时区=America/Santiago
2025-08-04 21:52:00 [信息] 屏幕工作区域: 1280x672
2025-08-04 21:52:00 [信息] 线程3窗口布局: 位置(0, 438), 大小(384x200), 列1行3, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-04 21:52:00 [信息] 线程3获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-04 21:52:00 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 21:52:00 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_010, WebGL=ANGLE (AMD Radeon RX 6700 XT Direct3D11 vs_5_0 ps_5_0), CPU=24核, RAM=10 GB
2025-08-04 21:52:00 线程3：[信息] 已创建，窗口位置: (0, 438)
2025-08-04 21:52:00 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-04 21:52:00 [信息] 线程3已创建，窗口位置: (0, 438)，指纹: 国家=CL, 时区=America/Santiago
2025-08-04 21:52:00 [信息] 多线程注册启动成功，共3个线程
2025-08-04 21:52:00 线程2：[信息] 开始启动注册流程
2025-08-04 21:52:00 线程1：[信息] 开始启动注册流程
2025-08-04 21:52:00 线程3：[信息] 开始启动注册流程
2025-08-04 21:52:00 线程2：[信息] 开始启动浏览器: 位置(0, 219), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0
2025-08-04 21:52:00 线程1：[信息] 开始启动浏览器: 位置(0, 0), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36
2025-08-04 21:52:00 线程1：[信息] 启动无痕Chrome浏览器...
2025-08-04 21:52:00 线程2：[信息] 启动无痕Chrome浏览器...
2025-08-04 21:52:00 线程3：[信息] 开始启动浏览器: 位置(0, 438), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0
2025-08-04 21:52:00 线程3：[信息] 启动无痕Chrome浏览器...
2025-08-04 21:52:00 线程3：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-04 21:52:00 线程2：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-04 21:52:00 线程1：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-04 21:52:00 [信息] 多线程管理窗口已初始化
2025-08-04 21:52:00 [信息] UniformGrid列数已更新为: 1
2025-08-04 21:52:00 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 0, 列数: 1
2025-08-04 21:52:00 [信息] 多线程管理窗口已打开
2025-08-04 21:52:00 [信息] 多线程注册启动成功，共3个线程
2025-08-04 21:52:03 [信息] UniformGrid列数已更新为: 1
2025-08-04 21:52:03 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 1, 列数: 1
2025-08-04 21:52:03 线程3：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-04 21:52:03 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-04 21:52:03 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36
2025-08-04 21:52:03 线程3：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-04 21:52:04 [信息] UniformGrid列数已更新为: 1
2025-08-04 21:52:04 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-08-04 21:52:04 线程2：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-04 21:52:04 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-04 21:52:04 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36
2025-08-04 21:52:04 线程2：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-04 21:52:04 [信息] UniformGrid列数已更新为: 2
2025-08-04 21:52:04 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 3, 列数: 2
2025-08-04 21:52:04 线程1：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-04 21:52:04 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-04 21:52:04 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0
2025-08-04 21:52:04 线程1：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-04 21:52:06 线程2：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-04 21:52:06 线程1：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-04 21:52:06 线程3：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-04 21:52:08 线程2：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-04 21:52:08 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-04 21:52:08 线程2：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-04 21:52:08 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 21:52:08 线程2：[信息] [信息] 多线程模式使用指纹地理位置: -36.8201, -73.0444 (进度: 0%)
2025-08-04 21:52:08 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-36.8201, 经度=-73.0444
2025-08-04 21:52:08 线程2：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_008, CPU: 20核 (进度: 0%)
2025-08-04 21:52:08 [信息] 浏览器指纹注入: Canvas=canvas_fp_008, WebGL=ANGLE (AMD Radeon RX 6700 XT Direct3D11 vs_5_0 ps_5_0), CPU=20核, RAM=12 GB
2025-08-04 21:52:08 线程1：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-04 21:52:08 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-04 21:52:08 线程1：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-04 21:52:08 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 21:52:08 线程1：[信息] [信息] 多线程模式使用指纹地理位置: -23.6509, -70.3975 (进度: 0%)
2025-08-04 21:52:08 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-23.6509, 经度=-70.3975
2025-08-04 21:52:08 线程3：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-04 21:52:08 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-04 21:52:08 线程3：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-04 21:52:08 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 21:52:08 线程3：[信息] [信息] 多线程模式使用指纹地理位置: -41.4693, -72.9424 (进度: 0%)
2025-08-04 21:52:08 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-41.4693, 经度=-72.9424
2025-08-04 21:52:08 线程1：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_003, CPU: 20核 (进度: 0%)
2025-08-04 21:52:08 [信息] 浏览器指纹注入: Canvas=canvas_fp_003, WebGL=ANGLE (NVIDIA GeForce RTX 3070 Direct3D11 vs_5_0 ps_5_0), CPU=20核, RAM=12 GB
2025-08-04 21:52:08 线程3：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_010, CPU: 24核 (进度: 0%)
2025-08-04 21:52:08 [信息] 浏览器指纹注入: Canvas=canvas_fp_010, WebGL=ANGLE (AMD Radeon RX 6700 XT Direct3D11 vs_5_0 ps_5_0), CPU=24核, RAM=10 GB
2025-08-04 21:52:11 线程2：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-04 21:52:11 线程1：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-04 21:52:12 线程3：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-04 21:52:14 线程3：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 24
   • 设备内存: 10 GB
   • 平台信息: Win32
   • Do Not Track: unspecified

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (AMD Radeon RX 6700 XT Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (NVIDIA)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 1E2F3A4B
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_008
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-K8L9M0N
   • MAC地址: DE-F0-12-34-56-78
   • 屏幕分辨率: 1881x1121
   • 可用区域: 1881x1081

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: C1D2E3F4
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: 3g
   • 电池API支持: True
   • 电池电量: 0.31
   • 电池充电状态: False
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-04 21:52:14 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 24    • 设备内存: 10 GB    • 平台信息: Win32    • Do Not Track: unspecified   图形渲染信息:    • WebGL渲染器: ANGLE (AMD Radeon RX 6700 XT Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (NVIDIA)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 1E2F3A4B    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_008    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-K8L9M0N    • MAC地址: DE-F0-12-34-56-78    • 屏幕分辨率: 1881x1121    • 可用区域: 1881x1081   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: C1D2E3F4    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: 3g    • 电池API支持: True    • 电池电量: 0.31    • 电池充电状态: False    • 性能API支持: True  ================================
2025-08-04 21:52:14 线程1：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 20
   • 设备内存: 12 GB
   • 平台信息: Win32
   • Do Not Track: disabled

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 3070 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (Intel Technologies)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 7A8B9C0D
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_004
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-M0N1O2P
   • MAC地址: E4-42-A6-5D-13-98
   • 屏幕分辨率: 1795x1081
   • 可用区域: 1795x1041

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: E5F6A7B8
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: 5g
   • 电池API支持: True
   • 电池电量: 0.46
   • 电池充电状态: True
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-04 21:52:14 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 20    • 设备内存: 12 GB    • 平台信息: Win32    • Do Not Track: disabled   图形渲染信息:    • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 3070 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (Intel Technologies)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 7A8B9C0D    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_004    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-M0N1O2P    • MAC地址: E4-42-A6-5D-13-98    • 屏幕分辨率: 1795x1081    • 可用区域: 1795x1041   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: E5F6A7B8    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: 5g    • 电池API支持: True    • 电池电量: 0.46    • 电池充电状态: True    • 性能API支持: True  ================================
2025-08-04 21:52:15 线程3：[信息] [信息] 已设置浏览器标题: 线程3 - AWS注册工具 (进度: 5%)
2025-08-04 21:52:15 线程3：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-04 21:52:15 线程3：[信息] 浏览器启动成功
2025-08-04 21:52:15 线程3：[信息] 获取下一个数据: <EMAIL>
2025-08-04 21:52:15 线程3：[信息] 开始处理账户: <EMAIL>
2025-08-04 21:52:15 线程3：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-04 21:52:15 线程3：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-04 21:52:15 线程3：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-04 21:52:15 线程3：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-04 21:52:15 线程3：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-04 21:52:16 线程2：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 20
   • 设备内存: 12 GB
   • 平台信息: Win32
   • Do Not Track: system

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (AMD Radeon RX 6700 XT Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (Intel Technologies)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 5A6B7C8D
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_004
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-Y9Z0A1B
   • MAC地址: 34-56-78-9A-BC-DE
   • 屏幕分辨率: 1953x1062
   • 可用区域: 1953x1022

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: C3D4E5F6
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: cellular
   • 电池API支持: True
   • 电池电量: 0.54
   • 电池充电状态: True
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-04 21:52:16 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 20    • 设备内存: 12 GB    • 平台信息: Win32    • Do Not Track: system   图形渲染信息:    • WebGL渲染器: ANGLE (AMD Radeon RX 6700 XT Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (Intel Technologies)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 5A6B7C8D    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_004    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-Y9Z0A1B    • MAC地址: 34-56-78-9A-BC-DE    • 屏幕分辨率: 1953x1062    • 可用区域: 1953x1022   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: C3D4E5F6    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: cellular    • 电池API支持: True    • 电池电量: 0.54    • 电池充电状态: True    • 性能API支持: True  ================================
2025-08-04 21:52:16 线程1：[信息] [信息] 已设置浏览器标题: 线程1 - AWS注册工具 (进度: 5%)
2025-08-04 21:52:16 线程1：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-04 21:52:16 线程1：[信息] 浏览器启动成功
2025-08-04 21:52:16 线程1：[信息] 获取下一个数据: <EMAIL>
2025-08-04 21:52:16 线程1：[信息] 开始处理账户: <EMAIL>
2025-08-04 21:52:16 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-04 21:52:17 线程1：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-04 21:52:17 线程1：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-04 21:52:17 线程1：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-04 21:52:17 线程1：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-04 21:52:17 线程2：[信息] [信息] 已设置浏览器标题: 线程2 - AWS注册工具 (进度: 5%)
2025-08-04 21:52:17 线程2：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-04 21:52:17 线程2：[信息] 浏览器启动成功
2025-08-04 21:52:17 线程2：[信息] 获取下一个数据: <EMAIL>
2025-08-04 21:52:17 线程2：[信息] 开始处理账户: <EMAIL>
2025-08-04 21:52:17 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-04 21:52:17 线程2：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-04 21:52:17 线程2：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-04 21:52:17 线程2：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-04 21:52:17 线程2：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-04 21:52:17 线程3：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-04 21:52:17 线程1：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-04 21:52:18 线程3：[信息] [信息] 已重新设置浏览器标题: 线程3 - AWS注册工具 (进度: 98%)
2025-08-04 21:52:18 线程3：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-04 21:52:18 线程1：[信息] [信息] 已重新设置浏览器标题: 线程1 - AWS注册工具 (进度: 98%)
2025-08-04 21:52:18 线程1：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-04 21:52:18 线程3：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-04 21:52:18 线程3：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-04 21:52:18 线程3：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-04 21:52:18 线程1：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-04 21:52:18 线程1：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-04 21:52:18 线程1：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-04 21:52:19 线程2：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-04 21:52:19 线程2：[信息] [信息] 已重新设置浏览器标题: 线程2 - AWS注册工具 (进度: 98%)
2025-08-04 21:52:19 线程2：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-04 21:52:19 线程2：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-04 21:52:19 线程2：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-04 21:52:19 线程2：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-04 21:52:48 线程1：[信息] [信息] 注册失败: Timeout 30000ms exceeded.
Call log:
  - navigating to "https://portal.aws.amazon.com/gp/aws/developer/registration/index.html?refid=em_127222&p=free&c=hp&z=1", waiting until "load" (进度: 98%)
2025-08-04 21:52:48 线程1：[信息] [信息] 第一页相关失败，数据保持不动 (进度: 98%)
2025-08-04 21:52:48 [信息] 第一页相关失败，数据保持不动
2025-08-04 21:52:48 线程1：[错误] 账户注册启动失败: <EMAIL>
2025-08-04 21:52:48 线程1：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-04 21:52:48 线程3：[信息] [信息] 注册失败: Timeout 30000ms exceeded.
Call log:
  - navigating to "https://portal.aws.amazon.com/gp/aws/developer/registration/index.html?refid=em_127222&p=free&c=hp&z=1", waiting until "load" (进度: 98%)
2025-08-04 21:52:48 线程3：[信息] [信息] 第一页相关失败，数据保持不动 (进度: 98%)
2025-08-04 21:52:48 [信息] 第一页相关失败，数据保持不动
2025-08-04 21:52:48 线程3：[错误] 账户注册启动失败: <EMAIL>
2025-08-04 21:52:48 线程3：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-04 21:52:49 线程2：[信息] [信息] 注册失败: Timeout 30000ms exceeded.
Call log:
  - navigating to "https://portal.aws.amazon.com/gp/aws/developer/registration/index.html?refid=em_127222&p=free&c=hp&z=1", waiting until "load" (进度: 98%)
2025-08-04 21:52:49 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 21:52:49 [信息] 多线程状态已重置
2025-08-04 21:52:49 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 21:52:49 [信息] 多线程状态已重置
2025-08-04 21:52:49 线程2：[信息] [信息] 第一页相关失败，数据保持不动 (进度: 98%)
2025-08-04 21:52:49 [信息] 第一页相关失败，数据保持不动
2025-08-04 21:52:49 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 21:52:49 [信息] 多线程状态已重置
2025-08-04 21:52:49 线程2：[错误] 账户注册启动失败: <EMAIL>
2025-08-04 21:52:49 线程2：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-04 21:52:56 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 21:52:56 [信息] 多线程状态已重置
2025-08-04 21:52:56 线程1：[信息] [信息]  继续注册被调用，当前状态: Error，当前步骤: 1 (进度: 0%)
2025-08-04 21:52:56 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 21:52:56 [信息] 多线程状态已重置
2025-08-04 21:52:56 线程1：[信息] [信息]  进行智能页面检测... (进度: 0%)
2025-08-04 21:52:56 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 21:52:56 [信息] 多线程状态已重置
2025-08-04 21:52:56 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 0%)
2025-08-04 21:52:56 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 21:52:56 [信息] 多线程状态已重置
2025-08-04 21:52:56 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 0%)
2025-08-04 21:52:56 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 21:52:56 [信息] 多线程状态已重置
2025-08-04 21:52:56 线程2：[信息] [信息]  继续注册被调用，当前状态: Error，当前步骤: 1 (进度: 0%)
2025-08-04 21:52:56 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 21:52:56 [信息] 多线程状态已重置
2025-08-04 21:52:56 线程2：[信息] [信息]  进行智能页面检测... (进度: 0%)
2025-08-04 21:52:56 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 21:52:56 [信息] 多线程状态已重置
2025-08-04 21:52:56 线程2：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 0%)
2025-08-04 21:52:56 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 21:52:56 [信息] 多线程状态已重置
2025-08-04 21:52:56 线程2：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 0%)
2025-08-04 21:52:56 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 21:52:56 [信息] 多线程状态已重置
2025-08-04 21:52:56 线程3：[信息] [信息]  继续注册被调用，当前状态: Error，当前步骤: 1 (进度: 0%)
2025-08-04 21:52:56 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 21:52:56 [信息] 多线程状态已重置
2025-08-04 21:52:56 线程3：[信息] [信息]  进行智能页面检测... (进度: 0%)
2025-08-04 21:52:56 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 21:52:56 [信息] 多线程状态已重置
2025-08-04 21:52:56 线程3：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 0%)
2025-08-04 21:52:56 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 21:52:56 [信息] 多线程状态已重置
2025-08-04 21:52:56 线程3：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 0%)
2025-08-04 21:53:00 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 21:53:00 [信息] 多线程状态已重置
2025-08-04 21:53:00 线程1：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 0%)
2025-08-04 21:53:00 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 21:53:00 [信息] 多线程状态已重置
2025-08-04 21:53:00 线程1：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-04 21:53:00 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 21:53:00 [信息] 多线程状态已重置
2025-08-04 21:53:00 线程1：[信息] [信息]  智能检测到当前在第1页 (进度: 100%)
2025-08-04 21:53:00 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 21:53:00 [信息] 多线程状态已重置
2025-08-04 21:53:00 线程1：[信息] [信息] 检测当前页面状态... (进度: 100%)
2025-08-04 21:53:00 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-04 21:53:00 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-04 21:53:00 线程3：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 0%)
2025-08-04 21:53:00 线程2：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 0%)
2025-08-04 21:53:00 线程3：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-04 21:53:00 线程2：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-04 21:53:00 线程3：[信息] [信息]  智能检测到当前在第1页 (进度: 100%)
2025-08-04 21:53:00 线程3：[信息] [信息] 检测当前页面状态... (进度: 100%)
2025-08-04 21:53:00 线程3：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-04 21:53:00 线程3：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-04 21:53:00 线程2：[信息] [信息]  智能检测到当前在第1页 (进度: 100%)
2025-08-04 21:53:00 线程2：[信息] [信息] 检测当前页面状态... (进度: 100%)
2025-08-04 21:53:00 线程2：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-04 21:53:00 线程2：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-04 21:53:00 线程1：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 100%)
2025-08-04 21:53:00 线程1：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-04 21:53:00 线程1：[信息] [信息] 智能检测到当前在第1页，继续执行... (进度: 100%)
2025-08-04 21:53:00 线程1：[信息] [信息] 🔍 等待第一页加载完成... (进度: 100%)
2025-08-04 21:53:00 线程3：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 100%)
2025-08-04 21:53:00 线程3：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-04 21:53:00 线程3：[信息] [信息] 智能检测到当前在第1页，继续执行... (进度: 100%)
2025-08-04 21:53:00 线程3：[信息] [信息] 🔍 等待第一页加载完成... (进度: 100%)
2025-08-04 21:53:00 线程2：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 100%)
2025-08-04 21:53:00 线程2：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-04 21:53:00 线程2：[信息] [信息] 智能检测到当前在第1页，继续执行... (进度: 100%)
2025-08-04 21:53:00 线程2：[信息] [信息] 🔍 等待第一页加载完成... (进度: 100%)
2025-08-04 21:53:00 线程1：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 100%)
2025-08-04 21:53:00 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-04 21:53:00 线程2：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 100%)
2025-08-04 21:53:00 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-04 21:53:00 线程3：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 100%)
2025-08-04 21:53:00 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-04 21:53:01 线程3：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 100%)
2025-08-04 21:53:01 线程2：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 100%)
2025-08-04 21:53:01 线程1：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 100%)
2025-08-04 21:53:04 线程2：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 100%)
2025-08-04 21:53:04 线程3：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 100%)
2025-08-04 21:53:04 线程3：[信息] [信息] ⚠️ 检测到错误信息，开始重试机制... (进度: 100%)
2025-08-04 21:53:04 [信息] 检测到错误信息，开始重试机制
2025-08-04 21:53:04 线程3：[信息] [信息] 🔄 第1次重试点击验证邮箱按钮... (进度: 100%)
2025-08-04 21:53:04 [信息] 第1次重试点击验证邮箱按钮
2025-08-04 21:53:04 线程2：[信息] [信息] ⚠️ 检测到错误信息，开始重试机制... (进度: 100%)
2025-08-04 21:53:04 [信息] 检测到错误信息，开始重试机制
2025-08-04 21:53:04 线程2：[信息] [信息] 🔄 第1次重试点击验证邮箱按钮... (进度: 100%)
2025-08-04 21:53:04 [信息] 第1次重试点击验证邮箱按钮
2025-08-04 21:53:04 线程1：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 100%)
2025-08-04 21:53:04 线程1：[信息] [信息] ⚠️ 检测到错误信息，开始重试机制... (进度: 100%)
2025-08-04 21:53:04 [信息] 检测到错误信息，开始重试机制
2025-08-04 21:53:04 线程1：[信息] [信息] 🔄 第1次重试点击验证邮箱按钮... (进度: 100%)
2025-08-04 21:53:04 [信息] 第1次重试点击验证邮箱按钮
2025-08-04 21:53:06 线程3：[信息] [信息] ✅ 第1次重试：已点击验证邮箱按钮 (进度: 100%)
2025-08-04 21:53:06 [信息] 第1次重试：已点击验证邮箱按钮
2025-08-04 21:53:06 线程2：[信息] [信息] ✅ 第1次重试：已点击验证邮箱按钮 (进度: 100%)
2025-08-04 21:53:06 [信息] 第1次重试：已点击验证邮箱按钮
2025-08-04 21:53:06 线程1：[信息] [信息] ✅ 第1次重试：已点击验证邮箱按钮 (进度: 100%)
2025-08-04 21:53:06 [信息] 第1次重试：已点击验证邮箱按钮
2025-08-04 21:53:08 线程2：[信息] [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']" (进度: 100%)
2025-08-04 21:53:08 [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']"
2025-08-04 21:53:08 线程3：[信息] [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']" (进度: 100%)
2025-08-04 21:53:08 [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']"
2025-08-04 21:53:08 线程2：[信息] [信息] ✅ 第1次重试成功：已到达第二页，继续正常流程 (进度: 100%)
2025-08-04 21:53:08 [信息] 第1次重试成功：已到达第二页
2025-08-04 21:53:08 线程3：[信息] [信息] ✅ 第1次重试成功：已到达第二页，继续正常流程 (进度: 100%)
2025-08-04 21:53:08 [信息] 第1次重试成功：已到达第二页
2025-08-04 21:53:08 线程2：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-04 21:53:08 线程2：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-04 21:53:08 线程3：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-04 21:53:08 线程3：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-04 21:53:08 线程2：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 21:53:08 线程2：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-04 21:53:08 线程3：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 21:53:08 线程3：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-04 21:53:08 线程1：[信息] [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']" (进度: 100%)
2025-08-04 21:53:08 [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']"
2025-08-04 21:53:08 线程1：[信息] [信息] ✅ 第1次重试成功：已到达第二页，继续正常流程 (进度: 100%)
2025-08-04 21:53:08 [信息] 第1次重试成功：已到达第二页
2025-08-04 21:53:08 线程1：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-04 21:53:08 线程1：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-04 21:53:08 线程1：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 21:53:08 线程1：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-04 21:53:10 线程3：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-04 21:53:10 线程2：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-04 21:53:10 线程1：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-04 21:53:10 线程3：[信息] [信息] 🔍 第2次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 21:53:10 线程3：[信息] [信息] ✅ 第2次检测发现图形验证码！ (进度: 100%)
2025-08-04 21:53:10 线程3：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-08-04 21:53:10 线程3：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-08-04 21:53:10 线程3：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-08-04 21:53:10 线程3：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 21:53:10 线程2：[信息] [信息] 🔍 第2次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 21:53:10 线程2：[信息] [信息] ✅ 第2次检测发现图形验证码！ (进度: 100%)
2025-08-04 21:53:10 线程2：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-08-04 21:53:10 线程2：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-08-04 21:53:10 线程2：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-08-04 21:53:10 线程2：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 21:53:10 线程1：[信息] [信息] 🔍 第2次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 21:53:10 线程1：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 未发现验证码 (进度: 100%)
2025-08-04 21:53:10 线程1：[信息] [信息] ✅ 第一页未检测到图形验证码，继续流程 (进度: 100%)
2025-08-04 21:53:10 线程1：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-04 21:53:10 线程1：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-04 21:53:10 线程1：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-04 21:53:10 线程1：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-04 21:53:10 线程1：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-04 21:53:10 线程1：[信息] 已继续
2025-08-04 21:53:10 [信息] 线程1已继续
2025-08-04 21:53:10 [信息] [线程1] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-04 21:53:10 [信息] [线程1] 等待2秒后开始第一次触发...
2025-08-04 21:53:12 [信息] [线程1] 第1次触发邮箱验证码获取...（最多20次）
2025-08-04 21:53:12 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 21:53:12
2025-08-04 21:53:15 [信息] [线程1] 第2次触发邮箱验证码获取...（最多20次）
2025-08-04 21:53:15 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 21:53:15
2025-08-04 21:53:16 线程3：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 35029 字节 (进度: 100%)
2025-08-04 21:53:16 线程3：[信息] [信息] ✅ 图片验证通过：201x71px，35029字节，复杂度符合要求 (进度: 100%)
2025-08-04 21:53:16 线程3：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 21:53:18 [信息] [线程1] 第3次触发邮箱验证码获取...（最多20次）
2025-08-04 21:53:18 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 21:53:18
2025-08-04 21:53:20 线程3：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"d3md47"},"taskId":"6115710c-713a-11f0-ae6e-62c5329370b7"} (进度: 100%)
2025-08-04 21:53:20 线程3：[信息] [信息] 第一页第1次识别结果: d3md47 → 转换为小写: d3md47 (进度: 100%)
2025-08-04 21:53:20 线程3：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 21:53:20 线程3：[信息] [信息] 已填入验证码: d3md47 (进度: 100%)
2025-08-04 21:53:20 线程3：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 21:53:21 线程2：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 35771 字节 (进度: 100%)
2025-08-04 21:53:21 线程2：[信息] [信息] ✅ 图片验证通过：201x71px，35771字节，复杂度符合要求 (进度: 100%)
2025-08-04 21:53:21 线程2：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 21:53:21 [信息] [线程1] 第4次触发邮箱验证码获取...（最多20次）
2025-08-04 21:53:21 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 21:53:21
2025-08-04 21:53:22 线程3：[信息] [信息] 第一页第1次图形验证码识别结果错误，等待新验证码 (进度: 100%)
2025-08-04 21:53:22 线程3：[信息] [信息] 第一页第2次失败，等待新验证码... (进度: 100%)
2025-08-04 21:53:23 线程2：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"wrrbrz"},"taskId":"62f46208-713a-11f0-9768-ba03bdd70631"} (进度: 100%)
2025-08-04 21:53:23 线程2：[信息] [信息] 第一页第1次识别结果: wrrbrz → 转换为小写: wrrbrz (进度: 100%)
2025-08-04 21:53:23 线程2：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 21:53:23 线程2：[信息] [信息] 已填入验证码: wrrbrz (进度: 100%)
2025-08-04 21:53:23 线程2：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 21:53:24 线程3：[信息] [信息] 第一页第2次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 21:53:24 [信息] [线程1] 第5次触发邮箱验证码获取...（最多20次）
2025-08-04 21:53:24 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 21:53:24
2025-08-04 21:53:25 线程2：[信息] [信息] 第一页第1次图形验证码识别成功 (进度: 100%)
2025-08-04 21:53:25 线程2：[信息] [信息] 第一页图形验证码自动完成 (进度: 100%)
2025-08-04 21:53:25 线程2：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-04 21:53:25 线程2：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-04 21:53:25 线程2：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-04 21:53:25 线程2：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-04 21:53:25 线程2：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-04 21:53:25 线程2：[信息] 已继续
2025-08-04 21:53:25 [信息] 线程2已继续
2025-08-04 21:53:25 [信息] [线程2] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-04 21:53:25 [信息] [线程2] 已删除旧的响应文件
2025-08-04 21:53:25 [信息] [线程2] 等待2秒后开始第一次触发...
2025-08-04 21:53:27 [信息] [线程2] 第1次触发邮箱验证码获取...（最多20次）
2025-08-04 21:53:27 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-04 21:53:27
2025-08-04 21:53:27 线程3：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 31134 字节 (进度: 100%)
2025-08-04 21:53:27 线程3：[信息] [信息] ✅ 图片验证通过：200x71px，31134字节，复杂度符合要求 (进度: 100%)
2025-08-04 21:53:27 线程3：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 21:53:27 [信息] [线程1] 第6次触发邮箱验证码获取...（最多20次）
2025-08-04 21:53:27 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 21:53:27
2025-08-04 21:53:30 线程3：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"28fw223"},"taskId":"6766fc60-713a-11f0-9ab0-ba03bdd70631"} (进度: 100%)
2025-08-04 21:53:30 线程3：[信息] [信息] 第一页第2次识别结果: 28fw223 → 转换为小写: 28fw223 (进度: 100%)
2025-08-04 21:53:30 线程3：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 21:53:30 [信息] [线程2] 第2次触发邮箱验证码获取...（最多20次）
2025-08-04 21:53:33 [信息] [线程1] 第7次触发邮箱验证码获取...（最多20次）
2025-08-04 21:53:33 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 21:53:33
2025-08-04 21:53:33 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-04 21:53:30
2025-08-04 21:53:33 线程3：[信息] [信息] 已填入验证码: 28fw223 (进度: 100%)
2025-08-04 21:53:33 线程3：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 21:53:34 [信息] [线程2] 第3次触发邮箱验证码获取...（最多20次）
2025-08-04 21:53:34 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-04 21:53:34
2025-08-04 21:53:35 线程3：[信息] [信息] 第一页第2次图形验证码识别结果错误，等待新验证码 (进度: 100%)
2025-08-04 21:53:35 线程3：[信息] [信息] 第一页第3次失败，等待新验证码... (进度: 100%)
2025-08-04 21:53:35 [信息] [线程2] 邮箱验证码获取成功: 555685，立即停止重复请求
2025-08-04 21:53:35 [信息] [线程2] 已清理请求文件，停止重复触发
2025-08-04 21:53:35 [信息] [线程2] 已清理响应文件
2025-08-04 21:53:35 线程2：[信息] [信息] 验证码获取成功: 555685，正在自动填入... (进度: 100%)
2025-08-04 21:53:35 线程2：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 100%)
2025-08-04 21:53:35 线程2：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 100%)
2025-08-04 21:53:35 线程2：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 100%)
2025-08-04 21:53:35 [信息] 线程2完成第二页事件已处理
2025-08-04 21:53:35 [信息] 线程2完成第二页，开始批量获取手机号码...
2025-08-04 21:53:35 线程2：[信息] [信息] 线程2第二页验证完成，已通知管理器 (进度: 100%)
2025-08-04 21:53:35 [信息] 开始批量获取3个手机号码，服务商: Durian
2025-08-04 21:53:35 [信息] [手机API] 批量获取3个手机号码，URL: https://api.durianrcs.com/out/ext_api/getMobile?name=oneone&ApiKey=NnlaR01xQm9hMlAwRnJDSVB2SG1kQT09&cuy=mx&pid=0209&num=3&noblack=0&serial=2&secret_key=null&vip=null
2025-08-04 21:53:36 [信息] [线程1] 第8次触发邮箱验证码获取...（最多20次）
2025-08-04 21:53:36 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 21:53:36
2025-08-04 21:53:36 [信息] [手机API] 批量获取响应内容: {"code":200,"msg":"Success","data":["+527841350506","+526531074846","+526642804884"]}
2025-08-04 21:53:36 [信息] [手机API] 检测到数组格式，元素数量: 3
2025-08-04 21:53:36 [信息] [手机API] 批量获取成功，获得3个手机号码
2025-08-04 21:53:36 [信息] 线程1分配榴莲手机号码: +527841350506
2025-08-04 21:53:36 [信息] 线程2分配榴莲手机号码: +526531074846
2025-08-04 21:53:36 [信息] 线程3分配榴莲手机号码: +526642804884
2025-08-04 21:53:36 [信息] 榴莲API批量获取手机号码成功，已分配给3个线程
2025-08-04 21:53:36 [信息] 批量获取3个手机号码成功
2025-08-04 21:53:37 线程3：[信息] [信息] 第一页第3次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 21:53:38 线程2：[信息] [信息] 等待密码设置页面加载... (进度: 100%)
2025-08-04 21:53:38 线程2：[信息] [信息] 开始填写密码信息... (进度: 100%)
2025-08-04 21:53:39 [信息] [线程1] 第9次触发邮箱验证码获取...（最多20次）
2025-08-04 21:53:39 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 21:53:39
2025-08-04 21:53:39 线程2：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 100%)
2025-08-04 21:53:39 线程2：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 100%)
2025-08-04 21:53:39 线程2：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 100%)
2025-08-04 21:53:41 线程3：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 31178 字节 (进度: 100%)
2025-08-04 21:53:41 线程3：[信息] [信息] ✅ 图片验证通过：200x71px，31178字节，复杂度符合要求 (进度: 100%)
2025-08-04 21:53:41 线程3：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 21:53:41 线程2：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 100%)
2025-08-04 21:53:42 [信息] [线程1] 第10次触发邮箱验证码获取...（最多20次）
2025-08-04 21:53:42 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 21:53:42
2025-08-04 21:53:44 线程2：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 100%)
2025-08-04 21:53:44 线程2：[信息] [信息] 等待账户类型确认页面加载... (进度: 100%)
2025-08-04 21:53:44 线程2：[信息] [信息] 开始处理账户类型确认... (进度: 100%)
2025-08-04 21:53:45 [信息] [线程1] 第11次触发邮箱验证码获取...（最多20次）
2025-08-04 21:53:45 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 21:53:45
2025-08-04 21:53:48 [信息] [线程1] 第12次触发邮箱验证码获取...（最多20次）
2025-08-04 21:53:48 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 21:53:48
2025-08-04 21:53:49 线程3：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"7p6g2tz"},"taskId":"72891fce-713a-11f0-ae6e-62c5329370b7"} (进度: 100%)
2025-08-04 21:53:49 线程3：[信息] [信息] 第一页第3次识别结果: 7p6g2tz → 转换为小写: 7p6g2tz (进度: 100%)
2025-08-04 21:53:49 线程3：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 21:53:50 线程3：[信息] [信息] 已填入验证码: 7p6g2tz (进度: 100%)
2025-08-04 21:53:50 线程3：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 21:53:51 [信息] [线程1] 第13次触发邮箱验证码获取...（最多20次）
2025-08-04 21:53:51 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 21:53:51
2025-08-04 21:53:52 线程3：[信息] [信息] 第一页第3次图形验证码识别结果错误，等待新验证码 (进度: 100%)
2025-08-04 21:53:52 线程3：[信息] [信息] 当前验证码抓取出错，需要手动完成图形验证码，并点击继续注册 (进度: 100%)
2025-08-04 21:53:55 [信息] [线程1] 第14次触发邮箱验证码获取...（最多20次）
2025-08-04 21:53:55 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 21:53:55
2025-08-04 21:53:58 [信息] [线程1] 第15次触发邮箱验证码获取...（最多20次）
2025-08-04 21:53:58 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 21:53:58
2025-08-04 21:53:58 线程2：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 100%)
2025-08-04 21:53:58 线程2：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 100%)
2025-08-04 21:54:01 [信息] [线程1] 第16次触发邮箱验证码获取...（最多20次）
2025-08-04 21:54:01 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 21:54:01
2025-08-04 21:54:03 线程2：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 100%)
2025-08-04 21:54:03 [信息] 线程2获取已分配的榴莲手机号码: +526531074846
2025-08-04 21:54:03 线程2：[信息] [信息] 多线程模式：使用已分配的手机号码 +526531074846 (进度: 100%)
2025-08-04 21:54:04 [信息] [线程1] 第17次触发邮箱验证码获取...（最多20次）
2025-08-04 21:54:04 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 21:54:04
2025-08-04 21:54:04 线程2：[信息] [信息] 数据国家代码为CL，需要选择Chile (进度: 100%)
2025-08-04 21:54:05 [信息] [线程1] 邮箱验证码获取成功: 122124，立即停止重复请求
2025-08-04 21:54:05 线程2：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 100%)
2025-08-04 21:54:05 [信息] [线程1] 已清理请求文件，停止重复触发
2025-08-04 21:54:05 [信息] [线程1] 已清理响应文件
2025-08-04 21:54:05 线程1：[信息] [信息] 验证码获取成功: 122124，正在自动填入... (进度: 100%)
2025-08-04 21:54:05 线程1：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 100%)
2025-08-04 21:54:07 线程1：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 100%)
2025-08-04 21:54:07 线程1：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 100%)
2025-08-04 21:54:07 [信息] 线程1完成第二页事件已处理
2025-08-04 21:54:07 [信息] 线程1完成第二页，手机号码已获取，无需重复获取
2025-08-04 21:54:07 线程1：[信息] [信息] 线程1第二页验证完成，已通知管理器 (进度: 100%)
2025-08-04 21:54:07 线程2：[信息] [信息] 已选择国家: Chile (进度: 100%)
2025-08-04 21:54:07 线程2：[信息] [信息] 已成功选择国家: Chile (进度: 100%)
2025-08-04 21:54:07 线程2：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 100%)
2025-08-04 21:54:08 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-04 21:54:10 线程1：[信息] [信息] 等待密码设置页面加载... (进度: 100%)
2025-08-04 21:54:10 线程1：[信息] [信息] 开始填写密码信息... (进度: 100%)
2025-08-04 21:54:10 线程1：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 100%)
2025-08-04 21:54:10 线程1：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 100%)
2025-08-04 21:54:10 线程1：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 100%)
2025-08-04 21:54:11 线程3：[信息] [信息] 检测到第一页验证码已完成，继续流程 (进度: 100%)
2025-08-04 21:54:11 线程3：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-04 21:54:11 线程3：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-04 21:54:11 线程3：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-04 21:54:11 线程3：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-04 21:54:11 线程3：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-04 21:54:11 线程3：[信息] 已继续
2025-08-04 21:54:11 [信息] 线程3已继续
2025-08-04 21:54:11 [信息] 继续了 3 个可继续的线程
2025-08-04 21:54:11 [信息] [线程3] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-04 21:54:11 [信息] [线程3] 等待2秒后开始第一次触发...
2025-08-04 21:54:12 线程1：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 100%)
2025-08-04 21:54:12 线程2：[信息] [信息] 已选择国家代码 +52 (进度: 100%)
2025-08-04 21:54:12 线程2：[信息] [信息] 等待后台获取的手机号码结果... (进度: 100%)
2025-08-04 21:54:12 线程2：[信息] [信息] 已自动获取并填入手机号码: +526531074846 (进度: 100%)
2025-08-04 21:54:13 线程2：[信息] [信息] 使用已获取的手机号码: +526531074846（保存本地号码: +526531074846） (进度: 100%)
2025-08-04 21:54:13 [信息] [线程3] 第1次触发邮箱验证码获取...（最多20次）
2025-08-04 21:54:13 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-04 21:54:13
2025-08-04 21:54:13 线程2：[信息] [信息] 联系信息完成，等待页面加载... (进度: 100%)
2025-08-04 21:54:15 线程1：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 100%)
2025-08-04 21:54:15 线程1：[信息] [信息] 等待账户类型确认页面加载... (进度: 100%)
2025-08-04 21:54:15 线程1：[信息] [信息] 开始处理账户类型确认... (进度: 100%)
2025-08-04 21:54:16 [信息] [线程3] 第2次触发邮箱验证码获取...（最多20次）
2025-08-04 21:54:16 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-04 21:54:16
2025-08-04 21:54:19 [信息] [线程3] 第3次触发邮箱验证码获取...（最多20次）
2025-08-04 21:54:19 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-04 21:54:19
2025-08-04 21:54:21 [信息] [线程3] 邮箱验证码获取成功: 342351，立即停止重复请求
2025-08-04 21:54:21 [信息] [线程3] 已清理请求文件，停止重复触发
2025-08-04 21:54:21 [信息] [线程3] 已清理响应文件
2025-08-04 21:54:21 线程3：[信息] [信息] 验证码获取成功: 342351，正在自动填入... (进度: 100%)
2025-08-04 21:54:21 线程3：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 100%)
2025-08-04 21:54:21 线程3：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 100%)
2025-08-04 21:54:21 线程3：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 100%)
2025-08-04 21:54:21 [信息] 线程3完成第二页事件已处理
2025-08-04 21:54:21 [信息] 线程3完成第二页，手机号码已获取，无需重复获取
2025-08-04 21:54:21 线程3：[信息] [信息] 线程3第二页验证完成，已通知管理器 (进度: 100%)
2025-08-04 21:54:24 线程1：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 100%)
2025-08-04 21:54:24 线程1：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 100%)
2025-08-04 21:54:24 线程3：[信息] [信息] 等待密码设置页面加载... (进度: 100%)
2025-08-04 21:54:24 线程3：[信息] [信息] 开始填写密码信息... (进度: 100%)
2025-08-04 21:54:24 线程3：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 100%)
2025-08-04 21:54:24 线程3：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 100%)
2025-08-04 21:54:24 线程3：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 100%)
2025-08-04 21:54:25 线程3：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 100%)
2025-08-04 21:54:27 线程2：[信息] [信息] 进入付款信息页面... (进度: 100%)
2025-08-04 21:54:28 线程2：[信息] [信息] 正在选择月份: June (进度: 100%)
2025-08-04 21:54:28 线程2：[信息] [信息] 已选择月份（标准选项）: June (进度: 100%)
2025-08-04 21:54:28 线程3：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 100%)
2025-08-04 21:54:28 线程3：[信息] [信息] 等待账户类型确认页面加载... (进度: 100%)
2025-08-04 21:54:28 线程3：[信息] [信息] 开始处理账户类型确认... (进度: 100%)
2025-08-04 21:54:29 线程2：[信息] [信息] 正在选择年份: 2029 (进度: 100%)
2025-08-04 21:54:29 线程2：[信息] [信息] 已选择年份（标准选项）: 2029 (进度: 100%)
2025-08-04 21:54:31 线程2：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 100%)
2025-08-04 21:54:31 线程2：[信息] [信息] 开始填写验证码验证页面... (进度: 100%)
2025-08-04 21:54:31 线程2：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 100%)
2025-08-04 21:54:36 线程3：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 100%)
2025-08-04 21:54:36 线程3：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 100%)
2025-08-04 21:54:39 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-04 21:54:40 线程2：[信息] [信息] 已选择国家代码: +52 (进度: 100%)
2025-08-04 21:54:40 线程2：[信息] [信息] 已清空并重新填写手机号码: +526531074846 (进度: 100%)
2025-08-04 21:54:41 线程2：[信息] [信息] 已点击发送验证码按钮 (进度: 100%)
2025-08-04 21:54:43 线程2：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 100%)
2025-08-04 21:54:43 线程2：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-08-04 21:54:43 线程2：[信息] [信息] 手机号码自动模式 + 图形验证码自动模式：开始自动处理... (进度: 100%)
2025-08-04 21:54:43 线程2：[信息] [信息] 自动模式：开始处理图形验证码... (进度: 100%)
2025-08-04 21:54:43 线程2：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-04 21:54:46 线程2：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-04 21:54:46 线程2：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 21:54:48 线程1：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 100%)
2025-08-04 21:54:48 [信息] 线程1获取已分配的榴莲手机号码: +527841350506
2025-08-04 21:54:48 线程1：[信息] [信息] 多线程模式：使用已分配的手机号码 +527841350506 (进度: 100%)
2025-08-04 21:54:51 线程2：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 35214 字节 (进度: 100%)
2025-08-04 21:54:51 线程2：[信息] [信息] ✅ 图片验证通过：201x70px，35214字节，复杂度符合要求 (进度: 100%)
2025-08-04 21:54:51 线程2：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 21:54:52 线程1：[信息] [信息] 数据国家代码为CL，需要选择Chile (进度: 100%)
2025-08-04 21:54:52 线程1：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 100%)
2025-08-04 21:54:53 线程2：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"2ty5hr"},"taskId":"98bd39be-713a-11f0-8267-9ab6db23b9b7"} (进度: 100%)
2025-08-04 21:54:53 线程2：[信息] [信息] 第六页第1次识别结果: 2ty5hr → 转换为小写: 2ty5hr (进度: 100%)
2025-08-04 21:54:53 线程2：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 21:54:53 线程2：[信息] [信息] 第六页已填入验证码: 2ty5hr (进度: 100%)
2025-08-04 21:54:53 线程2：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 21:54:53 线程1：[信息] [信息] 已选择国家: Chile (进度: 100%)
2025-08-04 21:54:53 线程1：[信息] [信息] 已成功选择国家: Chile (进度: 100%)
2025-08-04 21:54:53 线程1：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 100%)
2025-08-04 21:54:54 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-04 21:54:56 线程1：[信息] [信息] 已选择国家代码 +52 (进度: 100%)
2025-08-04 21:54:56 线程2：[信息] [信息] 第1次图形验证码识别成功 (进度: 100%)
2025-08-04 21:54:56 线程2：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-08-04 21:54:56 线程1：[信息] [信息] 等待后台获取的手机号码结果... (进度: 100%)
2025-08-04 21:54:56 线程1：[信息] [信息] 已自动获取并填入手机号码: +527841350506 (进度: 100%)
2025-08-04 21:54:57 线程1：[信息] [信息] 使用已获取的手机号码: +527841350506（保存本地号码: +527841350506） (进度: 100%)
2025-08-04 21:54:58 线程1：[信息] [信息] 联系信息完成，等待页面加载... (进度: 100%)
2025-08-04 21:54:59 线程2：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-08-04 21:54:59 线程3：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 100%)
2025-08-04 21:54:59 [信息] 线程3获取已分配的榴莲手机号码: +526642804884
2025-08-04 21:54:59 线程3：[信息] [信息] 多线程模式：使用已分配的手机号码 +526642804884 (进度: 100%)
2025-08-04 21:55:00 线程3：[信息] [信息] 数据国家代码为CL，需要选择Chile (进度: 100%)
2025-08-04 21:55:00 线程3：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 100%)
2025-08-04 21:55:01 线程1：[信息] [信息] 进入付款信息页面... (进度: 100%)
2025-08-04 21:55:02 线程1：[信息] [信息] 正在选择月份: October (进度: 100%)
2025-08-04 21:55:02 线程3：[信息] [信息] 已选择国家: Chile (进度: 100%)
2025-08-04 21:55:02 线程3：[信息] [信息] 已成功选择国家: Chile (进度: 100%)
2025-08-04 21:55:02 线程3：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 100%)
2025-08-04 21:55:02 线程1：[信息] [信息] 已选择月份（标准选项）: October (进度: 100%)
2025-08-04 21:55:02 线程3：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-04 21:55:02 线程2：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-04 21:55:02 线程2：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 21:55:02 线程2：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 21:55:02 线程2：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-08-04 21:55:03 线程1：[信息] [信息] 正在选择年份: 2028 (进度: 100%)
2025-08-04 21:55:03 线程1：[信息] [信息] 已选择年份（标准选项）: 2028 (进度: 100%)
2025-08-04 21:55:03 线程1：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 100%)
2025-08-04 21:55:03 线程1：[信息] [信息] 开始填写验证码验证页面... (进度: 100%)
2025-08-04 21:55:03 线程1：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 100%)
2025-08-04 21:55:05 线程3：[信息] [信息] 已选择国家代码 +52 (进度: 100%)
2025-08-04 21:55:07 线程3：[信息] [信息] 等待后台获取的手机号码结果... (进度: 100%)
2025-08-04 21:55:07 线程3：[信息] [信息] 已自动获取并填入手机号码: +526642804884 (进度: 100%)
2025-08-04 21:55:07 线程2：[信息] [信息] 线程2开始独立获取验证码... (进度: 100%)
2025-08-04 21:55:07 线程2：[信息] [信息] 线程2开始获取验证码，最多尝试8次，每次间隔8秒 (进度: 100%)
2025-08-04 21:55:07 线程2：[信息] [信息] 线程2第1次尝试获取验证码...（剩余7次尝试） (进度: 100%)
2025-08-04 21:55:07 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-04 21:55:08 线程3：[信息] [信息] 使用已获取的手机号码: +526642804884（保存本地号码: +526642804884） (进度: 100%)
2025-08-04 21:55:08 线程3：[信息] [信息] 联系信息完成，等待页面加载... (进度: 100%)
2025-08-04 21:55:08 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-04 21:55:08 线程2：[信息] [信息] 线程2第1次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-04 21:55:08 线程2：[信息] [信息] 线程2等待8秒后进行下一次尝试... (进度: 100%)
2025-08-04 21:55:15 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-04 21:55:16 线程3：[信息] [信息] 进入付款信息页面... (进度: 100%)
2025-08-04 21:55:16 线程1：[信息] [信息] 已选择国家代码: +52 (进度: 100%)
2025-08-04 21:55:16 线程2：[信息] [信息] 线程2第2次尝试获取验证码...（剩余6次尝试） (进度: 100%)
2025-08-04 21:55:16 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-04 21:55:16 线程1：[信息] [信息] 已清空并重新填写手机号码: +527841350506 (进度: 100%)
2025-08-04 21:55:16 线程1：[信息] [信息] 已点击发送验证码按钮 (进度: 100%)
2025-08-04 21:55:17 线程2：[信息] [信息] 线程2验证码获取成功: 9199 (进度: 100%)
2025-08-04 21:55:17 [信息] 线程2手机号码已加入释放队列: +526531074846 (原因: 获取验证码成功)
2025-08-04 21:55:17 线程2：[信息] [信息] 线程2验证码获取成功: 9199，立即填入验证码... (进度: 100%)
2025-08-04 21:55:17 线程2：[信息] [信息] 第七页：找到验证码输入框 (Verify code) (进度: 100%)
2025-08-04 21:55:17 线程2：[信息] [信息] 线程2已自动填入手机验证码: 9199 (进度: 100%)
2025-08-04 21:55:17 线程3：[信息] [信息] 正在选择月份: August (进度: 100%)
2025-08-04 21:55:17 线程3：[信息] [信息] 已选择月份（标准选项）: August (进度: 100%)
2025-08-04 21:55:18 线程3：[信息] [信息] 正在选择年份: 2029 (进度: 100%)
2025-08-04 21:55:18 线程2：[信息] [信息] 线程2正在自动点击Continue按钮... (进度: 100%)
2025-08-04 21:55:18 线程2：[信息] [信息] 线程2手机验证码验证完成，继续执行后续步骤... (进度: 100%)
2025-08-04 21:55:18 线程3：[信息] [信息] 已选择年份（标准选项）: 2029 (进度: 100%)
2025-08-04 21:55:18 线程1：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 100%)
2025-08-04 21:55:18 线程1：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-08-04 21:55:18 线程1：[信息] [信息] 手机号码自动模式 + 图形验证码自动模式：开始自动处理... (进度: 100%)
2025-08-04 21:55:18 线程1：[信息] [信息] 自动模式：开始处理图形验证码... (进度: 100%)
2025-08-04 21:55:18 线程1：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-04 21:55:18 线程3：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 100%)
2025-08-04 21:55:18 线程3：[信息] [信息] 开始填写验证码验证页面... (进度: 100%)
2025-08-04 21:55:18 线程3：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 100%)
2025-08-04 21:55:21 线程2：[信息] [信息] 验证码验证完成，等待页面加载... (进度: 100%)
2025-08-04 21:55:21 线程1：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-04 21:55:21 线程1：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 21:55:22 线程2：[信息] [信息] 已点击完成注册按钮，正在处理... (进度: 100%)
2025-08-04 21:55:22 线程2：[信息] [信息] 自动模式验证完成，API手机号码已加入黑名单: 加入黑名单成功 (进度: 100%)
2025-08-04 21:55:23 线程2：[信息] [信息] 等待完成注册页面加载... (进度: 100%)
2025-08-04 21:55:26 线程2：[信息] [信息] 完成注册页面加载完成 (进度: 100%)
2025-08-04 21:55:26 线程2：[信息] [信息] 🔍 正在查找'Go to the AWS Management Console'链接... (进度: 100%)
2025-08-04 21:55:26 线程3：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-04 21:55:26 线程1：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 34784 字节 (进度: 100%)
2025-08-04 21:55:26 线程1：[信息] [信息] ✅ 图片验证通过：201x70px，34784字节，复杂度符合要求 (进度: 100%)
2025-08-04 21:55:26 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 21:55:27 线程3：[信息] [信息] 已选择国家代码: +52 (进度: 100%)
2025-08-04 21:55:27 线程3：[信息] [信息] 已清空并重新填写手机号码: +526642804884 (进度: 100%)
2025-08-04 21:55:27 线程3：[信息] [信息] 已点击发送验证码按钮 (进度: 100%)
2025-08-04 21:55:28 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"p4szh3"},"taskId":"adc2dde6-713a-11f0-b896-029e46bc98e3"} (进度: 100%)
2025-08-04 21:55:28 线程1：[信息] [信息] 第六页第1次识别结果: p4szh3 → 转换为小写: p4szh3 (进度: 100%)
2025-08-04 21:55:28 线程1：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 21:55:28 线程1：[信息] [信息] 第六页已填入验证码: p4szh3 (进度: 100%)
2025-08-04 21:55:29 线程1：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 21:55:29 线程3：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 100%)
2025-08-04 21:55:29 线程3：[信息] [信息] ⚠️ 检测到错误信息，开始重试机制... (进度: 100%)
2025-08-04 21:55:29 [信息] 检测到错误信息，开始重试机制
2025-08-04 21:55:29 线程3：[信息] [信息] 🔄 第1次重试发送验证码按钮... (进度: 100%)
2025-08-04 21:55:29 [信息] 第1次重试发送验证码按钮
2025-08-04 21:55:30 [信息] 定时检查发现1个待释放手机号码，开始批量释放
2025-08-04 21:55:30 [信息] 开始释放1个手机号码
2025-08-04 21:55:30 [信息] [手机API] 开始批量释放1个手机号码
2025-08-04 21:55:30 [信息] [手机API] 释放手机号码: +526531074846
2025-08-04 21:55:31 [信息] [手机API] 手机号码释放成功: +526531074846
2025-08-04 21:55:31 [信息] [手机API] 批量释放完成: 成功1个, 失败0个
2025-08-04 21:55:31 [信息] 定时批量释放完成: 批量释放完成: 成功1个, 失败0个
2025-08-04 21:55:32 线程1：[信息] [信息] 第1次图形验证码识别成功 (进度: 100%)
2025-08-04 21:55:32 线程1：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-08-04 21:55:32 线程3：[信息] [信息] ✅ 第1次重试：已点击发送验证码按钮 (进度: 100%)
2025-08-04 21:55:32 [信息] 第1次重试：已点击发送验证码按钮
2025-08-04 21:55:34 线程2：[信息] [信息] 🖱️ 已点击跳转管理控制台链接 (进度: 100%)
2025-08-04 21:55:34 线程2：[信息] [信息] 🔗 通过导航进入IAM安全凭证页面... (进度: 100%)
2025-08-04 21:55:34 线程2：[信息] [信息] ⏳ 等待管理控制台页面加载，智能检测'更多'按钮（超时20秒）... (进度: 100%)
2025-08-04 21:55:34 线程3：[信息] [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']" (进度: 100%)
2025-08-04 21:55:34 [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']"
2025-08-04 21:55:35 线程3：[信息] [信息] ✅ 第1次重试成功：错误信息消失，继续正常流程 (进度: 100%)
2025-08-04 21:55:35 [信息] 第1次重试成功：错误信息消失
2025-08-04 21:55:35 线程3：[信息] [信息] 手机号码自动模式 + 图形验证码自动模式：开始自动处理... (进度: 100%)
2025-08-04 21:55:35 线程3：[信息] [信息] 自动模式：开始处理图形验证码... (进度: 100%)
2025-08-04 21:55:35 线程3：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-04 21:55:35 线程1：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-08-04 21:55:38 线程3：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-04 21:55:38 线程3：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 21:55:38 线程1：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-04 21:55:38 线程1：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 21:55:38 线程1：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 21:55:38 线程1：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-08-04 21:55:42 线程3：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 34428 字节 (进度: 100%)
2025-08-04 21:55:42 线程3：[信息] [信息] ✅ 图片验证通过：201x70px，34428字节，复杂度符合要求 (进度: 100%)
2025-08-04 21:55:42 线程3：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 21:55:44 线程1：[信息] [信息] 线程1开始独立获取验证码... (进度: 100%)
2025-08-04 21:55:44 线程1：[信息] [信息] 线程1开始获取验证码，最多尝试8次，每次间隔8秒 (进度: 100%)
2025-08-04 21:55:44 线程1：[信息] [信息] 线程1第1次尝试获取验证码...（剩余7次尝试） (进度: 100%)
2025-08-04 21:55:44 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-04 21:55:45 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-04 21:55:45 线程1：[信息] [信息] 线程1第1次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-04 21:55:45 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-08-04 21:55:48 线程3：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"dwbg3p"},"taskId":"b97cf5d6-713a-11f0-b896-029e46bc98e3"} (进度: 100%)
2025-08-04 21:55:48 线程3：[信息] [信息] 第六页第1次识别结果: dwbg3p → 转换为小写: dwbg3p (进度: 100%)
2025-08-04 21:55:48 线程3：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 21:55:48 线程3：[信息] [信息] 第六页已填入验证码: dwbg3p (进度: 100%)
2025-08-04 21:55:48 线程3：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 21:55:51 线程3：[信息] [信息] 第1次图形验证码识别成功 (进度: 100%)
2025-08-04 21:55:51 线程3：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-08-04 21:55:52 线程2：[信息] [信息] ✅ 找到'更多'按钮，页面加载完成 (进度: 100%)
2025-08-04 21:55:52 线程2：[信息] [信息] 🖱️ 正在点击更多按钮... (进度: 100%)
2025-08-04 21:55:52 线程2：[信息] [信息] ✅ 成功点击更多按钮 (进度: 100%)
2025-08-04 21:55:52 [信息] 成功点击更多按钮
2025-08-04 21:55:53 线程1：[信息] [信息] 线程1第2次尝试获取验证码...（剩余6次尝试） (进度: 100%)
2025-08-04 21:55:53 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-04 21:55:53 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-04 21:55:53 线程1：[信息] [信息] 线程1第2次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-04 21:55:53 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-08-04 21:55:53 线程2：[信息] [信息] 🖱️ 正在点击账户信息按钮... (进度: 100%)
2025-08-04 21:55:53 线程2：[信息] [信息] ✅ 成功点击账户信息按钮 (进度: 100%)
2025-08-04 21:55:53 [信息] 成功点击账户信息按钮
2025-08-04 21:55:54 线程2：[信息] [信息] 🖱️ 正在点击'安全凭证'菜单项... (进度: 100%)
2025-08-04 21:55:54 线程2：[信息] [信息] 🔍 正在定位'安全凭证'链接... (进度: 100%)
2025-08-04 21:55:54 线程2：[信息] [信息] ✅ 成功定位到'安全凭证'链接 (进度: 100%)
2025-08-04 21:55:54 [信息] 成功定位到'安全凭证'链接
2025-08-04 21:55:54 线程3：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-08-04 21:55:57 线程3：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-04 21:55:57 线程3：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 21:55:57 线程3：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 21:55:57 线程3：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-08-04 21:56:01 线程1：[信息] [信息] 线程1第3次尝试获取验证码...（剩余5次尝试） (进度: 100%)
2025-08-04 21:56:01 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-04 21:56:01 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-04 21:56:01 线程1：[信息] [信息] 线程1第3次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-04 21:56:01 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-08-04 21:56:02 线程3：[信息] [信息] 线程3开始独立获取验证码... (进度: 100%)
2025-08-04 21:56:02 线程3：[信息] [信息] 线程3开始获取验证码，最多尝试8次，每次间隔8秒 (进度: 100%)
2025-08-04 21:56:02 线程3：[信息] [信息] 线程3第1次尝试获取验证码...（剩余7次尝试） (进度: 100%)
2025-08-04 21:56:02 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-04 21:56:03 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-04 21:56:03 线程3：[信息] [信息] 线程3第1次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-04 21:56:03 线程3：[信息] [信息] 线程3等待8秒后进行下一次尝试... (进度: 100%)
2025-08-04 21:56:04 线程2：[信息] [信息] ✅ 成功点击'安全凭证'菜单项 (进度: 100%)
2025-08-04 21:56:04 [信息] 成功点击'安全凭证'链接
2025-08-04 21:56:04 线程2：[信息] [信息] ⏳ 等待IAM安全凭证页面加载（20秒超时，循环检测账单问题和创建密钥按钮）... (进度: 100%)
2025-08-04 21:56:09 线程1：[信息] [信息] 线程1第4次尝试获取验证码...（剩余4次尝试） (进度: 100%)
2025-08-04 21:56:09 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-04 21:56:09 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-04 21:56:09 线程1：[信息] [信息] 线程1第4次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-04 21:56:09 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-08-04 21:56:11 线程3：[信息] [信息] 线程3第2次尝试获取验证码...（剩余6次尝试） (进度: 100%)
2025-08-04 21:56:11 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-04 21:56:11 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-04 21:56:11 线程3：[信息] [信息] 线程3第2次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-04 21:56:11 线程3：[信息] [信息] 线程3等待8秒后进行下一次尝试... (进度: 100%)
2025-08-04 21:56:17 线程1：[信息] [信息] 线程1第5次尝试获取验证码...（剩余3次尝试） (进度: 100%)
2025-08-04 21:56:17 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-04 21:56:19 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-04 21:56:19 线程1：[信息] [信息] 线程1第5次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-04 21:56:19 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-08-04 21:56:19 线程3：[信息] [信息] 线程3第3次尝试获取验证码...（剩余5次尝试） (进度: 100%)
2025-08-04 21:56:19 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-04 21:56:19 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-04 21:56:19 线程3：[信息] [信息] 线程3第3次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-04 21:56:19 线程3：[信息] [信息] 线程3等待8秒后进行下一次尝试... (进度: 100%)
2025-08-04 21:56:24 线程2：[信息] [信息] ✅ 找到'创建访问密钥'按钮，页面加载完成 (进度: 100%)
2025-08-04 21:56:24 线程2：[信息] [信息] ✅ 页面状态正常，找到创建密钥按钮，开始密钥提取流程 (进度: 100%)
2025-08-04 21:56:24 线程2：[信息] [信息] 🔍 设置页面缩放为50%... (进度: 100%)
2025-08-04 21:56:25 线程2：[信息] [信息] ✅ 页面缩放设置完成 (进度: 100%)
2025-08-04 21:56:25 [信息] 页面缩放设置为50%完成
2025-08-04 21:56:25 线程2：[信息] [信息] 🔍 正在查找并点击'下一步'按钮关闭向导... (进度: 100%)
2025-08-04 21:56:25 [信息] 开始查找并点击'下一步'按钮关闭向导
2025-08-04 21:56:27 线程3：[信息] [信息] 线程3第4次尝试获取验证码...（剩余4次尝试） (进度: 100%)
2025-08-04 21:56:28 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-04 21:56:28 线程2：[信息] [信息] ✅ 找到可见的下一步按钮，使用选择器: Locator(button[data-testid='awsc-nav-services-tooltip-confirm-button']) (进度: 100%)
2025-08-04 21:56:28 [信息] 找到可见的下一步按钮，使用选择器: Locator(button[data-testid='awsc-nav-services-tooltip-confirm-button'])
2025-08-04 21:56:28 线程1：[信息] [信息] 线程1第6次尝试获取验证码...（剩余2次尝试） (进度: 100%)
2025-08-04 21:56:28 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-04 21:56:28 线程2：[信息] [信息] ✅ 第一次点击'下一步'按钮完成 (选择器: Locator(button[data-testid='awsc-nav-services-tooltip-confirm-button'])) (进度: 100%)
2025-08-04 21:56:28 [信息] 第一次点击'下一步'按钮完成 (选择器: Locator(button[data-testid='awsc-nav-services-tooltip-confirm-button']))
2025-08-04 21:56:28 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-04 21:56:28 线程1：[信息] [信息] 线程1第6次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-04 21:56:28 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-08-04 21:56:28 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-04 21:56:28 线程3：[信息] [信息] 线程3第4次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-04 21:56:28 线程3：[信息] [信息] 线程3等待8秒后进行下一次尝试... (进度: 100%)
2025-08-04 21:56:29 线程2：[信息] [信息] ℹ️ 第二次点击时按钮已消失，向导已关闭 (进度: 100%)
2025-08-04 21:56:29 [信息] 第二次点击时按钮已消失，向导已关闭
2025-08-04 21:56:29 线程2：[信息] [信息] ✅ '下一步'按钮点击流程完成 (进度: 100%)
2025-08-04 21:56:29 [信息] '下一步'按钮点击流程完成
2025-08-04 21:56:29 线程2：[信息] [信息] 🔑 第四阶段：开始创建和复制访问密钥 (进度: 100%)
2025-08-04 21:56:29 [信息] 开始创建和复制访问密钥
2025-08-04 21:56:29 线程2：[信息] [信息] 🔑 正在点击'创建访问密钥'按钮... (进度: 100%)
2025-08-04 21:56:29 线程2：[信息] [信息] 🔍 正在定位'创建访问密钥'按钮... (进度: 100%)
2025-08-04 21:56:29 线程2：[信息] [信息] ✅ 成功定位到'创建访问密钥'按钮 (进度: 100%)
2025-08-04 21:56:29 [信息] 成功定位到'创建访问密钥'按钮
2025-08-04 21:56:29 线程2：[信息] [信息] ✅ 成功点击'创建访问密钥'按钮 (进度: 100%)
2025-08-04 21:56:29 [信息] 成功点击'创建访问密钥'按钮
2025-08-04 21:56:31 线程2：[信息] [信息] ☑️ 正在勾选确认复选框... (进度: 100%)
2025-08-04 21:56:32 线程2：[信息] [信息] ✅ 使用id属性定位到确认复选框 (进度: 100%)
2025-08-04 21:56:32 [信息] 使用id属性定位到确认复选框
2025-08-04 21:56:32 线程2：[信息] [信息] ✅ 成功勾选确认复选框 (进度: 100%)
2025-08-04 21:56:32 [信息] 成功勾选确认复选框
2025-08-04 21:56:33 线程2：[信息] [信息] 🔑 正在点击'创建访问密钥'确认按钮... (进度: 100%)
2025-08-04 21:56:33 线程2：[信息] [信息] ✅ 成功点击'创建访问密钥'确认按钮 (进度: 100%)
2025-08-04 21:56:33 [信息] 成功点击'创建访问密钥'确认按钮
2025-08-04 21:56:36 线程3：[信息] [信息] 线程3第5次尝试获取验证码...（剩余3次尝试） (进度: 100%)
2025-08-04 21:56:36 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-04 21:56:36 线程1：[信息] [信息] 线程1第7次尝试获取验证码...（剩余1次尝试） (进度: 100%)
2025-08-04 21:56:36 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-04 21:56:36 线程2：[信息] [信息] 📋 开始复制访问密钥... (进度: 100%)
2025-08-04 21:56:36 [信息] 开始复制访问密钥
2025-08-04 21:56:36 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-04 21:56:36 线程1：[信息] [信息] 线程1第7次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-04 21:56:36 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-08-04 21:56:37 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-04 21:56:37 线程3：[信息] [信息] 线程3第5次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-04 21:56:37 线程3：[信息] [信息] 线程3等待8秒后进行下一次尝试... (进度: 100%)
2025-08-04 21:56:38 线程2：[信息] [信息] 🔍 方法2找到 2 个单元格 (进度: 100%)
2025-08-04 21:56:38 [信息] 方法2找到 2 个单元格
2025-08-04 21:56:38 线程2：[信息] [信息] 🔍 总共找到 2 个单元格，开始分析... (进度: 100%)
2025-08-04 21:56:38 [信息] 总共找到 2 个单元格，开始分析
2025-08-04 21:56:38 [信息] 单元格[0]: 'AKIASEFYCFRG7LIL74KO'
2025-08-04 21:56:38 [信息] 单元格[1]: '***************Mostrar'
2025-08-04 21:56:38 线程2：[信息] [信息] 📋 正在查找并复制访问密钥... (进度: 100%)
2025-08-04 21:56:38 线程2：[信息] [信息] ✅ 找到访问密钥: AKIASEFYCFRG7LIL74KO (进度: 100%)
2025-08-04 21:56:38 [信息] 找到访问密钥: AKIASEFYCFRG7LIL74KO
2025-08-04 21:56:38 线程2：[信息] [信息] ✅ 方法1成功点击访问密钥复制按钮 (进度: 100%)
2025-08-04 21:56:38 [信息] 方法1成功点击访问密钥复制按钮
2025-08-04 21:56:39 线程2：[信息] [信息] 📋 正在查找并复制秘密访问密钥... (进度: 100%)
2025-08-04 21:56:39 线程2：[信息] [信息] ✅ 找到秘密访问密钥: ***************Mostrar (进度: 100%)
2025-08-04 21:56:39 [信息] 找到秘密访问密钥: ***************Mostrar
2025-08-04 21:56:39 线程2：[信息] [信息] 🔍 检测到隐藏的秘密访问密钥，尝试点击显示按钮... (进度: 100%)
2025-08-04 21:56:39 [信息] 检测到隐藏的秘密访问密钥，尝试点击显示按钮
2025-08-04 21:56:39 线程2：[信息] [信息] ✅ 使用TestId定位到显示按钮 (进度: 100%)
2025-08-04 21:56:39 [信息] 使用TestId定位到显示按钮
2025-08-04 21:56:41 线程2：[信息] [信息] ✅ 显示按钮点击成功，新文本: bMqLZro0hX0EMqKTrIqbD8t5jnwamT7yv0Y23XbsOcultar (进度: 100%)
2025-08-04 21:56:41 [信息] 显示按钮点击成功，新文本: bMqLZro0hX0EMqKTrIqbD8t5jnwamT7yv0Y23XbsOcultar
2025-08-04 21:56:41 线程2：[信息] [信息] ✅ 直接从显示文本提取秘密访问密钥: bMqLZro0hX0EMqKTrIqbD8t5jnwamT7yv0Y23XbsOcultar (进度: 100%)
2025-08-04 21:56:41 [信息] 直接从显示文本提取秘密访问密钥: bMqLZro0hX0EMqKTrIqbD8t5jnwamT7yv0Y23XbsOcultar
2025-08-04 21:56:41 线程2：[信息] [信息] ✅ 访问密钥复制完成 (进度: 100%)
2025-08-04 21:56:41 [信息] 访问密钥复制完成 - AccessKey: AKIASEFYCFRG7LIL74KO, SecretKey: bMqLZro0hX0EMqKTrIqbD8t5jnwamT7yv0Y23XbsOcultar
2025-08-04 21:56:42 线程2：[信息] [信息] 🖱️ 正在点击'已完成'按钮... (进度: 100%)
2025-08-04 21:56:42 线程2：[信息] [信息] ✅ 成功点击'已完成'按钮 (进度: 100%)
2025-08-04 21:56:42 [信息] 成功点击'已完成'按钮
2025-08-04 21:56:42 线程2：[信息] [信息] ✅ 密钥已保存到数据对象 - AccessKey: AKIASEFYCFRG7LIL74KO, SecretKey: bMqLZro0hX0EMqKTrIqbD8t5jnwamT7yv0Y23XbsOcultar (进度: 100%)
2025-08-04 21:56:42 [信息] 密钥已保存到数据对象 - AccessKey: AKIASEFYCFRG7LIL74KO, SecretKey: bMqLZro0hX0EMqKTrIqbD8t5jnwamT7yv0Y23XbsOcultar
2025-08-04 21:56:43 线程2：[信息] [信息] 🖱️ 正在点击'继续'按钮... (进度: 100%)
2025-08-04 21:56:44 线程1：[信息] [信息] 线程1第8次尝试获取验证码...（剩余0次尝试） (进度: 100%)
2025-08-04 21:56:44 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-04 21:56:44 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-04 21:56:44 线程1：[信息] [信息] 线程1第8次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-04 21:56:44 线程1：[信息] [信息] 线程1验证码获取失败，已尝试8次 (进度: 100%)
2025-08-04 21:56:44 [信息] 线程1手机号码已加入释放队列: +527841350506 (原因: 验证码获取失败)
2025-08-04 21:56:44 线程1：[信息] [信息] 线程1验证码获取失败: 获取失败，已尝试8次 (进度: 100%)
2025-08-04 21:56:44 线程1：[信息] [信息] 验证码获取失败，第1次重试... (进度: 100%)
2025-08-04 21:56:44 线程1：[信息] [信息] 正在自动重试第1次，同时加入黑名单和获取新号码... (进度: 100%)
2025-08-04 21:56:44 线程1：[信息] [信息] 正在返回上一页... (进度: 100%)
2025-08-04 21:56:45 线程3：[信息] [信息] 线程3第6次尝试获取验证码...（剩余2次尝试） (进度: 100%)
2025-08-04 21:56:45 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-04 21:56:45 线程1：[信息] [信息] 已点击返回按钮 (进度: 100%)
2025-08-04 21:56:45 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-04 21:56:45 线程3：[信息] [信息] 线程3第6次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-04 21:56:45 线程3：[信息] [信息] 线程3等待8秒后进行下一次尝试... (进度: 100%)
2025-08-04 21:56:45 线程1：[信息] [信息] 超时号码已加入黑名单 (进度: 100%)
2025-08-04 21:56:46 线程2：[信息] [信息] ❌ 点击'继续'按钮失败: Timeout 3000ms exceeded.
Call log:
  - waiting for Locator("button[data-awsui-analytics*='navigate']") to be visible (进度: 100%)
2025-08-04 21:56:46 [信息] 点击'继续'按钮失败: Timeout 3000ms exceeded.
Call log:
  - waiting for Locator("button[data-awsui-analytics*='navigate']") to be visible
2025-08-04 21:56:46 线程2：[信息] [信息] 🔐 开始设置MFA设备... (进度: 100%)
2025-08-04 21:56:46 [信息] 开始设置MFA设备
2025-08-04 21:56:46 线程2：[信息] [信息] 🖱️ 正在点击'分配 MFA 设备'按钮... (进度: 100%)
2025-08-04 21:56:46 线程2：[信息] [信息] ✅ 成功点击'分配 MFA 设备'按钮 (进度: 100%)
2025-08-04 21:56:46 [信息] 成功点击'分配 MFA 设备'按钮
2025-08-04 21:56:46 线程2：[信息] [信息] ⏳ 等待MFA设备设置页面加载（20秒超时）... (进度: 100%)
2025-08-04 21:56:48 线程1：[信息] [信息] 正在选择国家代码: +52 (进度: 100%)
2025-08-04 21:56:50 线程1：[信息] [信息] 已打开国家代码下拉列表 (进度: 100%)
2025-08-04 21:56:51 线程1：[信息] [信息] 已选择国家代码: +52 (进度: 100%) (进度: 100%)
2025-08-04 21:56:51 线程1：[信息] [信息] 后台获取新手机号码... (进度: 100%)
2025-08-04 21:56:51 [信息] [榴莲API] 获取手机号码，尝试 1/4
2025-08-04 21:56:52 线程1：[信息] [信息] 后台获取新手机号码成功: +529361036023，已保存到注册数据 (进度: 100%)
2025-08-04 21:56:52 线程1：[信息] [信息] 正在清空并填入新的手机号码... (进度: 100%)
2025-08-04 21:56:52 线程1：[信息] [信息] 已填入新手机号码: +529361036023 (进度: 100%)
2025-08-04 21:56:52 线程1：[信息] [信息] 正在点击发送短信按钮... (进度: 100%)
2025-08-04 21:56:53 线程3：[信息] [信息] 线程3第7次尝试获取验证码...（剩余1次尝试） (进度: 100%)
2025-08-04 21:56:53 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-04 21:56:53 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-04 21:56:53 线程3：[信息] [信息] 线程3第7次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-04 21:56:53 线程3：[信息] [信息] 线程3等待8秒后进行下一次尝试... (进度: 100%)
2025-08-04 21:56:54 线程1：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 100%)
2025-08-04 21:56:54 线程1：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-08-04 21:56:55 线程1：[信息] [信息] 新手机号码已填入，自动模式：开始处理图形验证码... (进度: 100%)
2025-08-04 21:56:55 线程1：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-04 21:56:58 线程1：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-04 21:56:58 线程1：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 21:57:00 [信息] 定时检查发现1个待释放手机号码，开始批量释放
2025-08-04 21:57:00 [信息] 开始释放1个手机号码
2025-08-04 21:57:00 [信息] [手机API] 开始批量释放1个手机号码
2025-08-04 21:57:00 [信息] [手机API] 释放手机号码: +527841350506
2025-08-04 21:57:00 [信息] [手机API] 手机号码释放成功: +527841350506
2025-08-04 21:57:01 [信息] [手机API] 批量释放完成: 成功1个, 失败0个
2025-08-04 21:57:01 [信息] 定时批量释放完成: 批量释放完成: 成功1个, 失败0个
2025-08-04 21:57:01 线程1：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 35660 字节 (进度: 100%)
2025-08-04 21:57:01 线程1：[信息] [信息] ✅ 图片验证通过：201x70px，35660字节，复杂度符合要求 (进度: 100%)
2025-08-04 21:57:01 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 21:57:01 线程3：[信息] [信息] 线程3第8次尝试获取验证码...（剩余0次尝试） (进度: 100%)
2025-08-04 21:57:01 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-04 21:57:02 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-04 21:57:02 线程3：[信息] [信息] 线程3第8次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-04 21:57:02 线程3：[信息] [信息] 线程3验证码获取失败，已尝试8次 (进度: 100%)
2025-08-04 21:57:02 [信息] 线程3手机号码已加入释放队列: +526642804884 (原因: 验证码获取失败)
2025-08-04 21:57:02 线程3：[信息] [信息] 线程3验证码获取失败: 获取失败，已尝试8次 (进度: 100%)
2025-08-04 21:57:02 线程3：[信息] [信息] 验证码获取失败，第1次重试... (进度: 100%)
2025-08-04 21:57:02 线程3：[信息] [信息] 正在自动重试第1次，同时加入黑名单和获取新号码... (进度: 100%)
2025-08-04 21:57:02 线程3：[信息] [信息] 正在返回上一页... (进度: 100%)
2025-08-04 21:57:02 线程3：[信息] [信息] 已点击返回按钮 (进度: 100%)
2025-08-04 21:57:02 线程3：[信息] [信息] 超时号码已加入黑名单 (进度: 100%)
2025-08-04 21:57:03 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"ysswfsy"},"taskId":"e5fc9df0-713a-11f0-b791-029e46bc98e3"} (进度: 100%)
2025-08-04 21:57:03 线程1：[信息] [信息] 第六页第1次识别结果: ysswfsy → 转换为小写: ysswfsy (进度: 100%)
2025-08-04 21:57:03 线程1：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 21:57:03 线程1：[信息] [信息] 第六页已填入验证码: ysswfsy (进度: 100%)
2025-08-04 21:57:03 线程1：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 21:57:05 线程3：[信息] [信息] 正在选择国家代码: +52 (进度: 100%)
2025-08-04 21:57:07 线程2：[信息] [信息] ⚠️ 20秒内未找到'设备名称'输入框，需要手动处理 (进度: 100%)
2025-08-04 21:57:07 线程2：[信息] [信息] ⚠️ 设备名称页面加载超时，需要手动处理 (进度: 100%)
2025-08-04 21:57:07 [信息] 设备名称页面加载超时，需要手动处理
2025-08-04 21:57:07 线程2：[信息] [信息] 页面加载完成后，手动点击继续注册 (进度: 100%)
2025-08-04 21:57:07 线程2：[信息] [信息] MFA页面需要手动处理，等待用户操作 (进度: 100%)
2025-08-04 21:57:08 线程1：[信息] [信息] 第1次图形验证码识别结果错误，等待新验证码 (进度: 100%)
2025-08-04 21:57:08 线程1：[信息] [信息] 第六页第1次识别异常: 验证码错误 (进度: 100%)
2025-08-04 21:57:09 线程3：[信息] [信息] 已打开国家代码下拉列表 (进度: 100%)
2025-08-04 21:57:10 线程1：[信息] [信息] 第六页第2次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 21:57:10 线程3：[信息] [信息] 已选择国家代码: +52 (进度: 100%) (进度: 100%)
2025-08-04 21:57:10 线程3：[信息] [信息] 后台获取新手机号码... (进度: 100%)
2025-08-04 21:57:10 [信息] [榴莲API] 获取手机号码，尝试 1/4
2025-08-04 21:57:10 线程3：[信息] [信息] 后台获取新手机号码成功: +524361739745，已保存到注册数据 (进度: 100%)
2025-08-04 21:57:10 线程3：[信息] [信息] 正在清空并填入新的手机号码... (进度: 100%)
2025-08-04 21:57:11 线程3：[信息] [信息] 已填入新手机号码: +524361739745 (进度: 100%)
2025-08-04 21:57:11 线程3：[信息] [信息] 正在点击发送短信按钮... (进度: 100%)
2025-08-04 21:57:13 线程1：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 31395 字节 (进度: 100%)
2025-08-04 21:57:13 线程1：[信息] [信息] ✅ 图片验证通过：200x70px，31395字节，复杂度符合要求 (进度: 100%)
2025-08-04 21:57:13 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 21:57:13 线程3：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 100%)
2025-08-04 21:57:13 线程3：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-08-04 21:57:14 线程3：[信息] [信息] 新手机号码已填入，自动模式：开始处理图形验证码... (进度: 100%)
2025-08-04 21:57:14 线程3：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-04 21:57:16 [信息] 获取线程2当前数据: <EMAIL>
2025-08-04 21:57:16 线程2：[信息] 终止时正在处理的数据: <EMAIL>
2025-08-04 21:57:16 线程2：[信息] 数据详情: <EMAIL>|09o8psLx|Vilchez Roxana|Banco de Chile|Pasaje 18 1/2 Oriente A con 8|Maule|Talca|3460000|5331870059278544|06|29|411|Vilchez Roxana|F2ZiOpiJ|CL
2025-08-04 21:57:16 线程2：[信息] 多线程终止 - 复制密钥信息到剪贴板
2025-08-04 21:57:16 线程2：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：F2ZiOpiJ ③AWS密码：09o8psLx ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止 (进度: 100%)
2025-08-04 21:57:16 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：F2ZiOpiJ ③AWS密码：09o8psLx ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 21:57:16 线程2：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：F2ZiOpiJ ③AWS密码：09o8psLx ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 21:57:16 [信息] 线程2请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：F2ZiOpiJ ③AWS密码：09o8psLx ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 21:57:16 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：F2ZiOpiJ ③AWS密码：09o8psLx ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 21:57:16 [信息] 线程2剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：F2ZiOpiJ ③AWS密码：09o8psLx ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 21:57:16 线程2：[信息] [信息] 注册已终止 (进度: 100%)
2025-08-04 21:57:16 线程2：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-08-04 21:57:16 线程2：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：F2ZiOpiJ ③AWS密码：09o8psLx ④访问密钥：AKIASEFYCFRG7LIL74KO ⑤秘密访问密钥：bMqLZro0hX0EMqKTrIqbD8t5jnwamT7yv0Y23XbsOcultar ⑥MFA信息：   //手动终止 (进度: 100%)
2025-08-04 21:57:16 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：F2ZiOpiJ ③AWS密码：09o8psLx ④访问密钥：AKIASEFYCFRG7LIL74KO ⑤秘密访问密钥：bMqLZro0hX0EMqKTrIqbD8t5jnwamT7yv0Y23XbsOcultar ⑥MFA信息：   //手动终止
2025-08-04 21:57:16 线程2：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：F2ZiOpiJ ③AWS密码：09o8psLx ④访问密钥：AKIASEFYCFRG7LIL74KO ⑤秘密访问密钥：bMqLZro0hX0EMqKTrIqbD8t5jnwamT7yv0Y23XbsOcultar ⑥MFA信息：   //手动终止
2025-08-04 21:57:16 [信息] 线程2请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：F2ZiOpiJ ③AWS密码：09o8psLx ④访问密钥：AKIASEFYCFRG7LIL74KO ⑤秘密访问密钥：bMqLZro0hX0EMqKTrIqbD8t5jnwamT7yv0Y23XbsOcultar ⑥MFA信息：   //手动终止
2025-08-04 21:57:16 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：F2ZiOpiJ ③AWS密码：09o8psLx ④访问密钥：AKIASEFYCFRG7LIL74KO ⑤秘密访问密钥：bMqLZro0hX0EMqKTrIqbD8t5jnwamT7yv0Y23XbsOcultar ⑥MFA信息：   //手动终止
2025-08-04 21:57:16 [信息] 线程2剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：F2ZiOpiJ ③AWS密码：09o8psLx ④访问密钥：AKIASEFYCFRG7LIL74KO ⑤秘密访问密钥：bMqLZro0hX0EMqKTrIqbD8t5jnwamT7yv0Y23XbsOcultar ⑥MFA信息：   //手动终止
2025-08-04 21:57:16 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-08-04 21:57:16 线程2：[信息] 临时目录已清理: C:\Users\<USER>\AppData\Local\Temp\AWS_Thread_2_20250804_215200
2025-08-04 21:57:16 线程2：[信息] 已终止
2025-08-04 21:57:16 [信息] 线程2已终止
2025-08-04 21:57:16 [信息] 开始处理线程2终止数据，共1个数据
2025-08-04 21:57:16 [信息] 处理线程2终止数据: <EMAIL>
2025-08-04 21:57:16 [信息] 从注册数据列表中移除: <EMAIL>
2025-08-04 21:57:16 [信息] 线程2终止 - 数据已移动到终止列表: <EMAIL>
2025-08-04 21:57:16 [信息] 线程2终止数据处理完成，成功移动1个数据
2025-08-04 21:57:16 [信息] UniformGrid列数已更新为: 1
2025-08-04 21:57:16 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-08-04 21:57:16 [信息] 线程2已终止
2025-08-04 21:57:17 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"xtp36s"},"taskId":"ee4794f6-713a-11f0-b791-029e46bc98e3"} (进度: 100%)
2025-08-04 21:57:17 线程1：[信息] [信息] 第六页第2次识别结果: xtp36s → 转换为小写: xtp36s (进度: 100%)
2025-08-04 21:57:17 线程3：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-04 21:57:17 线程3：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 21:57:17 线程1：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 21:57:17 线程1：[信息] [信息] 第六页已填入验证码: xtp36s (进度: 100%)
2025-08-04 21:57:17 线程1：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 21:57:20 线程1：[信息] [信息] 第2次图形验证码识别成功 (进度: 100%)
2025-08-04 21:57:20 线程1：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-08-04 21:57:27 线程3：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 34986 字节 (进度: 100%)
2025-08-04 21:57:27 线程3：[信息] [信息] ✅ 图片验证通过：201x70px，34986字节，复杂度符合要求 (进度: 100%)
2025-08-04 21:57:27 线程3：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 21:57:28 线程1：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-08-04 21:57:30 [信息] 定时检查发现1个待释放手机号码，开始批量释放
2025-08-04 21:57:30 [信息] 开始释放1个手机号码
2025-08-04 21:57:30 [信息] [手机API] 开始批量释放1个手机号码
2025-08-04 21:57:30 [信息] [手机API] 释放手机号码: +526642804884
2025-08-04 21:57:30 [信息] [手机API] 手机号码释放成功: +526642804884
2025-08-04 21:57:31 [信息] [手机API] 批量释放完成: 成功1个, 失败0个
2025-08-04 21:57:31 [信息] 定时批量释放完成: 批量释放完成: 成功1个, 失败0个
2025-08-04 21:57:31 线程1：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-04 21:57:31 线程1：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 21:57:31 线程1：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 21:57:31 线程1：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-08-04 21:57:36 线程1：[信息] [信息] 线程1开始独立获取验证码... (进度: 100%)
2025-08-04 21:57:36 线程1：[信息] [信息] 线程1开始获取验证码，最多尝试8次，每次间隔8秒 (进度: 100%)
2025-08-04 21:57:36 线程1：[信息] [信息] 线程1第1次尝试获取验证码...（剩余7次尝试） (进度: 100%)
2025-08-04 21:57:36 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-04 21:57:36 线程1：[信息] [信息] 线程1验证码获取成功: 9432 (进度: 100%)
2025-08-04 21:57:36 [信息] 线程1手机号码已加入释放队列: +529361036023 (原因: 获取验证码成功)
2025-08-04 21:57:36 线程1：[信息] [信息] 线程1验证码获取成功: 9432，立即填入验证码... (进度: 100%)
2025-08-04 21:57:36 线程1：[信息] [信息] 第七页：找到验证码输入框 (Verify code) (进度: 100%)
2025-08-04 21:57:36 线程1：[信息] [信息] 线程1已自动填入手机验证码: 9432 (进度: 100%)
2025-08-04 21:57:37 线程3：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"gm3cbf"},"taskId":"fa4d5650-713a-11f0-9ab0-ba03bdd70631"} (进度: 100%)
2025-08-04 21:57:37 线程3：[信息] [信息] 第六页第1次识别结果: gm3cbf → 转换为小写: gm3cbf (进度: 100%)
2025-08-04 21:57:37 线程3：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 21:57:37 线程3：[信息] [信息] 第六页已填入验证码: gm3cbf (进度: 100%)
2025-08-04 21:57:37 线程3：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 21:57:37 线程1：[信息] [信息] 线程1正在自动点击Continue按钮... (进度: 100%)
2025-08-04 21:57:37 线程1：[信息] [信息] 线程1手机验证码验证完成，继续执行后续步骤... (进度: 100%)
2025-08-04 21:57:40 线程3：[信息] [信息] 第1次图形验证码识别成功 (进度: 100%)
2025-08-04 21:57:40 线程3：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-08-04 21:57:43 线程1：[信息] [信息] 验证码验证完成，等待页面加载... (进度: 100%)
2025-08-04 21:57:43 线程1：[信息] [信息] 已点击完成注册按钮，正在处理... (进度: 100%)
2025-08-04 21:57:44 线程1：[信息] [信息] 自动模式验证完成，API手机号码已加入黑名单: 加入黑名单成功 (进度: 100%)
2025-08-04 21:57:44 线程3：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-08-04 21:57:44 线程1：[信息] [信息] 等待完成注册页面加载... (进度: 100%)
2025-08-04 21:57:47 线程3：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-04 21:57:47 线程3：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 21:57:47 线程3：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 21:57:47 线程3：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-08-04 21:57:47 线程1：[信息] [信息] 完成注册页面加载完成 (进度: 100%)
2025-08-04 21:57:47 线程1：[信息] [信息] 🔍 正在查找'Go to the AWS Management Console'链接... (进度: 100%)
2025-08-04 21:57:52 线程3：[信息] [信息] 线程3开始独立获取验证码... (进度: 100%)
2025-08-04 21:57:52 线程3：[信息] [信息] 线程3开始获取验证码，最多尝试8次，每次间隔8秒 (进度: 100%)
2025-08-04 21:57:52 线程3：[信息] [信息] 线程3第1次尝试获取验证码...（剩余7次尝试） (进度: 100%)
2025-08-04 21:57:52 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-04 21:57:53 线程3：[信息] [信息] 线程3验证码获取成功: 2760 (进度: 100%)
2025-08-04 21:57:53 [信息] 线程3手机号码已加入释放队列: +524361739745 (原因: 获取验证码成功)
2025-08-04 21:57:53 线程3：[信息] [信息] 线程3验证码获取成功: 2760，立即填入验证码... (进度: 100%)
2025-08-04 21:57:53 线程3：[信息] [信息] 第七页：找到验证码输入框 (Verify code) (进度: 100%)
2025-08-04 21:57:53 线程3：[信息] [信息] 线程3已自动填入手机验证码: 2760 (进度: 100%)
2025-08-04 21:57:54 线程3：[信息] [信息] 线程3正在自动点击Continue按钮... (进度: 100%)
2025-08-04 21:57:54 线程3：[信息] [信息] 线程3手机验证码验证完成，继续执行后续步骤... (进度: 100%)
2025-08-04 21:57:59 线程1：[信息] [信息] 🖱️ 已点击跳转管理控制台链接 (进度: 100%)
2025-08-04 21:57:59 线程1：[信息] [信息] 🔗 通过导航进入IAM安全凭证页面... (进度: 100%)
2025-08-04 21:57:59 线程1：[信息] [信息] ⏳ 等待管理控制台页面加载，智能检测'更多'按钮（超时20秒）... (进度: 100%)
2025-08-04 21:57:59 线程3：[信息] [信息] 验证码验证完成，等待页面加载... (进度: 100%)
2025-08-04 21:57:59 线程3：[信息] [信息] 已点击完成注册按钮，正在处理... (进度: 100%)
2025-08-04 21:57:59 线程3：[信息] [信息] 自动模式验证完成，API手机号码已加入黑名单: 加入黑名单成功 (进度: 100%)
2025-08-04 21:58:00 线程3：[信息] [信息] 等待完成注册页面加载... (进度: 100%)
2025-08-04 21:58:00 [信息] 定时检查发现2个待释放手机号码，开始批量释放
2025-08-04 21:58:00 [信息] 开始释放2个手机号码
2025-08-04 21:58:00 [信息] [手机API] 开始批量释放2个手机号码
2025-08-04 21:58:00 [信息] [手机API] 释放手机号码: +529361036023
2025-08-04 21:58:00 [信息] [手机API] 手机号码释放成功: +529361036023
2025-08-04 21:58:01 [信息] [手机API] 释放手机号码: +524361739745
2025-08-04 21:58:01 [信息] [手机API] 手机号码释放成功: +524361739745
2025-08-04 21:58:02 [信息] [手机API] 批量释放完成: 成功2个, 失败0个
2025-08-04 21:58:02 [信息] 定时批量释放完成: 批量释放完成: 成功2个, 失败0个
2025-08-04 21:58:03 线程3：[信息] [信息] 完成注册页面加载完成 (进度: 100%)
2025-08-04 21:58:03 线程3：[信息] [信息] 🔍 正在查找'Go to the AWS Management Console'链接... (进度: 100%)
2025-08-04 21:58:14 线程3：[信息] [信息] 🖱️ 已点击跳转管理控制台链接 (进度: 100%)
2025-08-04 21:58:14 线程3：[信息] [信息] 🔗 通过导航进入IAM安全凭证页面... (进度: 100%)
2025-08-04 21:58:15 线程3：[信息] [信息] ⏳ 等待管理控制台页面加载，智能检测'更多'按钮（超时20秒）... (进度: 100%)
2025-08-04 21:58:15 线程1：[信息] [信息] ✅ 找到'更多'按钮，页面加载完成 (进度: 100%)
2025-08-04 21:58:15 线程1：[信息] [信息] 🖱️ 正在点击更多按钮... (进度: 100%)
2025-08-04 21:58:15 线程1：[信息] [信息] ✅ 成功点击更多按钮 (进度: 100%)
2025-08-04 21:58:15 [信息] 成功点击更多按钮
2025-08-04 21:58:16 线程1：[信息] [信息] 🖱️ 正在点击账户信息按钮... (进度: 100%)
2025-08-04 21:58:17 线程1：[信息] [信息] ✅ 成功点击账户信息按钮 (进度: 100%)
2025-08-04 21:58:17 [信息] 成功点击账户信息按钮
2025-08-04 21:58:18 线程1：[信息] [信息] 🖱️ 正在点击'安全凭证'菜单项... (进度: 100%)
2025-08-04 21:58:18 线程1：[信息] [信息] 🔍 正在定位'安全凭证'链接... (进度: 100%)
2025-08-04 21:58:18 线程1：[信息] [信息] ✅ 成功定位到'安全凭证'链接 (进度: 100%)
2025-08-04 21:58:18 [信息] 成功定位到'安全凭证'链接
2025-08-04 21:58:25 线程1：[信息] [信息] ✅ 成功点击'安全凭证'菜单项 (进度: 100%)
2025-08-04 21:58:25 [信息] 成功点击'安全凭证'链接
2025-08-04 21:58:25 线程1：[信息] [信息] ⏳ 等待IAM安全凭证页面加载（20秒超时，循环检测账单问题和创建密钥按钮）... (进度: 100%)
2025-08-04 21:58:28 线程3：[信息] [信息] ✅ 找到'更多'按钮，页面加载完成 (进度: 100%)
2025-08-04 21:58:28 线程3：[信息] [信息] 🖱️ 正在点击更多按钮... (进度: 100%)
2025-08-04 21:58:28 线程3：[信息] [信息] ✅ 成功点击更多按钮 (进度: 100%)
2025-08-04 21:58:28 [信息] 成功点击更多按钮
2025-08-04 21:58:29 线程3：[信息] [信息] 🖱️ 正在点击账户信息按钮... (进度: 100%)
2025-08-04 21:58:30 线程3：[信息] [信息] ✅ 成功点击账户信息按钮 (进度: 100%)
2025-08-04 21:58:30 [信息] 成功点击账户信息按钮
2025-08-04 21:58:31 线程3：[信息] [信息] 🖱️ 正在点击'安全凭证'菜单项... (进度: 100%)
2025-08-04 21:58:31 线程3：[信息] [信息] 🔍 正在定位'安全凭证'链接... (进度: 100%)
2025-08-04 21:58:31 线程3：[信息] [信息] ✅ 成功定位到'安全凭证'链接 (进度: 100%)
2025-08-04 21:58:31 [信息] 成功定位到'安全凭证'链接
2025-08-04 21:58:40 线程3：[信息] [信息] ✅ 成功点击'安全凭证'菜单项 (进度: 100%)
2025-08-04 21:58:40 [信息] 成功点击'安全凭证'链接
2025-08-04 21:58:40 线程3：[信息] [信息] ⏳ 等待IAM安全凭证页面加载（20秒超时，循环检测账单问题和创建密钥按钮）... (进度: 100%)
2025-08-04 21:58:46 线程1：[信息] [信息] ⚠️ 20秒超时未找到创建密钥按钮，尝试通用页面状态检查 (进度: 100%)
2025-08-04 21:58:46 线程1：[信息] [信息] 🔍 检查页面状态... (进度: 100%)
2025-08-04 21:58:46 线程1：[信息] [信息] 当前页面URL: https://us-east-1.console.aws.amazon.com/iam/home?region=ap-southeast-2#/security_credentials (进度: 100%)
2025-08-04 21:58:46 线程1：[信息] [信息] ✅ 页面状态正常，开始密钥提取流程 (进度: 100%)
2025-08-04 21:58:46 线程1：[信息] [信息] 🔍 设置页面缩放为50%... (进度: 100%)
2025-08-04 21:58:47 线程1：[信息] [信息] ✅ 页面缩放设置完成 (进度: 100%)
2025-08-04 21:58:47 [信息] 页面缩放设置为50%完成
2025-08-04 21:58:47 线程1：[信息] [信息] 🔍 正在查找并点击'下一步'按钮关闭向导... (进度: 100%)
2025-08-04 21:58:47 [信息] 开始查找并点击'下一步'按钮关闭向导
2025-08-04 21:58:47 线程1：[信息] [信息] ℹ️ 未检测到向导元素，向导可能已自动关闭 (进度: 100%)
2025-08-04 21:58:47 [信息] 未检测到向导元素，向导可能已自动关闭
2025-08-04 21:58:47 线程1：[信息] [信息] 🔑 第四阶段：开始创建和复制访问密钥 (进度: 100%)
2025-08-04 21:58:47 [信息] 开始创建和复制访问密钥
2025-08-04 21:58:47 线程1：[信息] [信息] 🔑 正在点击'创建访问密钥'按钮... (进度: 100%)
2025-08-04 21:58:47 线程1：[信息] [信息] 🔍 正在定位'创建访问密钥'按钮... (进度: 100%)
2025-08-04 21:58:50 线程1：[信息] [信息] ✅ 成功定位到'创建访问密钥'按钮 (进度: 100%)
2025-08-04 21:58:50 [信息] 成功定位到'创建访问密钥'按钮
2025-08-04 21:59:00 线程1：[信息] [信息] ✅ 成功点击'创建访问密钥'按钮 (进度: 100%)
2025-08-04 21:59:00 [信息] 成功点击'创建访问密钥'按钮
2025-08-04 21:59:01 线程3：[信息] [信息] ⚠️ 20秒超时未找到创建密钥按钮，尝试通用页面状态检查 (进度: 100%)
2025-08-04 21:59:01 线程3：[信息] [信息] 🔍 检查页面状态... (进度: 100%)
2025-08-04 21:59:01 线程3：[信息] [信息] 当前页面URL: https://us-east-1.console.aws.amazon.com/iam/home?region=ap-southeast-2#/security_credentials (进度: 100%)
2025-08-04 21:59:01 线程3：[信息] [信息] ✅ 页面状态正常，开始密钥提取流程 (进度: 100%)
2025-08-04 21:59:01 线程3：[信息] [信息] 🔍 设置页面缩放为50%... (进度: 100%)
2025-08-04 21:59:02 线程1：[信息] [信息] ☑️ 正在勾选确认复选框... (进度: 100%)
2025-08-04 21:59:02 线程1：[信息] [信息] ✅ 使用id属性定位到确认复选框 (进度: 100%)
2025-08-04 21:59:02 [信息] 使用id属性定位到确认复选框
2025-08-04 21:59:02 线程3：[信息] [信息] ✅ 页面缩放设置完成 (进度: 100%)
2025-08-04 21:59:02 [信息] 页面缩放设置为50%完成
2025-08-04 21:59:02 线程3：[信息] [信息] 🔍 正在查找并点击'下一步'按钮关闭向导... (进度: 100%)
2025-08-04 21:59:02 [信息] 开始查找并点击'下一步'按钮关闭向导
2025-08-04 21:59:02 线程3：[信息] [信息] ℹ️ 未检测到向导元素，向导可能已自动关闭 (进度: 100%)
2025-08-04 21:59:02 [信息] 未检测到向导元素，向导可能已自动关闭
2025-08-04 21:59:02 线程3：[信息] [信息] 🔑 第四阶段：开始创建和复制访问密钥 (进度: 100%)
2025-08-04 21:59:02 [信息] 开始创建和复制访问密钥
2025-08-04 21:59:02 线程3：[信息] [信息] 🔑 正在点击'创建访问密钥'按钮... (进度: 100%)
2025-08-04 21:59:02 线程3：[信息] [信息] 🔍 正在定位'创建访问密钥'按钮... (进度: 100%)
2025-08-04 21:59:02 线程1：[信息] [信息] ✅ 成功勾选确认复选框 (进度: 100%)
2025-08-04 21:59:02 [信息] 成功勾选确认复选框
2025-08-04 21:59:03 线程1：[信息] [信息] 🔑 正在点击'创建访问密钥'确认按钮... (进度: 100%)
2025-08-04 21:59:04 线程1：[信息] [信息] ✅ 成功点击'创建访问密钥'确认按钮 (进度: 100%)
2025-08-04 21:59:04 [信息] 成功点击'创建访问密钥'确认按钮
2025-08-04 21:59:07 线程1：[信息] [信息] 📋 开始复制访问密钥... (进度: 100%)
2025-08-04 21:59:07 [信息] 开始复制访问密钥
2025-08-04 21:59:09 线程1：[信息] [信息] 🔍 方法2找到 2 个单元格 (进度: 100%)
2025-08-04 21:59:09 [信息] 方法2找到 2 个单元格
2025-08-04 21:59:09 线程1：[信息] [信息] 🔍 总共找到 2 个单元格，开始分析... (进度: 100%)
2025-08-04 21:59:09 [信息] 总共找到 2 个单元格，开始分析
2025-08-04 21:59:09 [信息] 单元格[0]: 'AKIAVA2QSYAAQ5ZE7MNY'
2025-08-04 21:59:09 [信息] 单元格[1]: '***************Mostrar'
2025-08-04 21:59:09 线程1：[信息] [信息] 📋 正在查找并复制访问密钥... (进度: 100%)
2025-08-04 21:59:09 线程1：[信息] [信息] ✅ 找到访问密钥: AKIAVA2QSYAAQ5ZE7MNY (进度: 100%)
2025-08-04 21:59:09 [信息] 找到访问密钥: AKIAVA2QSYAAQ5ZE7MNY
2025-08-04 21:59:10 线程3：[信息] [信息] ✅ 成功定位到'创建访问密钥'按钮 (进度: 100%)
2025-08-04 21:59:10 [信息] 成功定位到'创建访问密钥'按钮
2025-08-04 21:59:14 线程3：[信息] [信息] ✅ 成功点击'创建访问密钥'按钮 (进度: 100%)
2025-08-04 21:59:14 [信息] 成功点击'创建访问密钥'按钮
2025-08-04 21:59:15 线程1：[信息] [信息] ✅ 方法1成功点击访问密钥复制按钮 (进度: 100%)
2025-08-04 21:59:15 [信息] 方法1成功点击访问密钥复制按钮
2025-08-04 21:59:16 线程3：[信息] [信息] ☑️ 正在勾选确认复选框... (进度: 100%)
2025-08-04 21:59:16 线程1：[信息] [信息] 📋 正在查找并复制秘密访问密钥... (进度: 100%)
2025-08-04 21:59:16 线程1：[信息] [信息] ✅ 找到秘密访问密钥: ***************Mostrar (进度: 100%)
2025-08-04 21:59:16 [信息] 找到秘密访问密钥: ***************Mostrar
2025-08-04 21:59:16 线程1：[信息] [信息] 🔍 检测到隐藏的秘密访问密钥，尝试点击显示按钮... (进度: 100%)
2025-08-04 21:59:16 [信息] 检测到隐藏的秘密访问密钥，尝试点击显示按钮
2025-08-04 21:59:16 线程1：[信息] [信息] ✅ 使用TestId定位到显示按钮 (进度: 100%)
2025-08-04 21:59:16 [信息] 使用TestId定位到显示按钮
2025-08-04 21:59:18 线程1：[信息] [信息] ✅ 显示按钮点击成功，新文本: X+Ko+nY6FetxynPb5jovhZeWbqnibhEJ3Jzi4RsjOcultar (进度: 100%)
2025-08-04 21:59:18 [信息] 显示按钮点击成功，新文本: X+Ko+nY6FetxynPb5jovhZeWbqnibhEJ3Jzi4RsjOcultar
2025-08-04 21:59:18 线程1：[信息] [信息] ✅ 直接从显示文本提取秘密访问密钥: X+Ko+nY6FetxynPb5jovhZeWbqnibhEJ3Jzi4RsjOcultar (进度: 100%)
2025-08-04 21:59:18 [信息] 直接从显示文本提取秘密访问密钥: X+Ko+nY6FetxynPb5jovhZeWbqnibhEJ3Jzi4RsjOcultar
2025-08-04 21:59:18 线程1：[信息] [信息] ✅ 访问密钥复制完成 (进度: 100%)
2025-08-04 21:59:18 [信息] 访问密钥复制完成 - AccessKey: AKIAVA2QSYAAQ5ZE7MNY, SecretKey: X+Ko+nY6FetxynPb5jovhZeWbqnibhEJ3Jzi4RsjOcultar
2025-08-04 21:59:19 线程1：[信息] [信息] 🖱️ 正在点击'已完成'按钮... (进度: 100%)
2025-08-04 21:59:19 线程1：[信息] [信息] ✅ 成功点击'已完成'按钮 (进度: 100%)
2025-08-04 21:59:19 [信息] 成功点击'已完成'按钮
2025-08-04 21:59:19 线程1：[信息] [信息] ✅ 密钥已保存到数据对象 - AccessKey: AKIAVA2QSYAAQ5ZE7MNY, SecretKey: X+Ko+nY6FetxynPb5jovhZeWbqnibhEJ3Jzi4RsjOcultar (进度: 100%)
2025-08-04 21:59:19 [信息] 密钥已保存到数据对象 - AccessKey: AKIAVA2QSYAAQ5ZE7MNY, SecretKey: X+Ko+nY6FetxynPb5jovhZeWbqnibhEJ3Jzi4RsjOcultar
2025-08-04 21:59:20 线程1：[信息] [信息] 🖱️ 正在点击'继续'按钮... (进度: 100%)
2025-08-04 21:59:21 线程3：[信息] [信息] ⚠️ id属性定位失败，使用CSS类定位 (进度: 100%)
2025-08-04 21:59:21 [信息] id属性定位失败，使用CSS类定位
2025-08-04 21:59:23 线程1：[信息] [信息] ❌ 点击'继续'按钮失败: Timeout 3000ms exceeded.
Call log:
  - waiting for Locator("button[data-awsui-analytics*='navigate']") to be visible (进度: 100%)
2025-08-04 21:59:23 [信息] 点击'继续'按钮失败: Timeout 3000ms exceeded.
Call log:
  - waiting for Locator("button[data-awsui-analytics*='navigate']") to be visible
2025-08-04 21:59:23 线程1：[信息] [信息] 🔐 开始设置MFA设备... (进度: 100%)
2025-08-04 21:59:23 [信息] 开始设置MFA设备
2025-08-04 21:59:23 线程1：[信息] [信息] 🖱️ 正在点击'分配 MFA 设备'按钮... (进度: 100%)
2025-08-04 21:59:23 线程1：[信息] [信息] ✅ 成功点击'分配 MFA 设备'按钮 (进度: 100%)
2025-08-04 21:59:23 [信息] 成功点击'分配 MFA 设备'按钮
2025-08-04 21:59:23 线程1：[信息] [信息] ⏳ 等待MFA设备设置页面加载（20秒超时）... (进度: 100%)
2025-08-04 21:59:27 线程3：[信息] [信息] ✅ 成功勾选确认复选框 (进度: 100%)
2025-08-04 21:59:27 [信息] 成功勾选确认复选框
2025-08-04 21:59:28 线程3：[信息] [信息] 🔑 正在点击'创建访问密钥'确认按钮... (进度: 100%)
2025-08-04 21:59:28 线程3：[信息] [信息] ✅ 成功点击'创建访问密钥'确认按钮 (进度: 100%)
2025-08-04 21:59:28 [信息] 成功点击'创建访问密钥'确认按钮
2025-08-04 21:59:31 线程3：[信息] [信息] 📋 开始复制访问密钥... (进度: 100%)
2025-08-04 21:59:31 [信息] 开始复制访问密钥
2025-08-04 21:59:33 线程3：[信息] [信息] 🔍 方法2找到 2 个单元格 (进度: 100%)
2025-08-04 21:59:33 [信息] 方法2找到 2 个单元格
2025-08-04 21:59:33 线程3：[信息] [信息] 🔍 总共找到 2 个单元格，开始分析... (进度: 100%)
2025-08-04 21:59:33 [信息] 总共找到 2 个单元格，开始分析
2025-08-04 21:59:33 [信息] 单元格[0]: 'AKIA5SQEL6NJWBZJXQBV'
2025-08-04 21:59:33 [信息] 单元格[1]: '***************Mostrar'
2025-08-04 21:59:33 线程3：[信息] [信息] 📋 正在查找并复制访问密钥... (进度: 100%)
2025-08-04 21:59:33 线程3：[信息] [信息] ✅ 找到访问密钥: AKIA5SQEL6NJWBZJXQBV (进度: 100%)
2025-08-04 21:59:33 [信息] 找到访问密钥: AKIA5SQEL6NJWBZJXQBV
2025-08-04 21:59:33 线程3：[信息] [信息] ✅ 方法1成功点击访问密钥复制按钮 (进度: 100%)
2025-08-04 21:59:33 [信息] 方法1成功点击访问密钥复制按钮
2025-08-04 21:59:35 线程3：[信息] [信息] 📋 正在查找并复制秘密访问密钥... (进度: 100%)
2025-08-04 21:59:35 线程3：[信息] [信息] ✅ 找到秘密访问密钥: ***************Mostrar (进度: 100%)
2025-08-04 21:59:35 [信息] 找到秘密访问密钥: ***************Mostrar
2025-08-04 21:59:35 线程3：[信息] [信息] 🔍 检测到隐藏的秘密访问密钥，尝试点击显示按钮... (进度: 100%)
2025-08-04 21:59:35 [信息] 检测到隐藏的秘密访问密钥，尝试点击显示按钮
2025-08-04 21:59:35 线程3：[信息] [信息] ✅ 使用TestId定位到显示按钮 (进度: 100%)
2025-08-04 21:59:35 [信息] 使用TestId定位到显示按钮
2025-08-04 21:59:36 线程3：[信息] [信息] ✅ 显示按钮点击成功，新文本: ylgJBXkjGAh2H4yhNus9tffzRmidSIm3PF8GcZmyOcultar (进度: 100%)
2025-08-04 21:59:36 [信息] 显示按钮点击成功，新文本: ylgJBXkjGAh2H4yhNus9tffzRmidSIm3PF8GcZmyOcultar
2025-08-04 21:59:36 线程3：[信息] [信息] ✅ 直接从显示文本提取秘密访问密钥: ylgJBXkjGAh2H4yhNus9tffzRmidSIm3PF8GcZmyOcultar (进度: 100%)
2025-08-04 21:59:36 [信息] 直接从显示文本提取秘密访问密钥: ylgJBXkjGAh2H4yhNus9tffzRmidSIm3PF8GcZmyOcultar
2025-08-04 21:59:36 线程3：[信息] [信息] ✅ 访问密钥复制完成 (进度: 100%)
2025-08-04 21:59:36 [信息] 访问密钥复制完成 - AccessKey: AKIA5SQEL6NJWBZJXQBV, SecretKey: ylgJBXkjGAh2H4yhNus9tffzRmidSIm3PF8GcZmyOcultar
2025-08-04 21:59:37 线程3：[信息] [信息] 🖱️ 正在点击'已完成'按钮... (进度: 100%)
2025-08-04 21:59:38 线程3：[信息] [信息] ✅ 成功点击'已完成'按钮 (进度: 100%)
2025-08-04 21:59:38 [信息] 成功点击'已完成'按钮
2025-08-04 21:59:38 线程3：[信息] [信息] ✅ 密钥已保存到数据对象 - AccessKey: AKIA5SQEL6NJWBZJXQBV, SecretKey: ylgJBXkjGAh2H4yhNus9tffzRmidSIm3PF8GcZmyOcultar (进度: 100%)
2025-08-04 21:59:38 [信息] 密钥已保存到数据对象 - AccessKey: AKIA5SQEL6NJWBZJXQBV, SecretKey: ylgJBXkjGAh2H4yhNus9tffzRmidSIm3PF8GcZmyOcultar
2025-08-04 21:59:39 线程3：[信息] [信息] 🖱️ 正在点击'继续'按钮... (进度: 100%)
2025-08-04 21:59:42 线程3：[信息] [信息] ❌ 点击'继续'按钮失败: Timeout 3000ms exceeded.
Call log:
  - waiting for Locator("button[data-awsui-analytics*='navigate']") to be visible (进度: 100%)
2025-08-04 21:59:42 [信息] 点击'继续'按钮失败: Timeout 3000ms exceeded.
Call log:
  - waiting for Locator("button[data-awsui-analytics*='navigate']") to be visible
2025-08-04 21:59:42 线程3：[信息] [信息] 🔐 开始设置MFA设备... (进度: 100%)
2025-08-04 21:59:42 [信息] 开始设置MFA设备
2025-08-04 21:59:42 线程3：[信息] [信息] 🖱️ 正在点击'分配 MFA 设备'按钮... (进度: 100%)
2025-08-04 21:59:44 线程1：[信息] [信息] ⚠️ 20秒内未找到'设备名称'输入框，需要手动处理 (进度: 100%)
2025-08-04 21:59:44 线程1：[信息] [信息] ⚠️ 设备名称页面加载超时，需要手动处理 (进度: 100%)
2025-08-04 21:59:44 [信息] 设备名称页面加载超时，需要手动处理
2025-08-04 21:59:44 线程1：[信息] [信息] 页面加载完成后，手动点击继续注册 (进度: 100%)
2025-08-04 21:59:44 线程1：[信息] [信息] MFA页面需要手动处理，等待用户操作 (进度: 100%)
2025-08-04 21:59:44 线程3：[信息] [信息] ✅ 成功点击'分配 MFA 设备'按钮 (进度: 100%)
2025-08-04 21:59:44 [信息] 成功点击'分配 MFA 设备'按钮
2025-08-04 21:59:44 线程3：[信息] [信息] ⏳ 等待MFA设备设置页面加载（20秒超时）... (进度: 100%)
2025-08-04 21:59:53 [信息] 获取线程1当前数据: <EMAIL>
2025-08-04 21:59:53 线程1：[信息] 终止时正在处理的数据: <EMAIL>
2025-08-04 21:59:53 线程1：[信息] 数据详情: <EMAIL>|isghkj3O|Hamasaki Rodrigo|Cencosud|Avenida Errazuriz 755 of 502|Valparaiso|Valparaiso|2340000|4757748017501629|10|28|187|Hamasaki Rodrigo|3U7FwuR1|CL
2025-08-04 21:59:53 线程1：[信息] 多线程终止 - 复制密钥信息到剪贴板
2025-08-04 21:59:53 线程1：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：3U7FwuR1 ③AWS密码：isghkj3O ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止 (进度: 100%)
2025-08-04 21:59:53 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：3U7FwuR1 ③AWS密码：isghkj3O ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 21:59:53 线程1：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：3U7FwuR1 ③AWS密码：isghkj3O ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 21:59:53 [信息] 线程1请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：3U7FwuR1 ③AWS密码：isghkj3O ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 21:59:53 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：3U7FwuR1 ③AWS密码：isghkj3O ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 21:59:53 [信息] 线程1剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：3U7FwuR1 ③AWS密码：isghkj3O ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 21:59:53 线程1：[信息] [信息] 注册已终止 (进度: 100%)
2025-08-04 21:59:53 线程1：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-08-04 21:59:53 线程1：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：3U7FwuR1 ③AWS密码：isghkj3O ④访问密钥：AKIAVA2QSYAAQ5ZE7MNY ⑤秘密访问密钥：X+Ko+nY6FetxynPb5jovhZeWbqnibhEJ3Jzi4RsjOcultar ⑥MFA信息：   //手动终止 (进度: 100%)
2025-08-04 21:59:53 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：3U7FwuR1 ③AWS密码：isghkj3O ④访问密钥：AKIAVA2QSYAAQ5ZE7MNY ⑤秘密访问密钥：X+Ko+nY6FetxynPb5jovhZeWbqnibhEJ3Jzi4RsjOcultar ⑥MFA信息：   //手动终止
2025-08-04 21:59:53 线程1：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：3U7FwuR1 ③AWS密码：isghkj3O ④访问密钥：AKIAVA2QSYAAQ5ZE7MNY ⑤秘密访问密钥：X+Ko+nY6FetxynPb5jovhZeWbqnibhEJ3Jzi4RsjOcultar ⑥MFA信息：   //手动终止
2025-08-04 21:59:53 [信息] 线程1请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：3U7FwuR1 ③AWS密码：isghkj3O ④访问密钥：AKIAVA2QSYAAQ5ZE7MNY ⑤秘密访问密钥：X+Ko+nY6FetxynPb5jovhZeWbqnibhEJ3Jzi4RsjOcultar ⑥MFA信息：   //手动终止
2025-08-04 21:59:53 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：3U7FwuR1 ③AWS密码：isghkj3O ④访问密钥：AKIAVA2QSYAAQ5ZE7MNY ⑤秘密访问密钥：X+Ko+nY6FetxynPb5jovhZeWbqnibhEJ3Jzi4RsjOcultar ⑥MFA信息：   //手动终止
2025-08-04 21:59:53 [信息] 线程1剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：3U7FwuR1 ③AWS密码：isghkj3O ④访问密钥：AKIAVA2QSYAAQ5ZE7MNY ⑤秘密访问密钥：X+Ko+nY6FetxynPb5jovhZeWbqnibhEJ3Jzi4RsjOcultar ⑥MFA信息：   //手动终止
2025-08-04 21:59:53 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-08-04 21:59:53 线程1：[信息] 临时目录已清理: C:\Users\<USER>\AppData\Local\Temp\AWS_Thread_1_20250804_215200
2025-08-04 21:59:53 线程1：[信息] 已终止
2025-08-04 21:59:53 [信息] 线程1已终止
2025-08-04 21:59:53 [信息] 开始处理线程1终止数据，共1个数据
2025-08-04 21:59:53 [信息] 处理线程1终止数据: <EMAIL>
2025-08-04 21:59:53 [信息] 从注册数据列表中移除: <EMAIL>
2025-08-04 21:59:53 [信息] 线程1终止 - 数据已移动到终止列表: <EMAIL>
2025-08-04 21:59:53 [信息] 线程1终止数据处理完成，成功移动1个数据
2025-08-04 21:59:53 [信息] UniformGrid列数已更新为: 1
2025-08-04 21:59:53 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 1, 列数: 1
2025-08-04 21:59:53 [信息] 线程1已终止
2025-08-04 22:00:05 线程3：[信息] [信息] ⚠️ 20秒内未找到'设备名称'输入框，需要手动处理 (进度: 100%)
2025-08-04 22:00:05 线程3：[信息] [信息] ⚠️ 设备名称页面加载超时，需要手动处理 (进度: 100%)
2025-08-04 22:00:05 [信息] 设备名称页面加载超时，需要手动处理
2025-08-04 22:00:05 线程3：[信息] [信息] 页面加载完成后，手动点击继续注册 (进度: 100%)
2025-08-04 22:00:05 线程3：[信息] [信息] MFA页面需要手动处理，等待用户操作 (进度: 100%)
2025-08-04 22:00:20 [信息] 获取线程3当前数据: <EMAIL>
2025-08-04 22:00:20 线程3：[信息] 终止时正在处理的数据: <EMAIL>
2025-08-04 22:00:20 线程3：[信息] 数据详情: <EMAIL>|OtcT267Q|M Ronald|Banco Santander Chile|Emlia Tellez 5529 Nunoa, Sant|Region Metropolitana|Santiago|7750000|5331870087915216|08|29|749|M Ronald|r9OtVOM3|CL
2025-08-04 22:00:20 线程3：[信息] 多线程终止 - 复制密钥信息到剪贴板
2025-08-04 22:00:20 线程3：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：r9OtVOM3 ③AWS密码：OtcT267Q ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止 (进度: 100%)
2025-08-04 22:00:20 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：r9OtVOM3 ③AWS密码：OtcT267Q ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 22:00:20 线程3：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：r9OtVOM3 ③AWS密码：OtcT267Q ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 22:00:20 [信息] 线程3请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：r9OtVOM3 ③AWS密码：OtcT267Q ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 22:00:20 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：r9OtVOM3 ③AWS密码：OtcT267Q ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 22:00:20 [信息] 线程3剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：r9OtVOM3 ③AWS密码：OtcT267Q ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 22:00:20 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 22:00:20 [信息] 多线程状态已重置
2025-08-04 22:00:20 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 22:00:20 [信息] 多线程状态已重置
2025-08-04 22:00:20 线程3：[信息] [信息] 注册已终止 (进度: 100%)
2025-08-04 22:00:20 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 22:00:20 [信息] 多线程状态已重置
2025-08-04 22:00:20 线程3：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-08-04 22:00:20 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 22:00:20 [信息] 多线程状态已重置
2025-08-04 22:00:20 线程3：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：r9OtVOM3 ③AWS密码：OtcT267Q ④访问密钥：AKIA5SQEL6NJWBZJXQBV ⑤秘密访问密钥：ylgJBXkjGAh2H4yhNus9tffzRmidSIm3PF8GcZmyOcultar ⑥MFA信息：   //手动终止 (进度: 100%)
2025-08-04 22:00:20 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：r9OtVOM3 ③AWS密码：OtcT267Q ④访问密钥：AKIA5SQEL6NJWBZJXQBV ⑤秘密访问密钥：ylgJBXkjGAh2H4yhNus9tffzRmidSIm3PF8GcZmyOcultar ⑥MFA信息：   //手动终止
2025-08-04 22:00:20 线程3：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：r9OtVOM3 ③AWS密码：OtcT267Q ④访问密钥：AKIA5SQEL6NJWBZJXQBV ⑤秘密访问密钥：ylgJBXkjGAh2H4yhNus9tffzRmidSIm3PF8GcZmyOcultar ⑥MFA信息：   //手动终止
2025-08-04 22:00:20 [信息] 线程3请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：r9OtVOM3 ③AWS密码：OtcT267Q ④访问密钥：AKIA5SQEL6NJWBZJXQBV ⑤秘密访问密钥：ylgJBXkjGAh2H4yhNus9tffzRmidSIm3PF8GcZmyOcultar ⑥MFA信息：   //手动终止
2025-08-04 22:00:20 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：r9OtVOM3 ③AWS密码：OtcT267Q ④访问密钥：AKIA5SQEL6NJWBZJXQBV ⑤秘密访问密钥：ylgJBXkjGAh2H4yhNus9tffzRmidSIm3PF8GcZmyOcultar ⑥MFA信息：   //手动终止
2025-08-04 22:00:20 [信息] 线程3剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：r9OtVOM3 ③AWS密码：OtcT267Q ④访问密钥：AKIA5SQEL6NJWBZJXQBV ⑤秘密访问密钥：ylgJBXkjGAh2H4yhNus9tffzRmidSIm3PF8GcZmyOcultar ⑥MFA信息：   //手动终止
2025-08-04 22:00:20 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 22:00:20 [信息] 多线程状态已重置
2025-08-04 22:00:20 线程3：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-08-04 22:00:20 线程3：[信息] 临时目录已清理: C:\Users\<USER>\AppData\Local\Temp\AWS_Thread_3_20250804_215200
2025-08-04 22:00:20 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 22:00:20 [信息] 多线程状态已重置
2025-08-04 22:00:20 线程3：[信息] 已终止
2025-08-04 22:00:20 [信息] 线程3已终止
2025-08-04 22:00:20 [信息] 开始处理线程3终止数据，共1个数据
2025-08-04 22:00:20 [信息] 处理线程3终止数据: <EMAIL>
2025-08-04 22:00:20 [信息] 从注册数据列表中移除: <EMAIL>
2025-08-04 22:00:20 [信息] 线程3终止 - 数据已移动到终止列表: <EMAIL>
2025-08-04 22:00:20 [信息] 线程3终止数据处理完成，成功移动1个数据
2025-08-04 22:00:20 [信息] UniformGrid列数已更新为: 1
2025-08-04 22:00:20 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 0, 列数: 1
2025-08-04 22:00:20 [信息] 线程3已终止
2025-08-04 22:00:21 [信息] 多线程窗口引用已清理
2025-08-04 22:00:21 [信息] 主窗口已恢复正常状态，多线程窗口引用已清理
2025-08-04 22:00:21 [信息] 多线程管理窗口正在关闭
