2025-08-04 22:52:14 [信息] UniformGrid列数已更新为: 1
2025-08-04 22:52:14 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 1, 列数: 1
2025-08-04 22:52:14 线程1：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-04 22:52:14 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-04 22:52:14 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36
2025-08-04 22:52:14 线程1：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-04 22:52:15 [信息] UniformGrid列数已更新为: 1
2025-08-04 22:52:15 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-08-04 22:52:15 线程3：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-04 22:52:15 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-04 22:52:15 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36
2025-08-04 22:52:15 线程3：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-04 22:52:15 [信息] UniformGrid列数已更新为: 2
2025-08-04 22:52:15 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 3, 列数: 2
2025-08-04 22:52:15 线程2：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-04 22:52:15 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-04 22:52:15 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36
2025-08-04 22:52:15 线程2：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-04 22:52:15 线程1：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-04 22:52:17 线程2：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-04 22:52:17 线程3：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-04 22:52:17 线程1：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-04 22:52:17 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-04 22:52:17 线程1：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-04 22:52:17 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 22:52:17 线程1：[信息] [信息] 多线程模式使用指纹地理位置: -29.9027, -71.2519 (进度: 0%)
2025-08-04 22:52:17 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-29.9027, 经度=-71.2519
2025-08-04 22:52:17 线程1：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_005, CPU: 18核 (进度: 0%)
2025-08-04 22:52:17 [信息] 浏览器指纹注入: Canvas=canvas_fp_005, WebGL=ANGLE (AMD Radeon RX 6700 XT Direct3D11 vs_5_0 ps_5_0), CPU=18核, RAM=8 GB
2025-08-04 22:52:19 线程1：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-04 22:52:19 线程2：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-04 22:52:19 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-04 22:52:19 线程2：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-04 22:52:19 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 22:52:19 线程2：[信息] [信息] 多线程模式使用指纹地理位置: -36.8201, -73.0444 (进度: 0%)
2025-08-04 22:52:19 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-36.8201, 经度=-73.0444
2025-08-04 22:52:19 线程3：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-04 22:52:19 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-04 22:52:19 线程3：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-04 22:52:19 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 22:52:19 线程3：[信息] [信息] 多线程模式使用指纹地理位置: -29.9027, -71.2519 (进度: 0%)
2025-08-04 22:52:19 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-29.9027, 经度=-71.2519
2025-08-04 22:52:19 线程3：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_009, CPU: 16核 (进度: 0%)
2025-08-04 22:52:19 [信息] 浏览器指纹注入: Canvas=canvas_fp_009, WebGL=ANGLE (AMD Radeon RX 6700 XT Direct3D11 vs_5_0 ps_5_0), CPU=16核, RAM=10 GB
2025-08-04 22:52:22 线程3：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-04 22:52:22 线程2：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_008, CPU: 4核 (进度: 0%)
2025-08-04 22:52:22 [信息] 浏览器指纹注入: Canvas=canvas_fp_008, WebGL=ANGLE (NVIDIA GeForce RTX 3070 Direct3D11 vs_5_0 ps_5_0), CPU=4核, RAM=8 GB
2025-08-04 22:52:23 线程2：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-04 22:52:24 线程3：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 16
   • 设备内存: 10 GB
   • 平台信息: Win32
   • Do Not Track: undefined

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (AMD Radeon RX 6700 XT Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (Intel)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 7A8B9C0D
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_003
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-E4F5G6H
   • MAC地址: E4-42-A6-5D-13-98
   • 屏幕分辨率: 2057x1177
   • 可用区域: 2057x1137

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: A3B4C5D6
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: slow-2g
   • 电池API支持: True
   • 电池电量: 0.55
   • 电池充电状态: False
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-04 22:52:24 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 16    • 设备内存: 10 GB    • 平台信息: Win32    • Do Not Track: undefined   图形渲染信息:    • WebGL渲染器: ANGLE (AMD Radeon RX 6700 XT Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (Intel)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 7A8B9C0D    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_003    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-E4F5G6H    • MAC地址: E4-42-A6-5D-13-98    • 屏幕分辨率: 2057x1177    • 可用区域: 2057x1137   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: A3B4C5D6    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: slow-2g    • 电池API支持: True    • 电池电量: 0.55    • 电池充电状态: False    • 性能API支持: True  ================================
2025-08-04 22:52:24 线程1：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 18
   • 设备内存: 8 GB
   • 平台信息: Win32
   • Do Not Track: enabled

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (AMD Radeon RX 6700 XT Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (Intel)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 5E6F7A8B
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_005
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-C2D3E4F
   • MAC地址: 9A-BC-DE-F0-12-34
   • 屏幕分辨率: 1772x1097
   • 可用区域: 1772x1057

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: A5B6C7D8
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: satellite
   • 电池API支持: True
   • 电池电量: 0.27
   • 电池充电状态: True
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-04 22:52:24 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 18    • 设备内存: 8 GB    • 平台信息: Win32    • Do Not Track: enabled   图形渲染信息:    • WebGL渲染器: ANGLE (AMD Radeon RX 6700 XT Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (Intel)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 5E6F7A8B    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_005    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-C2D3E4F    • MAC地址: 9A-BC-DE-F0-12-34    • 屏幕分辨率: 1772x1097    • 可用区域: 1772x1057   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: A5B6C7D8    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: satellite    • 电池API支持: True    • 电池电量: 0.27    • 电池充电状态: True    • 性能API支持: True  ================================
2025-08-04 22:52:24 线程3：[信息] [信息] 已设置浏览器标题: 线程3 - AWS注册工具 (进度: 5%)
2025-08-04 22:52:24 线程2：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 4
   • 设备内存: 8 GB
   • 平台信息: Win32
   • Do Not Track: 1

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 3070 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (Intel Technologies)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 9E0F1A2B
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_008
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-87EL3RX
   • MAC地址: E4-42-A6-5D-13-98
   • 屏幕分辨率: 1980x1050
   • 可用区域: 1980x1010

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: E1F2A3B4
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: satellite
   • 电池API支持: True
   • 电池电量: 0.61
   • 电池充电状态: False
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-04 22:52:24 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 4    • 设备内存: 8 GB    • 平台信息: Win32    • Do Not Track: 1   图形渲染信息:    • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 3070 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (Intel Technologies)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 9E0F1A2B    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_008    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-87EL3RX    • MAC地址: E4-42-A6-5D-13-98    • 屏幕分辨率: 1980x1050    • 可用区域: 1980x1010   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: E1F2A3B4    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: satellite    • 电池API支持: True    • 电池电量: 0.61    • 电池充电状态: False    • 性能API支持: True  ================================
2025-08-04 22:52:24 线程3：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-04 22:52:24 线程1：[信息] [信息] 已设置浏览器标题: 线程1 - AWS注册工具 (进度: 5%)
2025-08-04 22:52:24 线程3：[信息] 浏览器启动成功
2025-08-04 22:52:24 线程2：[信息] [信息] 已设置浏览器标题: 线程2 - AWS注册工具 (进度: 5%)
2025-08-04 22:52:24 线程1：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-04 22:52:24 线程1：[信息] 浏览器启动成功
2025-08-04 22:52:24 线程3：[信息] 获取下一个数据: <EMAIL>
2025-08-04 22:52:24 线程2：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-04 22:52:24 线程2：[信息] 浏览器启动成功
2025-08-04 22:52:24 线程1：[信息] 获取下一个数据: <EMAIL>
2025-08-04 22:52:24 线程2：[信息] 获取下一个数据: <EMAIL>
2025-08-04 22:52:24 线程3：[信息] 开始处理账户: <EMAIL>
2025-08-04 22:52:24 线程1：[信息] 开始处理账户: <EMAIL>
2025-08-04 22:52:24 线程2：[信息] 开始处理账户: <EMAIL>
2025-08-04 22:52:24 线程3：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-04 22:52:24 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-04 22:52:24 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-04 22:52:24 线程3：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-04 22:52:24 线程1：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-04 22:52:24 线程3：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-04 22:52:25 线程2：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-04 22:52:25 线程1：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-04 22:52:25 线程3：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-04 22:52:25 线程2：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-04 22:52:25 线程1：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-04 22:52:25 线程3：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-04 22:52:25 线程2：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-04 22:52:25 线程1：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-04 22:52:25 线程2：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-04 22:52:25 线程3：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-04 22:52:25 线程1：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-04 22:52:25 线程2：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-04 22:52:25 线程3：[信息] [信息] 已重新设置浏览器标题: 线程3 - AWS注册工具 (进度: 98%)
2025-08-04 22:52:25 线程3：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-04 22:52:25 线程2：[信息] [信息] 已重新设置浏览器标题: 线程2 - AWS注册工具 (进度: 98%)
2025-08-04 22:52:25 线程1：[信息] [信息] 已重新设置浏览器标题: 线程1 - AWS注册工具 (进度: 98%)
2025-08-04 22:52:25 线程1：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-04 22:52:25 线程2：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-04 22:52:25 线程3：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-04 22:52:25 线程3：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-04 22:52:25 线程3：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-04 22:52:25 线程1：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-04 22:52:25 线程1：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-04 22:52:25 线程1：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-04 22:52:25 线程2：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-04 22:52:25 线程2：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-04 22:52:25 线程2：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-04 22:52:56 线程2：[信息] [信息] 注册失败: Timeout 30000ms exceeded.
Call log:
  - navigating to "https://portal.aws.amazon.com/gp/aws/developer/registration/index.html?refid=em_127222&p=free&c=hp&z=1", waiting until "load" (进度: 98%)
2025-08-04 22:52:56 线程2：[信息] [信息] 第一页相关失败，数据保持不动 (进度: 98%)
2025-08-04 22:52:56 [信息] 第一页相关失败，数据保持不动
2025-08-04 22:52:56 线程2：[错误] 账户注册启动失败: <EMAIL>
2025-08-04 22:52:56 线程2：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-04 22:52:58 线程3：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程3 - AWS注册 (进度: 98%)
2025-08-04 22:52:58 线程3：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-04 22:52:58 线程3：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-04 22:52:58 线程3：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-04 22:52:58 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-04 22:52:58 线程3：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-04 22:53:01 线程3：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-04 22:53:01 线程3：[信息] [信息] ⚠️ 检测到错误信息，开始重试机制... (进度: 98%)
2025-08-04 22:53:01 [信息] 检测到错误信息，开始重试机制
2025-08-04 22:53:01 线程3：[信息] [信息] 🔄 第1次重试点击验证邮箱按钮... (进度: 98%)
2025-08-04 22:53:01 [信息] 第1次重试点击验证邮箱按钮
2025-08-04 22:53:03 线程3：[信息] [信息] ✅ 第1次重试：已点击验证邮箱按钮 (进度: 100%)
2025-08-04 22:53:03 [信息] 第1次重试：已点击验证邮箱按钮
2025-08-04 22:53:04 [信息] 多线程窗口引用已清理
2025-08-04 22:53:04 [信息] 主窗口已恢复正常状态，多线程窗口引用已清理
2025-08-04 22:53:04 [信息] 多线程管理窗口正在关闭
2025-08-04 22:53:05 [信息] 程序正在退出，开始清理工作...
2025-08-04 22:53:05 [信息] 程序退出，开始清理所有待拉黑千川手机号码
2025-08-04 22:53:05 [信息] 程序退出清理完成: 没有需要拉黑的千川手机号码
2025-08-04 22:53:05 [信息] 程序退出，开始清理所有待释放手机号码
2025-08-04 22:53:05 [信息] 程序退出清理完成: 没有需要释放的手机号码
2025-08-04 22:53:05 [信息] 程序退出清理工作完成
2025-08-04 22:53:05 线程3：[信息] [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']" (进度: 100%)
2025-08-04 22:53:05 [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']"
2025-08-04 22:53:18 [信息] AWS自动注册工具启动
2025-08-04 22:53:18 [信息] 程序版本: 1.0.0.0
2025-08-04 22:53:18 [信息] 启动时间: 2025-08-04 22:53:18
2025-08-04 22:53:18 [信息] 浏览器模式切换: 无痕 Chrome 模式
2025-08-04 22:53:18 [信息] 线程数量已选择: 1
2025-08-04 22:53:18 [信息] 线程数量选择初始化完成
2025-08-04 22:53:18 [信息] 程序初始化完成
2025-08-04 22:53:22 [按钮操作] 选择文件 -> 打开文件选择对话框
2025-08-04 22:53:25 [信息] 已选择文件: C:\Users\<USER>\Desktop\2025-08-04-智利.txt
2025-08-04 22:53:25 [按钮操作] 加载信息 -> 加载数据文件: C:\Users\<USER>\Desktop\2025-08-04-智利.txt
2025-08-04 22:53:26 [信息] 成功加载 11 条数据
2025-08-04 22:53:29 [信息] 线程数量已选择: 3
2025-08-04 22:53:32 [按钮操作] 开始注册 -> 启动注册流程
2025-08-04 22:53:32 [信息] 开始启动多线程注册，线程数量: 3
2025-08-04 22:53:32 [信息] 开始启动多线程注册，线程数量: 3，数据条数: 11
2025-08-04 22:53:32 [信息] 所有线程已停止并清理
2025-08-04 22:53:32 [信息] 正在初始化多线程服务...
2025-08-04 22:53:32 [信息] 千川手机API服务已初始化
2025-08-04 22:53:32 [信息] 手机号码管理器已初始化，服务商: Qianchuan，将在第一个线程完成第二页后获取手机号码
2025-08-04 22:53:32 [信息] 多线程服务初始化完成
2025-08-04 22:53:32 [信息] 数据分配完成：共11条数据分配给3个线程
2025-08-04 22:53:32 [信息] 线程1分配到4条数据
2025-08-04 22:53:32 [信息] 线程2分配到4条数据
2025-08-04 22:53:32 [信息] 线程3分配到3条数据
2025-08-04 22:53:32 [信息] 屏幕工作区域: 1280x672
2025-08-04 22:53:32 [信息] 线程1窗口布局: 位置(0, 0), 大小(384x200), 列1行1, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-04 22:53:32 [信息] 线程1获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-04 22:53:32 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 22:53:32 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_011, WebGL=ANGLE (NVIDIA GeForce RTX 4070 Direct3D11 vs_5_0 ps_5_0), CPU=2核, RAM=48 GB
2025-08-04 22:53:32 线程1：[信息] 已创建，窗口位置: (0, 0)
2025-08-04 22:53:32 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-04 22:53:32 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-04 22:53:32 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-04 22:53:32 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-04 22:53:32 [信息] 线程1已创建，窗口位置: (0, 0)，指纹: 国家=CL, 时区=America/Santiago
2025-08-04 22:53:32 [信息] 屏幕工作区域: 1280x672
2025-08-04 22:53:32 [信息] 线程2窗口布局: 位置(0, 219), 大小(384x200), 列1行2, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-04 22:53:32 [信息] 线程2获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-04 22:53:32 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 22:53:32 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_008, WebGL=ANGLE (NVIDIA GeForce RTX 4060 Direct3D11 vs_5_0 ps_5_0), CPU=24核, RAM=64 GB
2025-08-04 22:53:32 线程2：[信息] 已创建，窗口位置: (0, 219)
2025-08-04 22:53:32 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-04 22:53:32 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-04 22:53:32 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-04 22:53:32 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-04 22:53:32 [信息] 线程2已创建，窗口位置: (0, 219)，指纹: 国家=CL, 时区=America/Santiago
2025-08-04 22:53:32 [信息] 屏幕工作区域: 1280x672
2025-08-04 22:53:32 [信息] 线程3窗口布局: 位置(0, 438), 大小(384x200), 列1行3, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-04 22:53:32 [信息] 线程3获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-04 22:53:32 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 22:53:32 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_012, WebGL=ANGLE (NVIDIA GeForce RTX 4060 Direct3D11 vs_5_0 ps_5_0), CPU=24核, RAM=8 GB
2025-08-04 22:53:32 线程3：[信息] 已创建，窗口位置: (0, 438)
2025-08-04 22:53:32 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-04 22:53:32 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-04 22:53:32 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-04 22:53:32 [信息] 线程3已创建，窗口位置: (0, 438)，指纹: 国家=CL, 时区=America/Santiago
2025-08-04 22:53:32 [信息] 多线程注册启动成功，共3个线程
2025-08-04 22:53:32 线程1：[信息] 开始启动注册流程
2025-08-04 22:53:32 线程2：[信息] 开始启动注册流程
2025-08-04 22:53:32 线程3：[信息] 开始启动注册流程
2025-08-04 22:53:32 线程1：[信息] 开始启动浏览器: 位置(0, 0), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36
2025-08-04 22:53:32 线程2：[信息] 开始启动浏览器: 位置(0, 219), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36
2025-08-04 22:53:32 线程2：[信息] 启动无痕Chrome浏览器...
2025-08-04 22:53:32 线程1：[信息] 启动无痕Chrome浏览器...
2025-08-04 22:53:32 线程3：[信息] 开始启动浏览器: 位置(0, 438), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36
2025-08-04 22:53:32 线程3：[信息] 启动无痕Chrome浏览器...
2025-08-04 22:53:32 线程2：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-04 22:53:32 线程1：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-04 22:53:32 线程3：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-04 22:53:32 [信息] 多线程管理窗口已初始化
2025-08-04 22:53:32 [信息] UniformGrid列数已更新为: 1
2025-08-04 22:53:32 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 0, 列数: 1
2025-08-04 22:53:32 [信息] 多线程管理窗口已打开
2025-08-04 22:53:32 [信息] 多线程注册启动成功，共3个线程
2025-08-04 22:53:35 [信息] UniformGrid列数已更新为: 1
2025-08-04 22:53:35 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 1, 列数: 1
2025-08-04 22:53:35 线程2：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-04 22:53:35 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-04 22:53:35 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36
2025-08-04 22:53:35 线程2：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-04 22:53:36 [信息] UniformGrid列数已更新为: 1
2025-08-04 22:53:36 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-08-04 22:53:36 线程1：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-04 22:53:36 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-04 22:53:36 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36 Edg/119.0.0.0
2025-08-04 22:53:36 线程1：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-04 22:53:36 [信息] UniformGrid列数已更新为: 2
2025-08-04 22:53:36 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 3, 列数: 2
2025-08-04 22:53:36 线程3：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-04 22:53:36 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-04 22:53:36 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36 Edg/119.0.0.0
2025-08-04 22:53:37 线程3：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-04 22:53:38 线程1：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-04 22:53:38 线程2：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-04 22:53:38 线程3：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-04 22:53:40 线程1：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-04 22:53:40 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-04 22:53:40 线程1：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-04 22:53:40 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 22:53:40 线程1：[信息] [信息] 多线程模式使用指纹地理位置: -29.9027, -71.2519 (进度: 0%)
2025-08-04 22:53:40 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-29.9027, 经度=-71.2519
2025-08-04 22:53:40 线程1：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_011, CPU: 2核 (进度: 0%)
2025-08-04 22:53:40 [信息] 浏览器指纹注入: Canvas=canvas_fp_011, WebGL=ANGLE (NVIDIA GeForce RTX 4070 Direct3D11 vs_5_0 ps_5_0), CPU=2核, RAM=48 GB
2025-08-04 22:53:40 线程2：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-04 22:53:40 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-04 22:53:40 线程2：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-04 22:53:40 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 22:53:40 线程2：[信息] [信息] 多线程模式使用指纹地理位置: -23.6509, -70.3975 (进度: 0%)
2025-08-04 22:53:40 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-23.6509, 经度=-70.3975
2025-08-04 22:53:40 线程2：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_008, CPU: 24核 (进度: 0%)
2025-08-04 22:53:40 [信息] 浏览器指纹注入: Canvas=canvas_fp_008, WebGL=ANGLE (NVIDIA GeForce RTX 4060 Direct3D11 vs_5_0 ps_5_0), CPU=24核, RAM=64 GB
2025-08-04 22:53:40 线程3：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-04 22:53:40 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-04 22:53:40 线程3：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-04 22:53:40 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 22:53:40 线程3：[信息] [信息] 多线程模式使用指纹地理位置: -36.8201, -73.0444 (进度: 0%)
2025-08-04 22:53:40 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-36.8201, 经度=-73.0444
2025-08-04 22:53:41 线程3：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_012, CPU: 24核 (进度: 0%)
2025-08-04 22:53:41 [信息] 浏览器指纹注入: Canvas=canvas_fp_012, WebGL=ANGLE (NVIDIA GeForce RTX 4060 Direct3D11 vs_5_0 ps_5_0), CPU=24核, RAM=8 GB
2025-08-04 22:53:43 线程1：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-04 22:53:43 线程3：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-04 22:53:43 线程2：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-04 22:53:45 线程1：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 2
   • 设备内存: 48 GB
   • 平台信息: Win32
   • Do Not Track: default

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 4070 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (NVIDIA Technologies)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 3A4B5C6D
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_004
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-Y9Z0A1B
   • MAC地址: F0-12-34-56-78-9A
   • 屏幕分辨率: 2057x999
   • 可用区域: 2057x959

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: A7B8C9D0
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: 2g
   • 电池API支持: True
   • 电池电量: 0.32
   • 电池充电状态: True
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-04 22:53:45 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 2    • 设备内存: 48 GB    • 平台信息: Win32    • Do Not Track: default   图形渲染信息:    • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 4070 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (NVIDIA Technologies)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 3A4B5C6D    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_004    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-Y9Z0A1B    • MAC地址: F0-12-34-56-78-9A    • 屏幕分辨率: 2057x999    • 可用区域: 2057x959   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: A7B8C9D0    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: 2g    • 电池API支持: True    • 电池电量: 0.32    • 电池充电状态: True    • 性能API支持: True  ================================
2025-08-04 22:53:46 线程2：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 24
   • 设备内存: 64 GB
   • 平台信息: Win32
   • Do Not Track: undefined

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 4060 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (Advanced Micro Devices, Inc.)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 3C4D5E6F
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_008
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-G5H6I7J
   • MAC地址: AA-BB-CC-DD-EE-FF
   • 屏幕分辨率: 2099x1154
   • 可用区域: 2099x1114

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: E9F0A1B2
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: bluetooth
   • 电池API支持: True
   • 电池电量: 0.25
   • 电池充电状态: True
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-04 22:53:46 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 24    • 设备内存: 64 GB    • 平台信息: Win32    • Do Not Track: undefined   图形渲染信息:    • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 4060 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (Advanced Micro Devices, Inc.)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 3C4D5E6F    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_008    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-G5H6I7J    • MAC地址: AA-BB-CC-DD-EE-FF    • 屏幕分辨率: 2099x1154    • 可用区域: 2099x1114   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: E9F0A1B2    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: bluetooth    • 电池API支持: True    • 电池电量: 0.25    • 电池充电状态: True    • 性能API支持: True  ================================
2025-08-04 22:53:46 线程3：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 24
   • 设备内存: 8 GB
   • 平台信息: Win32
   • Do Not Track: undefined

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 4060 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (NVIDIA Corp.)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 1C2D3E4F
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_005
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-O1P2Q3R
   • MAC地址: 34-56-78-9A-BC-DE
   • 屏幕分辨率: 1764x1023
   • 可用区域: 1764x983

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: D79834F2
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: slow-2g
   • 电池API支持: True
   • 电池电量: 0.77
   • 电池充电状态: True
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-04 22:53:46 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 24    • 设备内存: 8 GB    • 平台信息: Win32    • Do Not Track: undefined   图形渲染信息:    • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 4060 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (NVIDIA Corp.)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 1C2D3E4F    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_005    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-O1P2Q3R    • MAC地址: 34-56-78-9A-BC-DE    • 屏幕分辨率: 1764x1023    • 可用区域: 1764x983   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: D79834F2    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: slow-2g    • 电池API支持: True    • 电池电量: 0.77    • 电池充电状态: True    • 性能API支持: True  ================================
2025-08-04 22:53:46 线程2：[信息] [信息] 已设置浏览器标题: 线程2 - AWS注册工具 (进度: 5%)
2025-08-04 22:53:46 线程2：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-04 22:53:46 线程2：[信息] 浏览器启动成功
2025-08-04 22:53:46 线程2：[信息] 获取下一个数据: <EMAIL>
2025-08-04 22:53:46 线程2：[信息] 开始处理账户: <EMAIL>
2025-08-04 22:53:46 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-04 22:53:46 线程2：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-04 22:53:46 线程2：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-04 22:53:46 线程2：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-04 22:53:46 线程2：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-04 22:53:46 线程3：[信息] [信息] 已设置浏览器标题: 线程3 - AWS注册工具 (进度: 5%)
2025-08-04 22:53:46 线程3：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-04 22:53:46 线程3：[信息] 浏览器启动成功
2025-08-04 22:53:46 线程3：[信息] 获取下一个数据: <EMAIL>
2025-08-04 22:53:47 线程3：[信息] 开始处理账户: <EMAIL>
2025-08-04 22:53:47 线程3：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-04 22:53:47 线程3：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-04 22:53:47 线程3：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-04 22:53:47 线程3：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-04 22:53:47 线程3：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-04 22:53:47 线程1：[信息] [信息] 已设置浏览器标题: 线程1 - AWS注册工具 (进度: 5%)
2025-08-04 22:53:47 线程1：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-04 22:53:47 线程1：[信息] 浏览器启动成功
2025-08-04 22:53:47 线程1：[信息] 获取下一个数据: <EMAIL>
2025-08-04 22:53:48 线程1：[信息] 开始处理账户: <EMAIL>
2025-08-04 22:53:48 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-04 22:53:48 线程1：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-04 22:53:48 线程1：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-04 22:53:48 线程1：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-04 22:53:48 线程1：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-04 22:53:48 线程2：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-04 22:53:48 线程2：[信息] [信息] 已重新设置浏览器标题: 线程2 - AWS注册工具 (进度: 98%)
2025-08-04 22:53:48 线程2：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-04 22:53:48 线程3：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-04 22:53:48 线程3：[信息] [信息] 已重新设置浏览器标题: 线程3 - AWS注册工具 (进度: 98%)
2025-08-04 22:53:48 线程3：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-04 22:53:48 线程2：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-04 22:53:48 线程2：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-04 22:53:48 线程2：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-04 22:53:48 线程3：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-04 22:53:48 线程3：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-04 22:53:48 线程3：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-04 22:53:48 线程1：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-04 22:53:48 线程1：[信息] [信息] 已重新设置浏览器标题: 线程1 - AWS注册工具 (进度: 98%)
2025-08-04 22:53:48 线程1：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-04 22:53:48 线程1：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-04 22:53:48 线程1：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-04 22:53:48 线程1：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-04 22:54:16 线程1：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程1 - AWS注册 (进度: 98%)
2025-08-04 22:54:16 线程1：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-04 22:54:16 线程1：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-04 22:54:16 线程1：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-04 22:54:16 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-04 22:54:16 线程1：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-04 22:54:18 线程3：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程3 - AWS注册 (进度: 98%)
2025-08-04 22:54:18 线程3：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-04 22:54:18 线程3：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-04 22:54:18 线程3：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-04 22:54:18 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-04 22:54:18 线程3：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-04 22:54:19 线程1：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-04 22:54:19 线程1：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-04 22:54:19 线程1：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-04 22:54:19 线程1：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-04 22:54:19 线程1：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 22:54:19 线程1：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-04 22:54:21 线程3：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-04 22:54:21 线程3：[信息] [信息] ⚠️ 检测到错误信息，开始重试机制... (进度: 98%)
2025-08-04 22:54:21 [信息] 检测到错误信息，开始重试机制
2025-08-04 22:54:21 线程3：[信息] [信息] 🔄 第1次重试点击验证邮箱按钮... (进度: 98%)
2025-08-04 22:54:21 [信息] 第1次重试点击验证邮箱按钮
2025-08-04 22:54:21 线程1：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-04 22:54:22 线程1：[信息] [信息] 🔍 第2次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 22:54:22 线程1：[信息] [信息] ✅ 第2次检测发现图形验证码！ (进度: 100%)
2025-08-04 22:54:22 线程1：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-08-04 22:54:22 线程1：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-08-04 22:54:22 线程1：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-08-04 22:54:22 线程1：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 22:54:24 线程3：[信息] [信息] ✅ 第1次重试：已点击验证邮箱按钮 (进度: 100%)
2025-08-04 22:54:24 [信息] 第1次重试：已点击验证邮箱按钮
2025-08-04 22:54:26 线程3：[信息] [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']" (进度: 100%)
2025-08-04 22:54:26 [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']"
2025-08-04 22:54:26 线程3：[信息] [信息] ✅ 第1次重试成功：已到达第二页，继续正常流程 (进度: 100%)
2025-08-04 22:54:26 [信息] 第1次重试成功：已到达第二页
2025-08-04 22:54:26 线程3：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-04 22:54:26 线程3：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-04 22:54:26 线程3：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 22:54:26 线程3：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-04 22:54:28 线程3：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-04 22:54:29 线程3：[信息] [信息] 🔍 第2次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 22:54:29 线程3：[信息] [信息] ✅ 第2次检测发现图形验证码！ (进度: 100%)
2025-08-04 22:54:29 线程3：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-08-04 22:54:29 线程3：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-08-04 22:54:29 线程3：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-08-04 22:54:29 线程3：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 22:54:30 线程1：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 35589 字节 (进度: 100%)
2025-08-04 22:54:30 线程1：[信息] [信息] ✅ 图片验证通过：201x71px，35589字节，复杂度符合要求 (进度: 100%)
2025-08-04 22:54:30 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 22:54:35 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"y3yzx8"},"taskId":"ef875c40-7142-11f0-9768-ba03bdd70631"} (进度: 100%)
2025-08-04 22:54:35 线程1：[信息] [信息] 第一页第1次识别结果: y3yzx8 → 转换为小写: y3yzx8 (进度: 100%)
2025-08-04 22:54:35 线程1：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 22:54:35 线程1：[信息] [信息] 已填入验证码: y3yzx8 (进度: 100%)
2025-08-04 22:54:35 线程1：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 22:54:37 线程1：[信息] [信息] 第一页第1次图形验证码识别成功 (进度: 100%)
2025-08-04 22:54:37 线程1：[信息] [信息] 第一页图形验证码自动完成 (进度: 100%)
2025-08-04 22:54:37 线程1：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-04 22:54:37 线程1：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-04 22:54:37 线程1：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-04 22:54:37 线程1：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-04 22:54:37 线程1：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-04 22:54:37 线程1：[信息] 账户注册流程已启动: <EMAIL>
2025-08-04 22:54:37 线程1：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-04 22:54:37 [信息] [线程1] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-04 22:54:37 [信息] [线程1] 已删除旧的响应文件
2025-08-04 22:54:37 [信息] [线程1] 等待2秒后开始第一次触发...
2025-08-04 22:54:39 [信息] [线程1] 第1次触发邮箱验证码获取...（最多20次）
2025-08-04 22:54:39 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 22:54:39
2025-08-04 22:54:42 线程3：[信息] [信息] 第一页验证码元素等待超时，转为手动模式 (进度: 100%)
2025-08-04 22:54:42 线程3：[信息] [信息] 第一页验证码元素等待超时，直接转为手动模式 (进度: 100%)
2025-08-04 22:54:42 线程3：[信息] [信息] 当前验证码抓取出错，需要手动完成图形验证码，并点击继续注册 (进度: 100%)
2025-08-04 22:54:42 [信息] [线程1] 第2次触发邮箱验证码获取...（最多20次）
2025-08-04 22:54:42 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 22:54:42
2025-08-04 22:54:45 [信息] [线程1] 第3次触发邮箱验证码获取...（最多20次）
2025-08-04 22:54:45 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 22:54:45
2025-08-04 22:54:48 [信息] [线程1] 邮箱验证码获取成功: 843817，立即停止重复请求
2025-08-04 22:54:48 [信息] [线程1] 已清理请求文件，停止重复触发
2025-08-04 22:54:48 [信息] [线程1] 已清理响应文件
2025-08-04 22:54:48 线程1：[信息] [信息] 验证码获取成功: 843817，正在自动填入... (进度: 25%)
2025-08-04 22:54:48 线程1：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 25%)
2025-08-04 22:54:48 线程1：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 25%)
2025-08-04 22:54:48 线程1：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 25%)
2025-08-04 22:54:48 [信息] 线程1完成第二页事件已处理
2025-08-04 22:54:48 [信息] 线程1完成第二页，开始批量获取手机号码...
2025-08-04 22:54:48 线程1：[信息] [信息] 线程1第二页验证完成，已通知管理器 (进度: 35%)
2025-08-04 22:54:48 线程1：[信息] [信息] 千川专用：第二页完成，开始获取手机号码（20秒超时，不重试） (进度: 35%)
2025-08-04 22:54:48 线程1：[信息] [信息] 千川手机号码获取任务已启动，将在第四页等待结果 (进度: 48%)
2025-08-04 22:54:48 [信息] [千川API] 第二页完成后开始获取手机号码（20秒超时，不重试）
2025-08-04 22:54:48 [信息] [千川API] 请求URL: https://api.qc86.shop/api/getPhone?token=170154-*-c774223f-**************-c8bc459d4a6b&channelId=1532842809726341126&operator=5
2025-08-04 22:54:48 [信息] 开始批量获取3个手机号码，服务商: Qianchuan
2025-08-04 22:54:48 [信息] 千川API：不在初始化时获取手机号码，将在各线程第二页完成后获取
2025-08-04 22:54:48 [信息] 批量获取3个手机号码成功
2025-08-04 22:54:51 线程1：[信息] [信息] 等待密码设置页面加载... (进度: 48%)
2025-08-04 22:54:51 线程1：[信息] [信息] 开始填写密码信息... (进度: 48%)
2025-08-04 22:54:55 [信息] [千川API] 响应内容: {"data":{"city":"","code":0,"desc":null,"lastMsgTime":null,"message":null,"minute":null,"mobile":"4367870307429","phoneId":"5369307429269","province":"","refreshTime":5000,"smsTask":{"createTime":null,"endTime":null,"id":5827532,"phoneNo":"4367870307429","projectId":804413,"startTime":"2025-08-04 22:54:53","status":0,"uid":170154,"updateTime":null},"sp":""},"msg":"操作成功","status":200,"success":true,"t":"268ee197-4676-4590-8ebf-9e361320b3aa"}
2025-08-04 22:54:55 [信息] [千川API] 获取手机号码成功: +4367870307429
2025-08-04 22:54:56 线程1：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 48%)
2025-08-04 22:54:57 线程1：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 48%)
2025-08-04 22:54:57 线程1：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 48%)
2025-08-04 22:54:58 线程1：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 48%)
2025-08-04 22:55:01 线程1：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 48%)
2025-08-04 22:55:01 线程1：[信息] [信息] 等待账户类型确认页面加载... (进度: 48%)
2025-08-04 22:55:01 线程1：[信息] [信息] 开始处理账户类型确认... (进度: 48%)
2025-08-04 22:55:08 线程1：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 48%)
2025-08-04 22:55:08 线程1：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 48%)
2025-08-04 22:55:33 线程1：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 48%)
2025-08-04 22:55:33 [警告] 线程1未找到分配的手机号码，服务商: Qianchuan
2025-08-04 22:55:33 线程1：[信息] [信息] 多线程模式：未找到分配的手机号码，尝试后台获取... (进度: 48%)
2025-08-04 22:55:33 线程1：[信息] [信息] 开始后台获取手机号码，同时填写其他信息... (进度: 48%)
2025-08-04 22:55:33 [信息] [千川API] 请求URL: https://api.qc86.shop/api/getPhone?token=170154-*-c774223f-**************-c8bc459d4a6b&channelId=1532842809726341126&operator=5
2025-08-04 22:55:37 [信息] [千川API] 响应内容: {"data":{"city":"","code":0,"desc":null,"lastMsgTime":null,"message":null,"minute":null,"mobile":"4367870312526","phoneId":"5373512526232","province":"","refreshTime":5000,"smsTask":{"createTime":null,"endTime":null,"id":5827535,"phoneNo":"4367870312526","projectId":804413,"startTime":"2025-08-04 22:55:35","status":0,"uid":170154,"updateTime":null},"sp":""},"msg":"操作成功","status":200,"success":true,"t":"949d3fe1-e25f-4b49-912a-235936b8af78"}
2025-08-04 22:55:37 [信息] [千川API] 获取手机号码成功: +4367870312526
2025-08-04 22:55:38 线程1：[信息] [信息] 数据国家代码为CL，需要选择Chile (进度: 48%)
2025-08-04 22:55:38 线程1：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 48%)
2025-08-04 22:55:39 线程3：[信息] [信息] 检测到第一页验证码已完成，继续流程 (进度: 100%)
2025-08-04 22:55:39 线程3：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-04 22:55:39 线程3：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-04 22:55:39 线程3：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-04 22:55:39 线程3：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-04 22:55:39 线程3：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-04 22:55:39 线程3：[信息] 账户注册流程已启动: <EMAIL>
2025-08-04 22:55:39 线程3：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-04 22:55:39 [信息] [线程3] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-04 22:55:39 [信息] [线程3] 等待2秒后开始第一次触发...
2025-08-04 22:55:40 线程1：[信息] [信息] 已选择国家: Chile (进度: 48%)
2025-08-04 22:55:40 线程1：[信息] [信息] 已成功选择国家: Chile (进度: 48%)
2025-08-04 22:55:40 线程1：[信息] [信息] 千川API模式：使用固定的+43奥地利区号 (进度: 48%)
2025-08-04 22:55:40 线程1：[信息] [信息] 正在选择国家代码 +43 (奥地利 (Austria) +43)... (进度: 48%)
2025-08-04 22:55:40 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 48%)
2025-08-04 22:55:41 [信息] [线程3] 第1次触发邮箱验证码获取...（最多20次）
2025-08-04 22:55:41 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-04 22:55:41
2025-08-04 22:55:44 [信息] [线程3] 第2次触发邮箱验证码获取...（最多20次）
2025-08-04 22:55:44 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-04 22:55:44
2025-08-04 22:55:47 [信息] [线程3] 第3次触发邮箱验证码获取...（最多20次）
2025-08-04 22:55:47 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-04 22:55:47
2025-08-04 22:55:49 [信息] [线程3] 邮箱验证码获取成功: 668768，立即停止重复请求
2025-08-04 22:55:49 [信息] [线程3] 已清理请求文件，停止重复触发
2025-08-04 22:55:49 [信息] [线程3] 已清理响应文件
2025-08-04 22:55:49 线程3：[信息] [信息] 验证码获取成功: 668768，正在自动填入... (进度: 25%)
2025-08-04 22:55:49 线程3：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 25%)
2025-08-04 22:55:50 线程3：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 25%)
2025-08-04 22:55:50 线程3：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 25%)
2025-08-04 22:55:50 [信息] 线程3完成第二页事件已处理
2025-08-04 22:55:50 [信息] 线程3完成第二页，手机号码已获取，无需重复获取
2025-08-04 22:55:50 线程3：[信息] [信息] 线程3第二页验证完成，已通知管理器 (进度: 35%)
2025-08-04 22:55:50 线程3：[信息] [信息] 千川专用：第二页完成，开始获取手机号码（20秒超时，不重试） (进度: 35%)
2025-08-04 22:55:50 线程3：[信息] [信息] 千川手机号码获取任务已启动，将在第四页等待结果 (进度: 48%)
2025-08-04 22:55:50 [信息] [千川API] 第二页完成后开始获取手机号码（20秒超时，不重试）
2025-08-04 22:55:50 [信息] [千川API] 请求URL: https://api.qc86.shop/api/getPhone?token=170154-*-c774223f-**************-c8bc459d4a6b&channelId=1532842809726341126&operator=5
2025-08-04 22:55:53 线程3：[信息] [信息] 等待密码设置页面加载... (进度: 48%)
2025-08-04 22:55:53 线程3：[信息] [信息] 开始填写密码信息... (进度: 48%)
2025-08-04 22:55:54 线程3：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 48%)
2025-08-04 22:55:55 线程3：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 48%)
2025-08-04 22:55:55 线程3：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 48%)
2025-08-04 22:55:56 线程3：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 48%)
2025-08-04 22:55:57 [信息] [千川API] 响应内容: {"data":{"city":"","code":0,"desc":null,"lastMsgTime":null,"message":null,"minute":null,"mobile":"4367870310704","phoneId":"5375510704272","province":"","refreshTime":5000,"smsTask":{"createTime":null,"endTime":null,"id":5827539,"phoneNo":"4367870310704","projectId":804413,"startTime":"2025-08-04 22:55:55","status":0,"uid":170154,"updateTime":null},"sp":""},"msg":"操作成功","status":200,"success":true,"t":"23d3263e-c0d8-4822-a505-7d400f7df476"}
2025-08-04 22:55:57 [信息] [千川API] 获取手机号码成功: +4367870310704
2025-08-04 22:55:59 线程3：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 48%)
2025-08-04 22:55:59 线程3：[信息] [信息] 等待账户类型确认页面加载... (进度: 48%)
2025-08-04 22:55:59 线程3：[信息] [信息] 开始处理账户类型确认... (进度: 48%)
2025-08-04 22:56:08 线程2：[信息] [信息] 注册失败: Target page, context or browser has been closed (进度: 98%)
2025-08-04 22:56:08 线程2：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：0Q088N038WY ③AWS密码：e6l1HngP ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息： (进度: 98%)
2025-08-04 22:56:08 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：0Q088N038WY ③AWS密码：e6l1HngP ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 22:56:08 线程2：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：0Q088N038WY ③AWS密码：e6l1HngP ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 22:56:08 [信息] 线程2请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：0Q088N038WY ③AWS密码：e6l1HngP ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 22:56:08 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：0Q088N038WY ③AWS密码：e6l1HngP ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 22:56:08 [信息] 线程2剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：0Q088N038WY ③AWS密码：e6l1HngP ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 22:56:08 线程2：[错误] 账户注册启动失败: <EMAIL>
2025-08-04 22:56:08 线程2：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-04 22:56:11 线程3：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 48%)
2025-08-04 22:56:11 线程3：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 48%)
2025-08-04 22:56:13 线程1：[信息] [信息] 自动选择国家代码失败，请手动选择+43 (进度: 48%)
2025-08-04 22:56:14 线程1：[信息] [信息] 千川专用：第四页开始处理手机号码 (进度: 48%)
2025-08-04 22:56:14 线程1：[信息] [信息] 等待千川手机号码获取结果（最多20秒）... (进度: 48%)
2025-08-04 22:56:14 线程1：[信息] [信息] 千川手机号码获取成功: +4367870307429 (进度: 48%)
2025-08-04 22:56:14 线程1：[信息] [信息] 千川手机号码已自动填入: +4367870307429 (进度: 48%)
2025-08-04 22:56:15 线程1：[信息] [信息] 使用已保存的手机号码: 67870307429 (进度: 48%)
2025-08-04 22:56:16 线程1：[信息] [信息] 联系信息完成，等待页面加载... (进度: 48%)
2025-08-04 22:56:19 线程1：[信息] [信息] 进入付款信息页面... (进度: 48%)
2025-08-04 22:56:28 线程1：[信息] [信息] 所有自动线程已停止 (进度: 48%)
2025-08-04 22:56:28 线程1：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 48%)
2025-08-04 22:56:28 线程1：[信息] 已暂停
2025-08-04 22:56:28 [信息] 线程1已暂停
2025-08-04 22:56:28 [信息] 线程1已暂停
2025-08-04 22:56:36 线程3：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 48%)
2025-08-04 22:56:36 [警告] 线程3未找到分配的手机号码，服务商: Qianchuan
2025-08-04 22:56:36 线程3：[信息] [信息] 多线程模式：未找到分配的手机号码，尝试后台获取... (进度: 48%)
2025-08-04 22:56:36 线程3：[信息] [信息] 开始后台获取手机号码，同时填写其他信息... (进度: 48%)
2025-08-04 22:56:36 [信息] [千川API] 请求URL: https://api.qc86.shop/api/getPhone?token=170154-*-c774223f-**************-c8bc459d4a6b&channelId=1532842809726341126&operator=5
2025-08-04 22:56:38 [信息] [千川API] 响应内容: {"data":null,"msg":"{\"status\":\"ERROR\",\"message\":\"too many requests\"}","status":500,"success":false,"t":"b0694266-cf65-4cc2-a865-cf0b96ecbc67"}
2025-08-04 22:56:38 [错误] [千川API] 获取手机号码失败: {"status":"ERROR","message":"too many requests"}
2025-08-04 22:56:38 线程3：[信息] [信息] 后台获取手机号码失败: 获取手机号码失败: {"status":"ERROR","message":"too many requests"} (进度: 48%)
2025-08-04 22:56:38 线程3：[信息] [信息] 数据国家代码为CL，需要选择Chile (进度: 48%)
2025-08-04 22:56:39 线程3：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 48%)
2025-08-04 22:56:40 线程3：[信息] [信息] 已选择国家: Chile (进度: 48%)
2025-08-04 22:56:40 线程3：[信息] [信息] 已成功选择国家: Chile (进度: 48%)
2025-08-04 22:56:40 线程3：[信息] [信息] 千川API模式：使用固定的+43奥地利区号 (进度: 48%)
2025-08-04 22:56:40 线程3：[信息] [信息] 正在选择国家代码 +43 (奥地利 (Austria) +43)... (进度: 48%)
2025-08-04 22:56:41 线程3：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 48%)
2025-08-04 22:56:42 线程3：[信息] [信息] 已选择国家代码 +43 (进度: 48%)
2025-08-04 22:56:43 线程1：[信息] [信息] 正在选择月份: July (进度: 48%)
2025-08-04 22:56:44 线程1：[信息] [信息] 已选择月份（标准选项）: July (进度: 48%)
2025-08-04 22:56:44 线程1：[信息] [信息]  继续注册被调用，当前状态: Paused，当前步骤: 5 (进度: 48%)
2025-08-04 22:56:44 线程1：[信息] [信息]  进行智能页面检测... (进度: 48%)
2025-08-04 22:56:44 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 48%)
2025-08-04 22:56:44 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 48%)
2025-08-04 22:56:44 线程1：[信息] [信息] 🎯 找到匹配按钮: 'Verify and continue (step 3 of 5)' → 第5页 (进度: 48%)
2025-08-04 22:56:44 线程1：[信息] [信息] ✅ 直接确认为第5页 (进度: 100%)
2025-08-04 22:56:44 线程1：[信息] [信息]  智能检测到当前在第5页 (进度: 100%)
2025-08-04 22:56:44 线程1：[信息] [信息] 智能检测到当前在第5页，开始智能处理... (进度: 100%)
2025-08-04 22:56:44 线程1：[信息] [信息] 正在选择年份: 2029 (进度: 100%)
2025-08-04 22:56:45 线程3：[信息] [信息] 千川专用：第四页开始处理手机号码 (进度: 48%)
2025-08-04 22:56:45 线程3：[信息] [信息] 等待千川手机号码获取结果（最多20秒）... (进度: 48%)
2025-08-04 22:56:45 线程3：[信息] [信息] 千川手机号码获取成功: +4367870310704 (进度: 48%)
2025-08-04 22:56:45 线程3：[信息] [信息] 千川手机号码已自动填入: +4367870310704 (进度: 48%)
2025-08-04 22:56:46 线程1：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-08-04 22:56:46 线程1：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 100%)
2025-08-04 22:56:46 线程1：[信息] 已暂停
2025-08-04 22:56:46 [信息] 线程1已暂停
2025-08-04 22:56:46 [信息] 线程1已暂停
2025-08-04 22:56:46 线程3：[信息] [信息] 使用已保存的手机号码: 67870310704 (进度: 48%)
2025-08-04 22:56:47 线程1：[信息] [信息] 标准选项方法失败，尝试其他方法... (进度: 100%)
2025-08-04 22:56:47 线程3：[信息] [信息] 联系信息完成，等待页面加载... (进度: 48%)
2025-08-04 22:56:47 线程1：[信息] [信息]  继续注册被调用，当前状态: Paused，当前步骤: 5 (进度: 100%)
2025-08-04 22:56:47 线程1：[信息] [信息]  进行智能页面检测... (进度: 100%)
2025-08-04 22:56:47 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-04 22:56:47 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-04 22:56:48 线程1：[信息] [信息] 🎯 找到匹配按钮: 'Verify and continue (step 3 of 5)' → 第5页 (进度: 100%)
2025-08-04 22:56:48 线程1：[信息] [信息] ✅ 直接确认为第5页 (进度: 100%)
2025-08-04 22:56:48 线程1：[信息] [信息]  智能检测到当前在第5页 (进度: 100%)
2025-08-04 22:56:48 线程1：[信息] [信息] 智能检测到当前在第5页，开始智能处理... (进度: 100%)
2025-08-04 22:56:49 线程1：[信息] [信息] 下拉菜单上下文方法失败，尝试精确匹配... (进度: 100%)
2025-08-04 22:56:50 线程3：[信息] [信息] 进入付款信息页面... (进度: 48%)
2025-08-04 22:56:51 线程1：[信息] [信息] 精确匹配方法失败，尝试通用方法... (进度: 100%)
2025-08-04 22:56:51 线程3：[信息] [信息] 正在选择月份: September (进度: 48%)
2025-08-04 22:56:51 线程1：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-08-04 22:56:51 线程1：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 100%)
2025-08-04 22:56:51 线程1：[信息] 已暂停
2025-08-04 22:56:51 [信息] 线程1已暂停
2025-08-04 22:56:51 [信息] 线程1已暂停
2025-08-04 22:56:51 线程3：[信息] [信息] 已选择月份（标准选项）: September (进度: 48%)
2025-08-04 22:56:52 线程3：[信息] [信息] 正在选择年份: 2028 (进度: 48%)
2025-08-04 22:56:52 线程3：[信息] [信息] 已选择年份（标准选项）: 2028 (进度: 48%)
2025-08-04 22:56:53 线程3：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 48%)
2025-08-04 22:56:53 线程3：[信息] [信息] 开始填写验证码验证页面... (进度: 48%)
2025-08-04 22:56:53 线程3：[信息] [信息] 千川API模式：使用固定的+43奥地利区号 (进度: 48%)
2025-08-04 22:56:53 线程3：[信息] [信息] 正在选择国家代码 +43 (奥地利 (Austria) +43)... (进度: 48%)
2025-08-04 22:57:00 线程3：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 48%)
2025-08-04 22:57:01 线程3：[信息] [信息] 已选择国家代码: +43 (进度: 48%)
2025-08-04 22:57:01 线程3：[信息] [信息] 已清空并重新填写手机号码: 67870310704 (进度: 48%)
2025-08-04 22:57:01 线程3：[信息] [信息] 已点击发送验证码按钮 (进度: 48%)
2025-08-04 22:57:03 线程3：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 48%)
2025-08-04 22:57:03 线程1：[信息] [信息] 已选择年份（回退方法）: 2029 (进度: 100%)
2025-08-04 22:57:03 线程3：[信息] [信息] ⚠️ 检测到错误信息，开始重试机制... (进度: 48%)
2025-08-04 22:57:03 [信息] 检测到错误信息，开始重试机制
2025-08-04 22:57:03 线程3：[信息] [信息] 🔄 第1次重试发送验证码按钮... (进度: 48%)
2025-08-04 22:57:03 [信息] 第1次重试发送验证码按钮
2025-08-04 22:57:04 线程1：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 100%)
2025-08-04 22:57:04 线程1：[信息] [信息] 开始填写验证码验证页面... (进度: 100%)
2025-08-04 22:57:04 线程1：[信息] [信息] 千川API模式：使用固定的+43奥地利区号 (进度: 100%)
2025-08-04 22:57:04 线程1：[信息] [信息] 正在选择国家代码 +43 (奥地利 (Austria) +43)... (进度: 100%)
2025-08-04 22:57:06 线程3：[信息] [信息] ✅ 第1次重试：已点击发送验证码按钮 (进度: 100%)
2025-08-04 22:57:06 [信息] 第1次重试：已点击发送验证码按钮
2025-08-04 22:57:08 线程3：[信息] [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']" (进度: 100%)
2025-08-04 22:57:08 [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']"
2025-08-04 22:57:08 线程3：[信息] [信息] ✅ 第1次重试成功：错误信息消失，继续正常流程 (进度: 100%)
2025-08-04 22:57:08 [信息] 第1次重试成功：错误信息消失
2025-08-04 22:57:08 线程3：[信息] [信息] 图形验证码自动模式：开始自动处理图形验证码... (进度: 100%)
2025-08-04 22:57:08 线程3：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-04 22:57:10 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-04 22:57:11 线程3：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-04 22:57:11 线程3：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 22:57:12 线程1：[信息] [信息] 已选择国家代码: +43 (进度: 100%)
2025-08-04 22:57:12 线程1：[信息] [信息] 检测到注册已暂停或终止，停止第六页手机验证处理 (进度: 100%)
2025-08-04 22:57:15 线程1：[信息] [信息] 继续注册失败: 第五页执行失败: Timeout 30000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Button, new() { Name = "Expiration date Month" }) (进度: 100%)
2025-08-04 22:57:15 线程1：[信息] 已继续
2025-08-04 22:57:15 [信息] 线程1已继续
2025-08-04 22:57:18 线程1：[信息] [信息] 继续注册失败: 第五页执行失败: Timeout 30000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Button, new() { Name = "Expiration date Month" }) (进度: 100%)
2025-08-04 22:57:18 线程1：[信息] 已继续
2025-08-04 22:57:18 [信息] 线程1已继续
2025-08-04 22:57:20 线程1：[信息] [信息]  继续注册被调用，当前状态: Error，当前步骤: 6 (进度: 100%)
2025-08-04 22:57:20 线程1：[信息] [信息]  进行智能页面检测... (进度: 100%)
2025-08-04 22:57:20 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-04 22:57:20 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-04 22:57:20 线程1：[信息] [信息] 🎯 找到匹配按钮: 'Send SMS (step 4 of 5)' → 第6页 (进度: 100%)
2025-08-04 22:57:20 线程1：[信息] [信息] 🔍 疑似第6页，进行二次确认... (进度: 100%)
2025-08-04 22:57:20 线程1：[信息] [信息] ✅ 确认为第6页：找到Mobile phone number输入框 (进度: 100%)
2025-08-04 22:57:20 线程1：[信息] [信息]  智能检测到当前在第6页 (进度: 100%)
2025-08-04 22:57:20 线程1：[信息] [信息] 检测当前页面状态... (进度: 100%)
2025-08-04 22:57:20 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-04 22:57:20 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-04 22:57:20 线程1：[信息] [信息] 🎯 找到匹配按钮: 'Send SMS (step 4 of 5)' → 第6页 (进度: 100%)
2025-08-04 22:57:20 线程1：[信息] [信息] 🔍 疑似第6页，进行二次确认... (进度: 100%)
2025-08-04 22:57:20 线程1：[信息] [信息] ✅ 确认为第6页：找到Mobile phone number输入框 (进度: 100%)
2025-08-04 22:57:20 线程1：[信息] [信息] 智能检测到当前在第6页，继续执行... (进度: 100%)
2025-08-04 22:57:20 线程1：[信息] [信息] 开始填写验证码验证页面... (进度: 100%)
2025-08-04 22:57:20 线程1：[信息] [信息] 千川API模式：使用固定的+43奥地利区号 (进度: 100%)
2025-08-04 22:57:20 线程1：[信息] [信息] 正在选择国家代码 +43 (奥地利 (Austria) +43)... (进度: 100%)
2025-08-04 22:57:20 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-04 22:57:21 线程1：[信息] [信息] 已选择国家代码: +43 (进度: 100%)
2025-08-04 22:57:22 线程1：[信息] [信息] 已清空并重新填写手机号码: 67870307429 (进度: 100%)
2025-08-04 22:57:22 线程1：[信息] [信息] 已点击发送验证码按钮 (进度: 100%)
2025-08-04 22:57:24 线程1：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 100%)
2025-08-04 22:57:24 线程1：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-08-04 22:57:24 线程1：[信息] [信息] 图形验证码自动模式：开始自动处理图形验证码... (进度: 100%)
2025-08-04 22:57:24 线程1：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-04 22:57:24 线程3：[信息] [信息] 第六页验证码元素等待超时，转为手动模式 (进度: 100%)
2025-08-04 22:57:24 线程3：[信息] [信息] 第六页第1次识别异常: 验证码元素等待超时，需要手动处理 (进度: 100%)
2025-08-04 22:57:25 线程3：[信息] [信息] 第六页验证码元素等待超时，直接转为手动模式 (进度: 100%)
2025-08-04 22:57:25 线程3：[信息] [信息] 当前验证码抓取出错，需要手动完成图形验证码，并点击继续注册 (进度: 100%)
2025-08-04 22:57:25 线程3：[信息] [信息] 图形验证码处理完成，请手动输入手机验证码... (进度: 100%)
2025-08-04 22:57:27 线程1：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-04 22:57:27 线程1：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 22:57:31 线程1：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 278 字节 (进度: 100%)
2025-08-04 22:57:31 线程1：[信息] [信息] ❌ 图片文件太小 (278 字节)，可能不是验证码 (进度: 100%)
2025-08-04 22:57:31 线程1：[信息] [信息] 第六页捕获的图片不符合验证码特征，跳过此次尝试（不计入重试次数） (进度: 100%)
2025-08-04 22:57:33 线程1：[信息] [信息] 第六页图片验证失败，第1次图片不符合验证码特征 (进度: 100%)
2025-08-04 22:57:33 线程1：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 22:57:36 线程1：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 278 字节 (进度: 100%)
2025-08-04 22:57:36 线程1：[信息] [信息] ❌ 图片文件太小 (278 字节)，可能不是验证码 (进度: 100%)
2025-08-04 22:57:36 线程1：[信息] [信息] 第六页捕获的图片不符合验证码特征，跳过此次尝试（不计入重试次数） (进度: 100%)
2025-08-04 22:57:38 线程1：[信息] [信息] 第六页图片验证失败，第2次图片不符合验证码特征 (进度: 100%)
2025-08-04 22:57:38 线程1：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 22:57:41 线程1：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 278 字节 (进度: 100%)
2025-08-04 22:57:41 线程1：[信息] [信息] ❌ 图片文件太小 (278 字节)，可能不是验证码 (进度: 100%)
2025-08-04 22:57:41 线程1：[信息] [信息] 第六页捕获的图片不符合验证码特征，跳过此次尝试（不计入重试次数） (进度: 100%)
2025-08-04 22:57:43 线程1：[信息] [信息] 第六页图片验证失败，第3次图片不符合验证码特征 (进度: 100%)
2025-08-04 22:57:43 线程1：[信息] [信息] 第六页连续3次图片不符合验证码特征，转为手动模式 (进度: 100%)
2025-08-04 22:57:43 线程1：[信息] [信息] 当前验证码抓取出错，需要手动完成图形验证码，并点击继续注册 (进度: 100%)
2025-08-04 22:57:43 线程1：[信息] [信息] 图形验证码处理完成，请手动输入手机验证码... (进度: 100%)
2025-08-04 22:57:43 线程1：[信息] 已继续
2025-08-04 22:57:43 [信息] 线程1已继续
2025-08-04 22:58:16 线程1：[信息] [信息]  继续注册被调用，当前状态: WaitingForSMSVerification，当前步骤: 6 (进度: 100%)
2025-08-04 22:58:16 线程1：[信息] [信息] 第六页手动模式继续注册，检测当前页面状态... (进度: 100%)
2025-08-04 22:58:16 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-04 22:58:16 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-04 22:58:17 线程1：[信息] [信息] 🎯 找到匹配按钮: 'Continue (step 4 of 5)' → 第7页 (进度: 100%)
2025-08-04 22:58:17 线程1：[信息] [信息] ✅ 直接确认为第7页 (进度: 100%)
2025-08-04 22:58:17 线程1：[信息] [信息] 检测到已跳转到第七页，更新步骤并执行第七页逻辑 (进度: 100%)
2025-08-04 22:58:17 线程1：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-04 22:58:17 线程1：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 22:58:17 线程1：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 22:58:17 线程1：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-08-04 22:58:22 线程1：[信息] [信息] 线程1开始独立获取验证码... (进度: 100%)
2025-08-04 22:58:22 线程1：[信息] [信息] 线程1等待5秒后开始获取千川API验证码... (进度: 100%)
2025-08-04 22:58:27 线程1：[信息] [信息] 线程1第1次尝试获取千川验证码...（剩余9次尝试） (进度: 100%)
2025-08-04 22:58:27 [信息] [千川API] 获取验证码，尝试 1/4
2025-08-04 22:58:27 [信息] [千川API] 获取验证码请求: 4367870307429
2025-08-04 22:58:29 线程3：[信息] [信息]  继续注册被调用，当前状态: WaitingForSMSVerification，当前步骤: 6 (进度: 100%)
2025-08-04 22:58:29 线程3：[信息] [信息] 第六页手动模式继续注册，检测当前页面状态... (进度: 100%)
2025-08-04 22:58:29 线程3：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-04 22:58:29 线程3：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-04 22:58:29 线程3：[信息] [信息] 🎯 找到匹配按钮: 'Continue (step 4 of 5)' → 第7页 (进度: 100%)
2025-08-04 22:58:29 线程3：[信息] [信息] ✅ 直接确认为第7页 (进度: 100%)
2025-08-04 22:58:29 线程3：[信息] [信息] 检测到已跳转到第七页，更新步骤并执行第七页逻辑 (进度: 100%)
2025-08-04 22:58:29 线程3：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-04 22:58:29 线程3：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 22:58:29 线程3：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 22:58:29 线程3：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-08-04 22:58:29 [信息] [千川API] 验证码响应: {"data":{"code":"8567","message":"ok","modle":"[AMAZON] 8567"},"msg":"操作成功","status":200,"success":true,"t":"ff4725e3-2f75-4007-add9-2a4b004f6a9d"}
2025-08-04 22:58:29 [信息] [千川API] 获取验证码成功: 8567
2025-08-04 22:58:29 线程1：[信息] [信息] 线程1千川验证码获取成功: 8567 (进度: 100%)
2025-08-04 22:58:29 线程1：[信息] [信息] 线程1验证码获取成功: 8567，立即填入验证码... (进度: 100%)
2025-08-04 22:58:29 [信息] 线程1手机号码已加入释放队列: +4367870307429 (原因: 验证码获取成功)
2025-08-04 22:58:29 [信息] 定时检查发现1个待释放手机号码，开始批量释放
2025-08-04 22:58:29 [错误] 手机API服务未设置，无法释放手机号码
2025-08-04 22:58:29 [信息] 定时批量释放完成: 手机API服务未设置
2025-08-04 22:58:29 线程1：[信息] [信息] 第七页：找到验证码输入框 (Verify code) (进度: 100%)
2025-08-04 22:58:29 线程1：[信息] [信息] 线程1已自动填入手机验证码: 8567 (进度: 100%)
2025-08-04 22:58:30 线程1：[信息] [信息] 线程1正在自动点击Continue按钮... (进度: 100%)
2025-08-04 22:58:30 线程1：[信息] [信息] 线程1手机验证码验证完成，继续执行后续步骤... (进度: 100%)
2025-08-04 22:58:33 线程1：[信息] [信息] 检测到无资格错误提示: You are not eligible for new customer credits (进度: 100%)
2025-08-04 22:58:33 线程1：[信息] [信息] 线程1检测到卡号已被关联错误，注册失败 (进度: 100%)
2025-08-04 22:58:33 线程1：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：Y71sRQyh5QiO ③AWS密码：Zp7GiX9P ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联 (进度: 100%)
2025-08-04 22:58:33 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：Y71sRQyh5QiO ③AWS密码：Zp7GiX9P ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-04 22:58:33 线程1：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：Y71sRQyh5QiO ③AWS密码：Zp7GiX9P ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-04 22:58:33 [信息] 线程1请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：Y71sRQyh5QiO ③AWS密码：Zp7GiX9P ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-04 22:58:33 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：Y71sRQyh5QiO ③AWS密码：Zp7GiX9P ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-04 22:58:33 [信息] 线程1剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：Y71sRQyh5QiO ③AWS密码：Zp7GiX9P ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-04 22:58:33 线程1：[信息] 收到失败数据保存请求: 卡号已被关联, 数据: <EMAIL>
2025-08-04 22:58:33 [信息] 线程1请求保存失败数据: 卡号已被关联, 数据: <EMAIL>
2025-08-04 22:58:34 [信息] 已处理失败数据: <EMAIL>, 失败原因: 卡号已被关联
2025-08-04 22:58:34 [信息] 线程1失败数据已保存: 卡号已被关联, 数据: <EMAIL>
2025-08-04 22:58:34 线程1：[信息] [信息] 已通知保存失败数据，失败原因: 卡号已被关联 (进度: 0%)
2025-08-04 22:58:34 线程1：[信息] [信息] 线程1注册失败，数据已归类，注册流程终止 (进度: 0%)
2025-08-04 22:58:34 线程1：[信息] 已继续
2025-08-04 22:58:34 [信息] 线程1已继续
2025-08-04 22:58:34 线程3：[信息] [信息] 线程3开始独立获取验证码... (进度: 100%)
2025-08-04 22:58:34 线程3：[信息] [信息] 线程3等待5秒后开始获取千川API验证码... (进度: 100%)
2025-08-04 22:58:39 线程3：[信息] [信息] 线程3第1次尝试获取千川验证码...（剩余9次尝试） (进度: 100%)
2025-08-04 22:58:39 [信息] [千川API] 获取验证码，尝试 1/4
2025-08-04 22:58:39 [信息] [千川API] 获取验证码请求: 4367870310704
2025-08-04 22:58:40 [信息] [千川API] 验证码响应: {"data":{"code":"7743","message":"ok","modle":"[AMAZON] 7743"},"msg":"操作成功","status":200,"success":true,"t":"626f92a7-1e5a-4e4d-8f8e-7f7e1cbc0010"}
2025-08-04 22:58:40 [信息] [千川API] 获取验证码成功: 7743
2025-08-04 22:58:40 线程3：[信息] [信息] 线程3千川验证码获取成功: 7743 (进度: 100%)
2025-08-04 22:58:40 线程3：[信息] [信息] 线程3验证码获取成功: 7743，立即填入验证码... (进度: 100%)
2025-08-04 22:58:40 [信息] 线程3手机号码已加入释放队列: +4367870310704 (原因: 验证码获取成功)
2025-08-04 22:58:40 线程3：[信息] [信息] 第七页：找到验证码输入框 (Verify code) (进度: 100%)
2025-08-04 22:58:40 线程3：[信息] [信息] 线程3已自动填入手机验证码: 7743 (进度: 100%)
2025-08-04 22:58:41 线程3：[信息] [信息] 线程3正在自动点击Continue按钮... (进度: 100%)
2025-08-04 22:58:41 线程3：[信息] [信息] 线程3手机验证码验证完成，继续执行后续步骤... (进度: 100%)
2025-08-04 22:58:44 线程3：[信息] [信息] 验证码验证完成，等待页面加载... (进度: 100%)
2025-08-04 22:58:48 线程3：[信息] [信息] 已点击完成注册按钮，正在处理... (进度: 100%)
2025-08-04 22:58:49 线程3：[信息] [信息] 等待完成注册页面加载... (进度: 100%)
2025-08-04 22:58:52 线程3：[信息] [信息] 完成注册页面加载完成 (进度: 100%)
2025-08-04 22:58:52 线程3：[信息] [信息] 🔍 正在查找'Go to the AWS Management Console'链接... (进度: 100%)
2025-08-04 22:58:58 线程3：[信息] [信息] 🖱️ 已点击跳转管理控制台链接 (进度: 100%)
2025-08-04 22:58:58 线程3：[信息] [信息] 🔗 通过导航进入IAM安全凭证页面... (进度: 100%)
2025-08-04 22:58:58 线程3：[信息] [信息] ⏳ 等待管理控制台页面加载，智能检测'更多'按钮（超时20秒）... (进度: 100%)
2025-08-04 22:58:59 [信息] 定时检查发现1个待释放手机号码，开始批量释放
2025-08-04 22:58:59 [错误] 手机API服务未设置，无法释放手机号码
2025-08-04 22:58:59 [信息] 定时批量释放完成: 手机API服务未设置
2025-08-04 22:59:18 线程3：[信息] [信息] ⚠️ 20秒内未找到'更多'按钮，继续执行后续流程... (进度: 100%)
2025-08-04 22:59:18 [信息] 20秒内未找到'更多'按钮，但继续执行
2025-08-04 22:59:19 线程3：[信息] [信息] 🔍 智能检测更多按钮... (进度: 100%)
2025-08-04 22:59:19 线程3：[信息] [信息] 🔍 第1次检查更多按钮... (进度: 100%)
2025-08-04 22:59:19 线程3：[信息] [信息] ✅ 第1次检查成功找到更多按钮 (进度: 100%)
2025-08-04 22:59:19 [信息] 第1次检查成功找到更多按钮
2025-08-04 22:59:19 线程3：[信息] [信息] 🖱️ 正在点击更多按钮... (进度: 100%)
2025-08-04 22:59:20 线程3：[信息] [信息] ✅ 成功点击更多按钮 (进度: 100%)
2025-08-04 22:59:20 [信息] 成功点击更多按钮
2025-08-04 22:59:21 线程3：[信息] [信息] 🖱️ 正在点击账户信息按钮... (进度: 100%)
2025-08-04 22:59:21 线程3：[信息] [信息] ✅ 成功点击账户信息按钮 (进度: 100%)
2025-08-04 22:59:21 [信息] 成功点击账户信息按钮
2025-08-04 22:59:22 线程3：[信息] [信息] 🖱️ 正在点击'安全凭证'菜单项... (进度: 100%)
2025-08-04 22:59:22 线程3：[信息] [信息] 🔍 正在定位'安全凭证'链接... (进度: 100%)
2025-08-04 22:59:22 线程3：[信息] [信息] ✅ 成功定位到'安全凭证'链接 (进度: 100%)
2025-08-04 22:59:22 [信息] 成功定位到'安全凭证'链接
2025-08-04 22:59:30 线程3：[信息] [信息] ✅ 成功点击'安全凭证'菜单项 (进度: 100%)
2025-08-04 22:59:30 [信息] 成功点击'安全凭证'链接
2025-08-04 22:59:30 线程3：[信息] [信息] ⏳ 等待IAM安全凭证页面加载（20秒超时，循环检测账单问题和创建密钥按钮）... (进度: 100%)
2025-08-04 22:59:45 线程3：[信息] [信息] ✅ 找到'创建访问密钥'按钮，页面加载完成 (进度: 100%)
2025-08-04 22:59:45 线程3：[信息] [信息] ✅ 页面状态正常，找到创建密钥按钮，开始密钥提取流程 (进度: 100%)
2025-08-04 22:59:45 线程3：[信息] [信息] 🔍 设置页面缩放为50%... (进度: 100%)
2025-08-04 22:59:46 线程3：[信息] [信息] ✅ 页面缩放设置完成 (进度: 100%)
2025-08-04 22:59:46 [信息] 页面缩放设置为50%完成
2025-08-04 22:59:46 线程3：[信息] [信息] 🔍 正在查找并点击'下一步'按钮关闭向导... (进度: 100%)
2025-08-04 22:59:46 [信息] 开始查找并点击'下一步'按钮关闭向导
2025-08-04 22:59:46 线程3：[信息] [信息] ℹ️ 未检测到向导元素，向导可能已自动关闭 (进度: 100%)
2025-08-04 22:59:46 [信息] 未检测到向导元素，向导可能已自动关闭
2025-08-04 22:59:46 线程3：[信息] [信息] 🔑 第四阶段：开始创建和复制访问密钥 (进度: 100%)
2025-08-04 22:59:46 [信息] 开始创建和复制访问密钥
2025-08-04 22:59:46 线程3：[信息] [信息] 🔑 正在点击'创建访问密钥'按钮... (进度: 100%)
2025-08-04 22:59:46 线程3：[信息] [信息] 🔍 正在定位'创建访问密钥'按钮... (进度: 100%)
2025-08-04 22:59:46 线程3：[信息] [信息] ✅ 成功定位到'创建访问密钥'按钮 (进度: 100%)
2025-08-04 22:59:46 [信息] 成功定位到'创建访问密钥'按钮
2025-08-04 22:59:47 线程3：[信息] [信息] ✅ 成功点击'创建访问密钥'按钮 (进度: 100%)
2025-08-04 22:59:47 [信息] 成功点击'创建访问密钥'按钮
2025-08-04 22:59:49 线程3：[信息] [信息] ☑️ 正在勾选确认复选框... (进度: 100%)
2025-08-04 22:59:51 线程3：[信息] [信息] ✅ 使用id属性定位到确认复选框 (进度: 100%)
2025-08-04 22:59:51 [信息] 使用id属性定位到确认复选框
2025-08-04 22:59:51 线程3：[信息] [信息] ✅ 成功勾选确认复选框 (进度: 100%)
2025-08-04 22:59:51 [信息] 成功勾选确认复选框
2025-08-04 22:59:52 线程3：[信息] [信息] 🔑 正在点击'创建访问密钥'确认按钮... (进度: 100%)
2025-08-04 22:59:52 线程3：[信息] [信息] ✅ 成功点击'创建访问密钥'确认按钮 (进度: 100%)
2025-08-04 22:59:52 [信息] 成功点击'创建访问密钥'确认按钮
2025-08-04 22:59:55 线程3：[信息] [信息] 📋 开始复制访问密钥... (进度: 100%)
2025-08-04 22:59:55 [信息] 开始复制访问密钥
2025-08-04 22:59:57 线程3：[信息] [信息] 🔍 方法2找到 2 个单元格 (进度: 100%)
2025-08-04 22:59:57 [信息] 方法2找到 2 个单元格
2025-08-04 22:59:57 线程3：[信息] [信息] 🔍 总共找到 2 个单元格，开始分析... (进度: 100%)
2025-08-04 22:59:57 [信息] 总共找到 2 个单元格，开始分析
2025-08-04 22:59:57 [信息] 单元格[0]: 'AKIAR6FEX4RC7RI3NLV2'
2025-08-04 22:59:57 [信息] 单元格[1]: '***************Mostrar'
2025-08-04 22:59:57 线程3：[信息] [信息] 📋 正在查找并复制访问密钥... (进度: 100%)
2025-08-04 22:59:57 线程3：[信息] [信息] ✅ 找到访问密钥: AKIAR6FEX4RC7RI3NLV2 (进度: 100%)
2025-08-04 22:59:57 [信息] 找到访问密钥: AKIAR6FEX4RC7RI3NLV2
2025-08-04 23:00:06 线程3：[信息] [信息] ✅ 方法1成功点击访问密钥复制按钮 (进度: 100%)
2025-08-04 23:00:06 [信息] 方法1成功点击访问密钥复制按钮
2025-08-04 23:00:07 线程3：[信息] [信息] 📋 正在查找并复制秘密访问密钥... (进度: 100%)
2025-08-04 23:00:11 线程3：[信息] [信息] ✅ 找到秘密访问密钥: ***************Mostrar (进度: 100%)
2025-08-04 23:00:11 [信息] 找到秘密访问密钥: ***************Mostrar
2025-08-04 23:00:11 线程3：[信息] [信息] 🔍 正在寻找向导阻挡... (进度: 100%)
2025-08-04 23:00:11 线程3：[信息] [信息] 🔍 检测到隐藏的秘密访问密钥，尝试点击显示按钮... (进度: 100%)
2025-08-04 23:00:11 [信息] 检测到隐藏的秘密访问密钥，尝试点击显示按钮
2025-08-04 23:00:11 线程3：[信息] [信息] ✅ 使用TestId定位到显示按钮 (进度: 100%)
2025-08-04 23:00:11 [信息] 使用TestId定位到显示按钮
2025-08-04 23:00:12 线程3：[信息] [信息] ✅ 显示按钮点击成功，新文本: GfBE37sfTfksEFZnsGvdDpGg/PoPDHsSZWTfVhtgOcultar (进度: 100%)
2025-08-04 23:00:12 [信息] 显示按钮点击成功，新文本: GfBE37sfTfksEFZnsGvdDpGg/PoPDHsSZWTfVhtgOcultar
2025-08-04 23:00:12 线程3：[信息] [信息] ✅ 直接从显示文本提取秘密访问密钥: GfBE37sfTfksEFZnsGvdDpGg/PoPDHsSZWTfVhtgOcultar (进度: 100%)
2025-08-04 23:00:12 [信息] 直接从显示文本提取秘密访问密钥: GfBE37sfTfksEFZnsGvdDpGg/PoPDHsSZWTfVhtgOcultar
2025-08-04 23:00:12 线程3：[信息] [信息] ✅ 访问密钥复制完成 (进度: 100%)
2025-08-04 23:00:12 [信息] 访问密钥复制完成 - AccessKey: AKIAR6FEX4RC7RI3NLV2, SecretKey: GfBE37sfTfksEFZnsGvdDpGg/PoPDHsSZWTfVhtgOcultar
2025-08-04 23:00:13 线程3：[信息] [信息] 🖱️ 正在点击'已完成'按钮... (进度: 100%)
2025-08-04 23:00:13 线程3：[信息] [信息] ✅ 成功点击'已完成'按钮 (进度: 100%)
2025-08-04 23:00:13 [信息] 成功点击'已完成'按钮
2025-08-04 23:00:13 线程3：[信息] [信息] ✅ 密钥已保存到数据对象 - AccessKey: AKIAR6FEX4RC7RI3NLV2, SecretKey: GfBE37sfTfksEFZnsGvdDpGg/PoPDHsSZWTfVhtgOcultar (进度: 100%)
2025-08-04 23:00:13 [信息] 密钥已保存到数据对象 - AccessKey: AKIAR6FEX4RC7RI3NLV2, SecretKey: GfBE37sfTfksEFZnsGvdDpGg/PoPDHsSZWTfVhtgOcultar
2025-08-04 23:00:14 线程3：[信息] [信息] 🖱️ 正在点击'继续'按钮... (进度: 100%)
2025-08-04 23:00:17 线程3：[信息] [信息] ❌ 点击'继续'按钮失败: Timeout 3000ms exceeded.
Call log:
  - waiting for Locator("button[data-awsui-analytics*='navigate']") to be visible (进度: 100%)
2025-08-04 23:00:17 [信息] 点击'继续'按钮失败: Timeout 3000ms exceeded.
Call log:
  - waiting for Locator("button[data-awsui-analytics*='navigate']") to be visible
2025-08-04 23:00:17 线程3：[信息] [信息] 🔐 开始设置MFA设备... (进度: 100%)
2025-08-04 23:00:17 [信息] 开始设置MFA设备
2025-08-04 23:00:17 线程3：[信息] [信息] 🖱️ 正在点击'分配 MFA 设备'按钮... (进度: 100%)
2025-08-04 23:00:20 线程3：[信息] [信息] ✅ 成功点击'分配 MFA 设备'按钮 (进度: 100%)
2025-08-04 23:00:20 [信息] 成功点击'分配 MFA 设备'按钮
2025-08-04 23:00:20 线程3：[信息] [信息] ⏳ 等待MFA设备设置页面加载（20秒超时）... (进度: 100%)
2025-08-04 23:00:31 [信息] 获取线程3当前数据: <EMAIL>
2025-08-04 23:00:31 线程3：[信息] 终止时正在处理的数据: <EMAIL>
2025-08-04 23:00:31 线程3：[信息] 数据详情: <EMAIL>|APzZsEy6|Vergaray Robles Delvis|Entel|Las madreselvas 5067 Dpto 34D|Region Metropolitana|Santiago|7811048|4757741016880557|09|28|912|Vergaray Robles Delvis|2L032D039IE|CL
2025-08-04 23:00:31 线程3：[信息] 多线程终止 - 复制密钥信息到剪贴板
2025-08-04 23:00:31 线程3：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：2L032D039IE ③AWS密码：APzZsEy6 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止 (进度: 100%)
2025-08-04 23:00:31 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：2L032D039IE ③AWS密码：APzZsEy6 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 23:00:31 线程3：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：2L032D039IE ③AWS密码：APzZsEy6 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 23:00:31 [信息] 线程3请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：2L032D039IE ③AWS密码：APzZsEy6 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 23:00:31 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：2L032D039IE ③AWS密码：APzZsEy6 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 23:00:31 [信息] 线程3剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：2L032D039IE ③AWS密码：APzZsEy6 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 23:00:32 线程3：[信息] [信息] 注册已终止 (进度: 100%)
2025-08-04 23:00:32 线程3：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-08-04 23:00:32 线程3：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：2L032D039IE ③AWS密码：APzZsEy6 ④访问密钥：AKIAR6FEX4RC7RI3NLV2 ⑤秘密访问密钥：GfBE37sfTfksEFZnsGvdDpGg/PoPDHsSZWTfVhtgOcultar ⑥MFA信息：   //手动终止 (进度: 100%)
2025-08-04 23:00:32 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：2L032D039IE ③AWS密码：APzZsEy6 ④访问密钥：AKIAR6FEX4RC7RI3NLV2 ⑤秘密访问密钥：GfBE37sfTfksEFZnsGvdDpGg/PoPDHsSZWTfVhtgOcultar ⑥MFA信息：   //手动终止
2025-08-04 23:00:32 线程3：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：2L032D039IE ③AWS密码：APzZsEy6 ④访问密钥：AKIAR6FEX4RC7RI3NLV2 ⑤秘密访问密钥：GfBE37sfTfksEFZnsGvdDpGg/PoPDHsSZWTfVhtgOcultar ⑥MFA信息：   //手动终止
2025-08-04 23:00:32 [信息] 线程3请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：2L032D039IE ③AWS密码：APzZsEy6 ④访问密钥：AKIAR6FEX4RC7RI3NLV2 ⑤秘密访问密钥：GfBE37sfTfksEFZnsGvdDpGg/PoPDHsSZWTfVhtgOcultar ⑥MFA信息：   //手动终止
2025-08-04 23:00:32 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：2L032D039IE ③AWS密码：APzZsEy6 ④访问密钥：AKIAR6FEX4RC7RI3NLV2 ⑤秘密访问密钥：GfBE37sfTfksEFZnsGvdDpGg/PoPDHsSZWTfVhtgOcultar ⑥MFA信息：   //手动终止
2025-08-04 23:00:32 [信息] 线程3剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：2L032D039IE ③AWS密码：APzZsEy6 ④访问密钥：AKIAR6FEX4RC7RI3NLV2 ⑤秘密访问密钥：GfBE37sfTfksEFZnsGvdDpGg/PoPDHsSZWTfVhtgOcultar ⑥MFA信息：   //手动终止
2025-08-04 23:00:32 线程3：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-08-04 23:00:32 线程3：[信息] 临时目录已清理: C:\Users\<USER>\AppData\Local\Temp\AWS_Thread_3_20250804_225332
2025-08-04 23:00:32 线程3：[信息] 已终止
2025-08-04 23:00:32 [信息] 线程3已终止
2025-08-04 23:00:32 [信息] 开始处理线程3终止数据，共1个数据
2025-08-04 23:00:32 [信息] 处理线程3终止数据: <EMAIL>
2025-08-04 23:00:32 [信息] 从注册数据列表中移除: <EMAIL>
2025-08-04 23:00:32 [信息] 线程3终止 - 数据已移动到终止列表: <EMAIL>
2025-08-04 23:00:32 [信息] 线程3终止数据处理完成，成功移动1个数据
2025-08-04 23:00:32 [信息] UniformGrid列数已更新为: 1
2025-08-04 23:00:32 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-08-04 23:00:32 [信息] 线程3已终止
2025-08-04 23:00:40 线程3：[信息] [信息] ⚠️ 20秒内未找到'设备名称'输入框，需要手动处理 (进度: 0%)
2025-08-04 23:00:40 线程3：[信息] [信息] ⚠️ 设备名称页面加载超时，需要手动处理 (进度: 0%)
2025-08-04 23:00:40 [信息] 设备名称页面加载超时，需要手动处理
2025-08-04 23:00:40 线程3：[信息] [信息] 页面加载完成后，手动点击继续注册 (进度: 0%)
2025-08-04 23:00:40 线程3：[信息] [信息] MFA页面需要手动处理，等待用户操作 (进度: 0%)
2025-08-04 23:00:40 线程3：[信息] 已继续
2025-08-04 23:00:40 [信息] 线程3已继续
2025-08-04 23:02:24 [信息] 多线程窗口引用已清理
2025-08-04 23:02:24 [信息] 主窗口已恢复正常状态，多线程窗口引用已清理
2025-08-04 23:02:24 [信息] 多线程管理窗口正在关闭
2025-08-04 23:02:26 [信息] 程序正在退出，开始清理工作...
2025-08-04 23:02:26 [信息] 程序退出，开始清理所有待拉黑千川手机号码
2025-08-04 23:02:26 [信息] 程序退出清理完成: 没有需要拉黑的千川手机号码
2025-08-04 23:02:26 [信息] 程序退出，开始清理所有待释放手机号码
2025-08-04 23:02:26 [信息] 程序退出清理完成: 没有需要释放的手机号码
2025-08-04 23:02:26 [信息] 程序退出清理工作完成
2025-08-04 23:02:29 [信息] AWS自动注册工具启动
2025-08-04 23:02:29 [信息] 程序版本: 1.0.0.0
2025-08-04 23:02:29 [信息] 启动时间: 2025-08-04 23:02:29
2025-08-04 23:02:29 [信息] 浏览器模式切换: 无痕 Chrome 模式
2025-08-04 23:02:29 [信息] 线程数量已选择: 1
2025-08-04 23:02:29 [信息] 线程数量选择初始化完成
2025-08-04 23:02:29 [信息] 程序初始化完成
2025-08-04 23:02:33 [按钮操作] 选择文件 -> 打开文件选择对话框
2025-08-04 23:02:36 [信息] 已选择文件: C:\Users\<USER>\Desktop\2025-08-04-智利.txt
2025-08-04 23:02:37 [按钮操作] 加载信息 -> 加载数据文件: C:\Users\<USER>\Desktop\2025-08-04-智利.txt
2025-08-04 23:02:37 [信息] 成功加载 9 条数据
2025-08-04 23:02:39 [信息] 线程数量已选择: 5
2025-08-04 23:03:19 [按钮操作] 开始注册 -> 启动注册流程
2025-08-04 23:03:19 [信息] 开始启动多线程注册，线程数量: 5
2025-08-04 23:03:19 [信息] 开始启动多线程注册，线程数量: 5，数据条数: 9
2025-08-04 23:03:19 [信息] 所有线程已停止并清理
2025-08-04 23:03:19 [信息] 正在初始化多线程服务...
2025-08-04 23:03:19 [信息] 榴莲手机API服务已初始化
2025-08-04 23:03:19 [信息] 手机号码管理器已初始化，服务商: Durian，将在第一个线程完成第二页后获取手机号码
2025-08-04 23:03:19 [信息] 多线程服务初始化完成
2025-08-04 23:03:19 [信息] 数据分配完成：共9条数据分配给5个线程
2025-08-04 23:03:19 [信息] 线程1分配到2条数据
2025-08-04 23:03:19 [信息] 线程2分配到2条数据
2025-08-04 23:03:19 [信息] 线程3分配到2条数据
2025-08-04 23:03:19 [信息] 线程4分配到2条数据
2025-08-04 23:03:19 [信息] 线程5分配到1条数据
2025-08-04 23:03:19 [信息] 屏幕工作区域: 1280x672
2025-08-04 23:03:19 [信息] 线程1窗口布局: 位置(0, 0), 大小(320x200), 列1行1, 宽度25%, 当前列窗口数:3, 间隙19px, 双列模式:True, 底部安全边距:33px, 可用高度:639px
2025-08-04 23:03:19 [信息] 线程1获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-04 23:03:19 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 23:03:19 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_006, WebGL=ANGLE (AMD Radeon RX 6700 XT Direct3D11 vs_5_0 ps_5_0), CPU=12核, RAM=32 GB
2025-08-04 23:03:19 线程1：[信息] 已创建，窗口位置: (0, 0)
2025-08-04 23:03:19 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-04 23:03:19 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-04 23:03:19 [信息] 线程1已创建，窗口位置: (0, 0)，指纹: 国家=CL, 时区=America/Santiago
2025-08-04 23:03:19 [信息] 屏幕工作区域: 1280x672
2025-08-04 23:03:19 [信息] 线程2窗口布局: 位置(0, 219), 大小(320x200), 列1行2, 宽度25%, 当前列窗口数:3, 间隙19px, 双列模式:True, 底部安全边距:33px, 可用高度:639px
2025-08-04 23:03:19 [信息] 线程2获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-04 23:03:19 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 23:03:19 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_001, WebGL=ANGLE (AMD Radeon RX 7600 Direct3D11 vs_5_0 ps_5_0), CPU=4核, RAM=16 GB
2025-08-04 23:03:19 线程2：[信息] 已创建，窗口位置: (0, 219)
2025-08-04 23:03:19 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-04 23:03:19 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-04 23:03:19 [信息] 线程2已创建，窗口位置: (0, 219)，指纹: 国家=CL, 时区=America/Santiago
2025-08-04 23:03:19 [信息] 屏幕工作区域: 1280x672
2025-08-04 23:03:19 [信息] 线程3窗口布局: 位置(0, 438), 大小(320x200), 列1行3, 宽度25%, 当前列窗口数:3, 间隙19px, 双列模式:True, 底部安全边距:33px, 可用高度:639px
2025-08-04 23:03:19 [信息] 线程3获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-04 23:03:19 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 23:03:19 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_004, WebGL=ANGLE (NVIDIA GeForce RTX 3070 Direct3D11 vs_5_0 ps_5_0), CPU=24核, RAM=6 GB
2025-08-04 23:03:19 线程3：[信息] 已创建，窗口位置: (0, 438)
2025-08-04 23:03:19 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-04 23:03:19 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-04 23:03:19 [信息] 线程3已创建，窗口位置: (0, 438)，指纹: 国家=CL, 时区=America/Santiago
2025-08-04 23:03:19 [信息] 屏幕工作区域: 1280x672
2025-08-04 23:03:19 [信息] 线程4窗口布局: 位置(384, 0), 大小(320x310), 列2行1, 宽度25%, 当前列窗口数:2, 间隙19px, 双列模式:True, 底部安全边距:33px, 可用高度:639px
2025-08-04 23:03:19 [信息] 线程4获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-04 23:03:19 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 23:03:19 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_003, WebGL=ANGLE (Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0), CPU=24核, RAM=32 GB
2025-08-04 23:03:19 线程4：[信息] 已创建，窗口位置: (384, 0)
2025-08-04 23:03:19 线程4：[信息] 添加数据到队列: <EMAIL>
2025-08-04 23:03:19 线程4：[信息] 添加数据到队列: <EMAIL>
2025-08-04 23:03:19 [信息] 线程4已创建，窗口位置: (384, 0)，指纹: 国家=CL, 时区=America/Santiago
2025-08-04 23:03:19 [信息] 屏幕工作区域: 1280x672
2025-08-04 23:03:19 [信息] 线程5窗口布局: 位置(384, 329), 大小(320x310), 列2行2, 宽度25%, 当前列窗口数:2, 间隙19px, 双列模式:True, 底部安全边距:33px, 可用高度:639px
2025-08-04 23:03:19 [信息] 线程5获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-04 23:03:19 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 23:03:19 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_002, WebGL=ANGLE (AMD Radeon RX 7600 Direct3D11 vs_5_0 ps_5_0), CPU=24核, RAM=14 GB
2025-08-04 23:03:19 线程5：[信息] 已创建，窗口位置: (384, 329)
2025-08-04 23:03:19 线程5：[信息] 添加数据到队列: <EMAIL>
2025-08-04 23:03:19 [信息] 线程5已创建，窗口位置: (384, 329)，指纹: 国家=CL, 时区=America/Santiago
2025-08-04 23:03:19 [信息] 多线程注册启动成功，共5个线程
2025-08-04 23:03:19 线程2：[信息] 开始启动注册流程
2025-08-04 23:03:19 线程1：[信息] 开始启动注册流程
2025-08-04 23:03:19 线程4：[信息] 开始启动注册流程
2025-08-04 23:03:19 线程3：[信息] 开始启动注册流程
2025-08-04 23:03:19 线程2：[信息] 开始启动浏览器: 位置(0, 219), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36
2025-08-04 23:03:19 线程3：[信息] 开始启动浏览器: 位置(0, 438), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0
2025-08-04 23:03:19 线程3：[信息] 启动无痕Chrome浏览器...
2025-08-04 23:03:19 线程1：[信息] 开始启动浏览器: 位置(0, 0), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0
2025-08-04 23:03:19 线程1：[信息] 启动无痕Chrome浏览器...
2025-08-04 23:03:19 线程2：[信息] 启动无痕Chrome浏览器...
2025-08-04 23:03:19 [信息] 多线程管理窗口已初始化
2025-08-04 23:03:19 线程4：[信息] 开始启动浏览器: 位置(384, 0), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0
2025-08-04 23:03:19 线程4：[信息] 启动无痕Chrome浏览器...
2025-08-04 23:03:19 [信息] UniformGrid列数已更新为: 1
2025-08-04 23:03:19 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 0, 列数: 1
2025-08-04 23:03:19 [信息] 多线程管理窗口已打开
2025-08-04 23:03:19 [信息] 多线程注册启动成功，共5个线程
2025-08-04 23:03:19 [信息] UniformGrid列数已更新为: 1
2025-08-04 23:03:19 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 1, 列数: 1
2025-08-04 23:03:19 线程3：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-04 23:03:19 [信息] UniformGrid列数已更新为: 1
2025-08-04 23:03:19 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-08-04 23:03:19 [信息] UniformGrid列数已更新为: 2
2025-08-04 23:03:19 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 3, 列数: 2
2025-08-04 23:03:19 [信息] UniformGrid列数已更新为: 2
2025-08-04 23:03:19 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 4, 列数: 2
2025-08-04 23:03:19 线程1：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-04 23:03:19 线程2：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-04 23:03:19 线程4：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-04 23:03:22 [信息] UniformGrid列数已更新为: 2
2025-08-04 23:03:22 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 5, 列数: 2
2025-08-04 23:03:22 线程5：[信息] 开始启动注册流程
2025-08-04 23:03:22 线程5：[信息] 开始启动浏览器: 位置(384, 329), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-08-04 23:03:22 线程5：[信息] 启动无痕Chrome浏览器...
2025-08-04 23:03:23 线程5：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-04 23:03:30 线程3：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-04 23:03:30 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-04 23:03:30 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-08-04 23:03:30 线程3：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-04 23:03:30 线程2：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-04 23:03:30 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-04 23:03:30 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0
2025-08-04 23:03:30 线程2：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-04 23:03:30 线程4：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-04 23:03:30 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-04 23:03:30 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0
2025-08-04 23:03:30 线程4：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-04 23:03:30 线程1：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-04 23:03:30 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-04 23:03:30 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36
2025-08-04 23:03:30 线程1：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-04 23:03:30 线程5：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-04 23:03:30 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-04 23:03:30 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36 Edg/119.0.0.0
2025-08-04 23:03:30 线程5：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-04 23:03:32 线程2：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-04 23:03:32 线程1：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-04 23:03:33 线程4：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-04 23:03:34 线程3：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-04 23:03:34 线程2：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-04 23:03:34 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-04 23:03:34 线程2：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-04 23:03:34 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 23:03:34 线程2：[信息] [信息] 多线程模式使用指纹地理位置: -29.9027, -71.2519 (进度: 0%)
2025-08-04 23:03:35 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-29.9027, 经度=-71.2519
2025-08-04 23:03:35 线程1：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-04 23:03:35 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-04 23:03:35 线程1：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-04 23:03:35 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 23:03:35 线程1：[信息] [信息] 多线程模式使用指纹地理位置: -33.4489, -70.6693 (进度: 0%)
2025-08-04 23:03:35 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-33.4489, 经度=-70.6693
2025-08-04 23:03:35 线程5：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-04 23:03:35 线程2：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_001, CPU: 4核 (进度: 0%)
2025-08-04 23:03:35 [信息] 浏览器指纹注入: Canvas=canvas_fp_001, WebGL=ANGLE (AMD Radeon RX 7600 Direct3D11 vs_5_0 ps_5_0), CPU=4核, RAM=16 GB
2025-08-04 23:03:35 线程1：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_006, CPU: 12核 (进度: 0%)
2025-08-04 23:03:35 [信息] 浏览器指纹注入: Canvas=canvas_fp_006, WebGL=ANGLE (AMD Radeon RX 6700 XT Direct3D11 vs_5_0 ps_5_0), CPU=12核, RAM=32 GB
2025-08-04 23:03:36 线程4：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-04 23:03:36 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-04 23:03:36 线程4：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-04 23:03:36 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 23:03:36 线程4：[信息] [信息] 多线程模式使用指纹地理位置: -41.4693, -72.9424 (进度: 0%)
2025-08-04 23:03:36 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-41.4693, 经度=-72.9424
2025-08-04 23:03:36 线程3：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-04 23:03:36 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-04 23:03:36 线程3：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-04 23:03:36 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 23:03:36 线程3：[信息] [信息] 多线程模式使用指纹地理位置: -36.8201, -73.0444 (进度: 0%)
2025-08-04 23:03:36 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-36.8201, 经度=-73.0444
2025-08-04 23:03:36 线程4：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_003, CPU: 24核 (进度: 0%)
2025-08-04 23:03:36 [信息] 浏览器指纹注入: Canvas=canvas_fp_003, WebGL=ANGLE (Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0), CPU=24核, RAM=32 GB
2025-08-04 23:03:37 线程3：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_004, CPU: 24核 (进度: 0%)
2025-08-04 23:03:38 线程5：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-04 23:03:38 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-04 23:03:38 [信息] 浏览器指纹注入: Canvas=canvas_fp_004, WebGL=ANGLE (NVIDIA GeForce RTX 3070 Direct3D11 vs_5_0 ps_5_0), CPU=24核, RAM=6 GB
2025-08-04 23:03:38 线程5：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-04 23:03:38 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 23:03:38 线程2：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-04 23:03:38 线程5：[信息] [信息] 多线程模式使用指纹地理位置: -33.4489, -70.6693 (进度: 0%)
2025-08-04 23:03:38 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-33.4489, 经度=-70.6693
2025-08-04 23:03:38 线程1：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-04 23:03:38 线程5：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_002, CPU: 24核 (进度: 0%)
2025-08-04 23:03:38 [信息] 浏览器指纹注入: Canvas=canvas_fp_002, WebGL=ANGLE (AMD Radeon RX 7600 Direct3D11 vs_5_0 ps_5_0), CPU=24核, RAM=14 GB
2025-08-04 23:03:40 线程4：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-04 23:03:41 线程5：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-04 23:03:41 线程3：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-04 23:03:43 线程4：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 24
   • 设备内存: 32 GB
   • 平台信息: Win32
   • Do Not Track: auto

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (Intel Corp.)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 3C4D5E6F
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_010
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-C2D3E4F
   • MAC地址: 56-78-9A-BC-DE-F0
   • 屏幕分辨率: 2079x1047
   • 可用区域: 2079x1007

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: C3D4E5F6
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: 2g
   • 电池API支持: True
   • 电池电量: 0.80
   • 电池充电状态: False
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-04 23:03:43 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 24    • 设备内存: 32 GB    • 平台信息: Win32    • Do Not Track: auto   图形渲染信息:    • WebGL渲染器: ANGLE (Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (Intel Corp.)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 3C4D5E6F    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_010    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-C2D3E4F    • MAC地址: 56-78-9A-BC-DE-F0    • 屏幕分辨率: 2079x1047    • 可用区域: 2079x1007   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: C3D4E5F6    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: 2g    • 电池API支持: True    • 电池电量: 0.80    • 电池充电状态: False    • 性能API支持: True  ================================
2025-08-04 23:03:43 线程2：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 4
   • 设备内存: 16 GB
   • 平台信息: Win32
   • Do Not Track: system

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (AMD Radeon RX 7600 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (Advanced Micro Devices, Inc.)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 9C0D1E2F
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_002
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-E4F5G6H
   • MAC地址: 9A-BC-DE-F0-12-34
   • 屏幕分辨率: 1800x1039
   • 可用区域: 1800x999

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: A5B6C7D8
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: wifi
   • 电池API支持: True
   • 电池电量: 0.50
   • 电池充电状态: True
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-04 23:03:43 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 4    • 设备内存: 16 GB    • 平台信息: Win32    • Do Not Track: system   图形渲染信息:    • WebGL渲染器: ANGLE (AMD Radeon RX 7600 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (Advanced Micro Devices, Inc.)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 9C0D1E2F    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_002    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-E4F5G6H    • MAC地址: 9A-BC-DE-F0-12-34    • 屏幕分辨率: 1800x1039    • 可用区域: 1800x999   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: A5B6C7D8    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: wifi    • 电池API支持: True    • 电池电量: 0.50    • 电池充电状态: True    • 性能API支持: True  ================================
2025-08-04 23:03:43 线程5：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 24
   • 设备内存: 14 GB
   • 平台信息: Win32
   • Do Not Track: undefined

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (AMD Radeon RX 7600 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (Intel Corporation)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 5A6B7C8D
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_008
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-87EL3RX
   • MAC地址: BC-DE-F0-12-34-56
   • 屏幕分辨率: 2018x1177
   • 可用区域: 2018x1137

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: A5B6C7D8
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: satellite
   • 电池API支持: True
   • 电池电量: 0.91
   • 电池充电状态: True
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-04 23:03:43 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 24    • 设备内存: 14 GB    • 平台信息: Win32    • Do Not Track: undefined   图形渲染信息:    • WebGL渲染器: ANGLE (AMD Radeon RX 7600 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (Intel Corporation)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 5A6B7C8D    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_008    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-87EL3RX    • MAC地址: BC-DE-F0-12-34-56    • 屏幕分辨率: 2018x1177    • 可用区域: 2018x1137   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: A5B6C7D8    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: satellite    • 电池API支持: True    • 电池电量: 0.91    • 电池充电状态: True    • 性能API支持: True  ================================
2025-08-04 23:03:43 线程1：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 12
   • 设备内存: 32 GB
   • 平台信息: Win32
   • Do Not Track: user

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (AMD Radeon RX 6700 XT Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (Intel Corporation)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 1A2B3C4D
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_006
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-E4F5G6H
   • MAC地址: BC-DE-F0-12-34-56
   • 屏幕分辨率: 1762x1107
   • 可用区域: 1762x1067

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: D79834F2
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: unknown
   • 电池API支持: True
   • 电池电量: 0.91
   • 电池充电状态: True
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-04 23:03:43 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 12    • 设备内存: 32 GB    • 平台信息: Win32    • Do Not Track: user   图形渲染信息:    • WebGL渲染器: ANGLE (AMD Radeon RX 6700 XT Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (Intel Corporation)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 1A2B3C4D    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_006    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-E4F5G6H    • MAC地址: BC-DE-F0-12-34-56    • 屏幕分辨率: 1762x1107    • 可用区域: 1762x1067   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: D79834F2    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: unknown    • 电池API支持: True    • 电池电量: 0.91    • 电池充电状态: True    • 性能API支持: True  ================================
2025-08-04 23:03:43 线程5：[信息] [信息] 已设置浏览器标题: 线程5 - AWS注册工具 (进度: 5%)
2025-08-04 23:03:43 线程5：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-04 23:03:43 线程5：[信息] 浏览器启动成功
2025-08-04 23:03:43 线程5：[信息] 获取下一个数据: <EMAIL>
2025-08-04 23:03:44 线程5：[信息] 开始处理账户: <EMAIL>
2025-08-04 23:03:44 线程5：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-04 23:03:44 线程5：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-04 23:03:44 线程5：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-04 23:03:44 线程5：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-04 23:03:44 线程5：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-04 23:03:44 线程4：[信息] [信息] 已设置浏览器标题: 线程4 - AWS注册工具 (进度: 5%)
2025-08-04 23:03:44 线程4：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-04 23:03:44 线程4：[信息] 浏览器启动成功
2025-08-04 23:03:44 线程4：[信息] 获取下一个数据: <EMAIL>
2025-08-04 23:03:45 线程4：[信息] 开始处理账户: <EMAIL>
2025-08-04 23:03:45 线程4：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-04 23:03:45 线程4：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-04 23:03:45 线程4：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-04 23:03:45 线程4：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-04 23:03:45 线程4：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-04 23:03:45 线程3：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 24
   • 设备内存: 6 GB
   • 平台信息: Win32
   • Do Not Track: undefined

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 3070 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (Intel Corporation)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 7A8B9C0D
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_010
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-C2D3E4F
   • MAC地址: E4-42-A6-5D-13-98
   • 屏幕分辨率: 2048x986
   • 可用区域: 2048x946

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: E1F2A3B4
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: bluetooth
   • 电池API支持: True
   • 电池电量: 0.70
   • 电池充电状态: True
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-04 23:03:45 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 24    • 设备内存: 6 GB    • 平台信息: Win32    • Do Not Track: undefined   图形渲染信息:    • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 3070 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (Intel Corporation)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 7A8B9C0D    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_010    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-C2D3E4F    • MAC地址: E4-42-A6-5D-13-98    • 屏幕分辨率: 2048x986    • 可用区域: 2048x946   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: E1F2A3B4    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: bluetooth    • 电池API支持: True    • 电池电量: 0.70    • 电池充电状态: True    • 性能API支持: True  ================================
2025-08-04 23:03:45 线程2：[信息] [信息] 已设置浏览器标题: 线程2 - AWS注册工具 (进度: 5%)
2025-08-04 23:03:45 线程2：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-04 23:03:45 线程2：[信息] 浏览器启动成功
2025-08-04 23:03:45 线程2：[信息] 获取下一个数据: <EMAIL>
2025-08-04 23:03:46 线程2：[信息] 开始处理账户: <EMAIL>
2025-08-04 23:03:46 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-04 23:03:46 线程2：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-04 23:03:46 线程2：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-04 23:03:46 线程2：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-04 23:03:46 线程2：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-04 23:03:46 线程1：[信息] [信息] 已设置浏览器标题: 线程1 - AWS注册工具 (进度: 5%)
2025-08-04 23:03:46 线程1：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-04 23:03:46 线程1：[信息] 浏览器启动成功
2025-08-04 23:03:46 线程1：[信息] 获取下一个数据: <EMAIL>
2025-08-04 23:03:46 线程1：[信息] 开始处理账户: <EMAIL>
2025-08-04 23:03:46 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-04 23:03:46 线程1：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-04 23:03:46 线程1：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-04 23:03:46 线程1：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-04 23:03:46 线程1：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-04 23:03:47 线程3：[信息] [信息] 已设置浏览器标题: 线程3 - AWS注册工具 (进度: 5%)
2025-08-04 23:03:47 线程3：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-04 23:03:47 线程3：[信息] 浏览器启动成功
2025-08-04 23:03:47 线程3：[信息] 获取下一个数据: <EMAIL>
2025-08-04 23:03:48 线程3：[信息] 开始处理账户: <EMAIL>
2025-08-04 23:03:48 线程5：[信息] [信息] 已设置页面视口大小为320x310 (进度: 98%)
2025-08-04 23:03:48 线程4：[信息] [信息] 已设置页面视口大小为320x310 (进度: 98%)
2025-08-04 23:03:48 线程2：[信息] [信息] 已设置页面视口大小为320x200 (进度: 98%)
2025-08-04 23:03:48 线程3：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-04 23:03:48 线程4：[信息] [信息] 已重新设置浏览器标题: 线程4 - AWS注册工具 (进度: 98%)
2025-08-04 23:03:48 线程2：[信息] [信息] 已重新设置浏览器标题: 线程2 - AWS注册工具 (进度: 98%)
2025-08-04 23:03:48 线程2：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-04 23:03:48 线程5：[信息] [信息] 已重新设置浏览器标题: 线程5 - AWS注册工具 (进度: 98%)
2025-08-04 23:03:48 线程5：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-04 23:03:48 线程3：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-04 23:03:48 线程3：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-04 23:03:48 线程3：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-04 23:03:48 线程1：[信息] [信息] 已设置页面视口大小为320x200 (进度: 98%)
2025-08-04 23:03:48 线程3：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-04 23:03:48 线程4：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-04 23:03:48 线程5：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-04 23:03:48 线程5：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-04 23:03:48 线程5：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-04 23:03:48 线程1：[信息] [信息] 已重新设置浏览器标题: 线程1 - AWS注册工具 (进度: 98%)
2025-08-04 23:03:48 线程1：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-04 23:03:48 线程2：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-04 23:03:48 线程2：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0... (进度: 98%)
2025-08-04 23:03:48 线程2：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-04 23:03:48 线程4：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-04 23:03:48 线程4：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0... (进度: 98%)
2025-08-04 23:03:48 线程4：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-04 23:03:48 线程1：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-04 23:03:48 线程1：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-04 23:03:48 线程1：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-04 23:03:53 线程3：[信息] [信息] 已设置页面视口大小为320x200 (进度: 98%)
2025-08-04 23:03:53 线程3：[信息] [信息] 已重新设置浏览器标题: 线程3 - AWS注册工具 (进度: 98%)
2025-08-04 23:03:53 线程3：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-04 23:03:53 线程3：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-04 23:03:53 线程3：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-04 23:03:53 线程3：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-04 23:04:18 线程5：[信息] [信息] 注册失败: Timeout 30000ms exceeded.
Call log:
  - navigating to "https://portal.aws.amazon.com/gp/aws/developer/registration/index.html?refid=em_127222&p=free&c=hp&z=1", waiting until "load" (进度: 98%)
2025-08-04 23:04:18 线程5：[信息] [信息] 第一页相关失败，数据保持不动 (进度: 98%)
2025-08-04 23:04:18 [信息] 第一页相关失败，数据保持不动
2025-08-04 23:04:18 线程5：[错误] 账户注册启动失败: <EMAIL>
2025-08-04 23:04:18 线程5：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-04 23:04:18 线程2：[信息] [信息] 注册失败: Timeout 30000ms exceeded.
Call log:
  - navigating to "https://portal.aws.amazon.com/gp/aws/developer/registration/index.html?refid=em_127222&p=free&c=hp&z=1", waiting until "load" (进度: 98%)
2025-08-04 23:04:18 线程2：[信息] [信息] 第一页相关失败，数据保持不动 (进度: 98%)
2025-08-04 23:04:18 [信息] 第一页相关失败，数据保持不动
2025-08-04 23:04:18 线程2：[错误] 账户注册启动失败: <EMAIL>
2025-08-04 23:04:18 线程2：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-04 23:04:18 线程4：[信息] [信息] 注册失败: Timeout 30000ms exceeded.
Call log:
  - navigating to "https://portal.aws.amazon.com/gp/aws/developer/registration/index.html?refid=em_127222&p=free&c=hp&z=1", waiting until "load" (进度: 98%)
2025-08-04 23:04:18 线程4：[信息] [信息] 第一页相关失败，数据保持不动 (进度: 98%)
2025-08-04 23:04:18 [信息] 第一页相关失败，数据保持不动
2025-08-04 23:04:18 线程4：[错误] 账户注册启动失败: <EMAIL>
2025-08-04 23:04:18 线程4：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-04 23:04:18 线程1：[信息] [信息] 注册失败: Timeout 30000ms exceeded.
Call log:
  - navigating to "https://portal.aws.amazon.com/gp/aws/developer/registration/index.html?refid=em_127222&p=free&c=hp&z=1", waiting until "load" (进度: 98%)
2025-08-04 23:04:19 线程1：[信息] [信息] 第一页相关失败，数据保持不动 (进度: 98%)
2025-08-04 23:04:19 [信息] 第一页相关失败，数据保持不动
2025-08-04 23:04:19 线程1：[错误] 账户注册启动失败: <EMAIL>
2025-08-04 23:04:19 线程1：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-04 23:04:24 线程3：[信息] [信息] 注册失败: Timeout 30000ms exceeded.
Call log:
  - navigating to "https://portal.aws.amazon.com/gp/aws/developer/registration/index.html?refid=em_127222&p=free&c=hp&z=1", waiting until "load" (进度: 98%)
2025-08-04 23:04:24 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 23:04:24 [信息] 多线程状态已重置
2025-08-04 23:04:24 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 23:04:24 [信息] 多线程状态已重置
2025-08-04 23:04:24 线程3：[信息] [信息] 第一页相关失败，数据保持不动 (进度: 98%)
2025-08-04 23:04:24 [信息] 第一页相关失败，数据保持不动
2025-08-04 23:04:24 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 23:04:24 [信息] 多线程状态已重置
2025-08-04 23:04:24 线程3：[错误] 账户注册启动失败: <EMAIL>
2025-08-04 23:04:24 线程3：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-04 23:04:38 [信息] 多线程窗口引用已清理
2025-08-04 23:04:38 [信息] 主窗口已恢复正常状态，多线程窗口引用已清理
2025-08-04 23:04:38 [信息] 多线程管理窗口正在关闭
2025-08-04 23:04:42 [信息] 程序正在退出，开始清理工作...
2025-08-04 23:04:42 [信息] 程序退出，开始清理所有待拉黑千川手机号码
2025-08-04 23:04:42 [信息] 程序退出清理完成: 没有需要拉黑的千川手机号码
2025-08-04 23:04:42 [信息] 程序退出，开始清理所有待释放手机号码
2025-08-04 23:04:42 [信息] 程序退出清理完成: 没有需要释放的手机号码
2025-08-04 23:04:42 [信息] 程序退出清理工作完成
2025-08-04 23:05:46 [信息] AWS自动注册工具启动
2025-08-04 23:05:46 [信息] 程序版本: 1.0.0.0
2025-08-04 23:05:46 [信息] 启动时间: 2025-08-04 23:05:46
2025-08-04 23:05:46 [信息] 浏览器模式切换: 无痕 Chrome 模式
2025-08-04 23:05:46 [信息] 线程数量已选择: 1
2025-08-04 23:05:46 [信息] 线程数量选择初始化完成
2025-08-04 23:05:46 [信息] 程序初始化完成
2025-08-04 23:05:51 [按钮操作] 选择文件 -> 打开文件选择对话框
2025-08-04 23:05:54 [信息] 已选择文件: C:\Users\<USER>\Desktop\2025-08-04-智利.txt
2025-08-04 23:05:55 [按钮操作] 加载信息 -> 加载数据文件: C:\Users\<USER>\Desktop\2025-08-04-智利.txt
2025-08-04 23:05:55 [信息] 成功加载 9 条数据
2025-08-04 23:05:57 [信息] 线程数量已选择: 3
2025-08-04 23:06:00 [按钮操作] 开始注册 -> 启动注册流程
2025-08-04 23:06:00 [信息] 开始启动多线程注册，线程数量: 3
2025-08-04 23:06:00 [信息] 开始启动多线程注册，线程数量: 3，数据条数: 9
2025-08-04 23:06:00 [信息] 所有线程已停止并清理
2025-08-04 23:06:00 [信息] 正在初始化多线程服务...
2025-08-04 23:06:00 [信息] 榴莲手机API服务已初始化
2025-08-04 23:06:00 [信息] 手机号码管理器已初始化，服务商: Durian，将在第一个线程完成第二页后获取手机号码
2025-08-04 23:06:00 [信息] 多线程服务初始化完成
2025-08-04 23:06:00 [信息] 数据分配完成：共9条数据分配给3个线程
2025-08-04 23:06:00 [信息] 线程1分配到3条数据
2025-08-04 23:06:00 [信息] 线程2分配到3条数据
2025-08-04 23:06:00 [信息] 线程3分配到3条数据
2025-08-04 23:06:00 [信息] 屏幕工作区域: 1280x672
2025-08-04 23:06:00 [信息] 线程1窗口布局: 位置(0, 0), 大小(384x200), 列1行1, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-04 23:06:00 [信息] 线程1获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-04 23:06:00 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 23:06:00 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_002, WebGL=ANGLE (NVIDIA GeForce GTX 1660 Direct3D11 vs_5_0 ps_5_0), CPU=20核, RAM=6 GB
2025-08-04 23:06:00 线程1：[信息] 已创建，窗口位置: (0, 0)
2025-08-04 23:06:00 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-04 23:06:00 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-04 23:06:00 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-04 23:06:00 [信息] 线程1已创建，窗口位置: (0, 0)，指纹: 国家=CL, 时区=America/Santiago
2025-08-04 23:06:00 [信息] 屏幕工作区域: 1280x672
2025-08-04 23:06:00 [信息] 线程2窗口布局: 位置(0, 219), 大小(384x200), 列1行2, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-04 23:06:00 [信息] 线程2获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-04 23:06:00 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 23:06:00 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_008, WebGL=ANGLE (NVIDIA GeForce RTX 3070 Direct3D11 vs_5_0 ps_5_0), CPU=8核, RAM=4 GB
2025-08-04 23:06:00 线程2：[信息] 已创建，窗口位置: (0, 219)
2025-08-04 23:06:00 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-04 23:06:00 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-04 23:06:00 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-04 23:06:00 [信息] 线程2已创建，窗口位置: (0, 219)，指纹: 国家=CL, 时区=America/Santiago
2025-08-04 23:06:00 [信息] 屏幕工作区域: 1280x672
2025-08-04 23:06:00 [信息] 线程3窗口布局: 位置(0, 438), 大小(384x200), 列1行3, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-04 23:06:00 [信息] 线程3获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-04 23:06:00 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 23:06:00 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_008, WebGL=ANGLE (Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0), CPU=32核, RAM=6 GB
2025-08-04 23:06:00 线程3：[信息] 已创建，窗口位置: (0, 438)
2025-08-04 23:06:00 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-04 23:06:00 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-04 23:06:00 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-04 23:06:00 [信息] 线程3已创建，窗口位置: (0, 438)，指纹: 国家=CL, 时区=America/Santiago
2025-08-04 23:06:00 [信息] 多线程注册启动成功，共3个线程
2025-08-04 23:06:00 线程1：[信息] 开始启动注册流程
2025-08-04 23:06:00 线程2：[信息] 开始启动注册流程
2025-08-04 23:06:00 线程3：[信息] 开始启动注册流程
2025-08-04 23:06:00 线程1：[信息] 开始启动浏览器: 位置(0, 0), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36
2025-08-04 23:06:00 线程2：[信息] 开始启动浏览器: 位置(0, 219), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36
2025-08-04 23:06:00 线程2：[信息] 启动无痕Chrome浏览器...
2025-08-04 23:06:00 线程1：[信息] 启动无痕Chrome浏览器...
2025-08-04 23:06:00 线程3：[信息] 开始启动浏览器: 位置(0, 438), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36
2025-08-04 23:06:00 线程3：[信息] 启动无痕Chrome浏览器...
2025-08-04 23:06:00 线程2：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-04 23:06:00 线程1：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-04 23:06:00 线程3：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-04 23:06:00 [信息] 多线程管理窗口已初始化
2025-08-04 23:06:00 [信息] UniformGrid列数已更新为: 1
2025-08-04 23:06:00 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 0, 列数: 1
2025-08-04 23:06:00 [信息] 多线程管理窗口已打开
2025-08-04 23:06:00 [信息] 多线程注册启动成功，共3个线程
2025-08-04 23:06:03 [信息] UniformGrid列数已更新为: 1
2025-08-04 23:06:03 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 1, 列数: 1
2025-08-04 23:06:03 线程1：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-04 23:06:03 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-04 23:06:03 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0
2025-08-04 23:06:03 线程1：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-04 23:06:07 [信息] UniformGrid列数已更新为: 1
2025-08-04 23:06:07 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-08-04 23:06:07 线程2：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-04 23:06:07 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-04 23:06:07 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36
2025-08-04 23:06:07 线程2：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-04 23:06:07 [信息] UniformGrid列数已更新为: 2
2025-08-04 23:06:07 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 3, 列数: 2
2025-08-04 23:06:07 线程3：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-04 23:06:07 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-04 23:06:07 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36 Edg/119.0.0.0
2025-08-04 23:06:07 线程3：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-04 23:06:09 线程1：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-04 23:06:09 线程3：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-04 23:06:09 线程2：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-04 23:06:11 线程3：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-04 23:06:11 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-04 23:06:11 线程3：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-04 23:06:11 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 23:06:11 线程3：[信息] [信息] 多线程模式使用指纹地理位置: -23.6509, -70.3975 (进度: 0%)
2025-08-04 23:06:11 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-23.6509, 经度=-70.3975
2025-08-04 23:06:11 线程1：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-04 23:06:11 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-04 23:06:11 线程1：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-04 23:06:11 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 23:06:11 线程1：[信息] [信息] 多线程模式使用指纹地理位置: -33.4489, -70.6693 (进度: 0%)
2025-08-04 23:06:11 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-33.4489, 经度=-70.6693
2025-08-04 23:06:11 线程2：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-04 23:06:11 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-04 23:06:11 线程2：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-04 23:06:11 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 23:06:11 线程2：[信息] [信息] 多线程模式使用指纹地理位置: -33.4489, -70.6693 (进度: 0%)
2025-08-04 23:06:11 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-33.4489, 经度=-70.6693
2025-08-04 23:06:11 线程2：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_008, CPU: 8核 (进度: 0%)
2025-08-04 23:06:11 [信息] 浏览器指纹注入: Canvas=canvas_fp_008, WebGL=ANGLE (NVIDIA GeForce RTX 3070 Direct3D11 vs_5_0 ps_5_0), CPU=8核, RAM=4 GB
2025-08-04 23:06:11 线程3：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_008, CPU: 32核 (进度: 0%)
2025-08-04 23:06:11 [信息] 浏览器指纹注入: Canvas=canvas_fp_008, WebGL=ANGLE (Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0), CPU=32核, RAM=6 GB
2025-08-04 23:06:11 线程1：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_002, CPU: 20核 (进度: 0%)
2025-08-04 23:06:11 [信息] 浏览器指纹注入: Canvas=canvas_fp_002, WebGL=ANGLE (NVIDIA GeForce GTX 1660 Direct3D11 vs_5_0 ps_5_0), CPU=20核, RAM=6 GB
2025-08-04 23:06:13 线程3：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-04 23:06:13 线程1：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-04 23:06:13 线程2：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-04 23:06:15 线程3：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 32
   • 设备内存: 6 GB
   • 平台信息: Win32
   • Do Not Track: undefined

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (Intel Technologies)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 5A6B7C8D
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_009
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-U6V7W8X
   • MAC地址: DE-F0-12-34-56-78
   • 屏幕分辨率: 2043x1162
   • 可用区域: 2043x1122

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: C3D4E5F6
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: 2g
   • 电池API支持: True
   • 电池电量: 0.63
   • 电池充电状态: False
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-04 23:06:15 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 32    • 设备内存: 6 GB    • 平台信息: Win32    • Do Not Track: undefined   图形渲染信息:    • WebGL渲染器: ANGLE (Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (Intel Technologies)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 5A6B7C8D    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_009    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-U6V7W8X    • MAC地址: DE-F0-12-34-56-78    • 屏幕分辨率: 2043x1162    • 可用区域: 2043x1122   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: C3D4E5F6    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: 2g    • 电池API支持: True    • 电池电量: 0.63    • 电池充电状态: False    • 性能API支持: True  ================================
2025-08-04 23:06:15 线程1：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 20
   • 设备内存: 6 GB
   • 平台信息: Win32
   • Do Not Track: manual

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (NVIDIA GeForce GTX 1660 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (AMD Inc.)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 1C2D3E4F
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_012
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-G5H6I7J
   • MAC地址: 56-78-9A-BC-DE-F0
   • 屏幕分辨率: 1897x1048
   • 可用区域: 1897x1008

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: D79834F2
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: wimax
   • 电池API支持: True
   • 电池电量: 0.81
   • 电池充电状态: False
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-04 23:06:15 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 20    • 设备内存: 6 GB    • 平台信息: Win32    • Do Not Track: manual   图形渲染信息:    • WebGL渲染器: ANGLE (NVIDIA GeForce GTX 1660 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (AMD Inc.)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 1C2D3E4F    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_012    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-G5H6I7J    • MAC地址: 56-78-9A-BC-DE-F0    • 屏幕分辨率: 1897x1048    • 可用区域: 1897x1008   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: D79834F2    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: wimax    • 电池API支持: True    • 电池电量: 0.81    • 电池充电状态: False    • 性能API支持: True  ================================
2025-08-04 23:06:15 线程2：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 8
   • 设备内存: 4 GB
   • 平台信息: Win32
   • Do Not Track: default

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 3070 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (AMD Technologies)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 7E8F9A0B
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_006
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-K8L9M0N
   • MAC地址: DE-F0-12-34-56-78
   • 屏幕分辨率: 2067x993
   • 可用区域: 2067x953

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: A7B8C9D0
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: ethernet
   • 电池API支持: True
   • 电池电量: 0.86
   • 电池充电状态: True
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-04 23:06:15 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 8    • 设备内存: 4 GB    • 平台信息: Win32    • Do Not Track: default   图形渲染信息:    • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 3070 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (AMD Technologies)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 7E8F9A0B    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_006    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-K8L9M0N    • MAC地址: DE-F0-12-34-56-78    • 屏幕分辨率: 2067x993    • 可用区域: 2067x953   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: A7B8C9D0    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: ethernet    • 电池API支持: True    • 电池电量: 0.86    • 电池充电状态: True    • 性能API支持: True  ================================
2025-08-04 23:06:15 线程3：[信息] [信息] 已设置浏览器标题: 线程3 - AWS注册工具 (进度: 5%)
2025-08-04 23:06:15 线程3：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-04 23:06:15 线程3：[信息] 浏览器启动成功
2025-08-04 23:06:15 线程3：[信息] 获取下一个数据: <EMAIL>
2025-08-04 23:06:15 线程3：[信息] 开始处理账户: <EMAIL>
2025-08-04 23:06:15 线程3：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-04 23:06:15 线程3：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-04 23:06:15 线程3：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-04 23:06:15 线程3：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-04 23:06:15 线程3：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-04 23:06:15 线程2：[信息] [信息] 已设置浏览器标题: 线程2 - AWS注册工具 (进度: 5%)
2025-08-04 23:06:15 线程2：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-04 23:06:15 线程2：[信息] 浏览器启动成功
2025-08-04 23:06:15 线程2：[信息] 获取下一个数据: <EMAIL>
2025-08-04 23:06:16 线程2：[信息] 开始处理账户: <EMAIL>
2025-08-04 23:06:16 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-04 23:06:16 线程2：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-04 23:06:16 线程2：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-04 23:06:16 线程2：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-04 23:06:16 线程2：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-04 23:06:16 线程1：[信息] [信息] 已设置浏览器标题: 线程1 - AWS注册工具 (进度: 5%)
2025-08-04 23:06:16 线程1：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-04 23:06:16 线程1：[信息] 浏览器启动成功
2025-08-04 23:06:16 线程1：[信息] 获取下一个数据: <EMAIL>
2025-08-04 23:06:17 线程1：[信息] 开始处理账户: <EMAIL>
2025-08-04 23:06:17 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-04 23:06:17 线程1：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-04 23:06:17 线程1：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-04 23:06:17 线程1：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-04 23:06:17 线程1：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-04 23:06:17 线程3：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-04 23:06:17 线程2：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-04 23:06:17 线程3：[信息] [信息] 已重新设置浏览器标题: 线程3 - AWS注册工具 (进度: 98%)
2025-08-04 23:06:17 线程3：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-04 23:06:17 线程2：[信息] [信息] 已重新设置浏览器标题: 线程2 - AWS注册工具 (进度: 98%)
2025-08-04 23:06:17 线程2：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-04 23:06:17 线程3：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-04 23:06:17 线程3：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-04 23:06:17 线程3：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-04 23:06:17 线程2：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-04 23:06:17 线程2：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-04 23:06:17 线程2：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-04 23:06:18 线程1：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-04 23:06:18 线程1：[信息] [信息] 已重新设置浏览器标题: 线程1 - AWS注册工具 (进度: 98%)
2025-08-04 23:06:18 线程1：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-04 23:06:18 线程1：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-04 23:06:18 线程1：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0... (进度: 98%)
2025-08-04 23:06:18 线程1：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-04 23:06:43 线程1：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程1 - AWS注册 (进度: 98%)
2025-08-04 23:06:43 线程1：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-04 23:06:43 线程1：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-04 23:06:43 线程1：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-04 23:06:43 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-04 23:06:44 线程1：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-04 23:06:46 线程3：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程3 - AWS注册 (进度: 98%)
2025-08-04 23:06:46 线程3：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-04 23:06:46 线程3：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-04 23:06:46 线程2：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程2 - AWS注册 (进度: 98%)
2025-08-04 23:06:46 线程2：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-04 23:06:46 线程2：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-04 23:06:46 线程3：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-04 23:06:46 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-04 23:06:46 线程2：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-04 23:06:46 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-04 23:06:46 线程3：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-04 23:06:46 线程2：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-04 23:06:47 线程1：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-04 23:06:47 线程1：[信息] [信息] ⚠️ 检测到错误信息，开始重试机制... (进度: 98%)
2025-08-04 23:06:47 [信息] 检测到错误信息，开始重试机制
2025-08-04 23:06:47 线程1：[信息] [信息] 🔄 第1次重试点击验证邮箱按钮... (进度: 98%)
2025-08-04 23:06:47 [信息] 第1次重试点击验证邮箱按钮
2025-08-04 23:06:49 线程1：[信息] [信息] ✅ 第1次重试：已点击验证邮箱按钮 (进度: 100%)
2025-08-04 23:06:49 [信息] 第1次重试：已点击验证邮箱按钮
2025-08-04 23:06:49 线程3：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-04 23:06:49 线程3：[信息] [信息] ⚠️ 检测到错误信息，开始重试机制... (进度: 98%)
2025-08-04 23:06:49 [信息] 检测到错误信息，开始重试机制
2025-08-04 23:06:49 线程3：[信息] [信息] 🔄 第1次重试点击验证邮箱按钮... (进度: 98%)
2025-08-04 23:06:49 [信息] 第1次重试点击验证邮箱按钮
2025-08-04 23:06:49 线程2：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-04 23:06:49 线程2：[信息] [信息] ⚠️ 检测到错误信息，开始重试机制... (进度: 98%)
2025-08-04 23:06:49 [信息] 检测到错误信息，开始重试机制
2025-08-04 23:06:49 线程2：[信息] [信息] 🔄 第1次重试点击验证邮箱按钮... (进度: 98%)
2025-08-04 23:06:49 [信息] 第1次重试点击验证邮箱按钮
2025-08-04 23:06:51 线程1：[信息] [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']" (进度: 100%)
2025-08-04 23:06:51 [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']"
2025-08-04 23:06:51 线程1：[信息] [信息] ✅ 第1次重试成功：已到达第二页，继续正常流程 (进度: 100%)
2025-08-04 23:06:51 [信息] 第1次重试成功：已到达第二页
2025-08-04 23:06:51 线程1：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-04 23:06:51 线程1：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-04 23:06:51 线程1：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 23:06:51 线程1：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-04 23:06:51 线程3：[信息] [信息] ✅ 第1次重试：已点击验证邮箱按钮 (进度: 100%)
2025-08-04 23:06:51 [信息] 第1次重试：已点击验证邮箱按钮
2025-08-04 23:06:51 线程2：[信息] [信息] ✅ 第1次重试：已点击验证邮箱按钮 (进度: 100%)
2025-08-04 23:06:51 [信息] 第1次重试：已点击验证邮箱按钮
2025-08-04 23:06:53 线程1：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-04 23:06:53 线程1：[信息] [信息] 🔍 第2次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 23:06:53 线程1：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 未发现验证码 (进度: 100%)
2025-08-04 23:06:53 线程1：[信息] [信息] ✅ 第一页未检测到图形验证码，继续流程 (进度: 100%)
2025-08-04 23:06:53 线程1：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-04 23:06:53 线程1：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-04 23:06:53 线程3：[信息] [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']" (进度: 100%)
2025-08-04 23:06:53 [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']"
2025-08-04 23:06:53 线程3：[信息] [信息] ✅ 第1次重试成功：已到达第二页，继续正常流程 (进度: 100%)
2025-08-04 23:06:53 [信息] 第1次重试成功：已到达第二页
2025-08-04 23:06:53 线程3：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-04 23:06:53 线程3：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-04 23:06:53 线程1：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-04 23:06:53 线程1：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-04 23:06:53 线程1：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-04 23:06:53 线程1：[信息] 账户注册流程已启动: <EMAIL>
2025-08-04 23:06:53 线程1：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-04 23:06:53 线程3：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 23:06:53 线程3：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-04 23:06:53 [信息] [线程1] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-04 23:06:53 [信息] [线程1] 等待2秒后开始第一次触发...
2025-08-04 23:06:53 线程2：[信息] [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']" (进度: 100%)
2025-08-04 23:06:53 [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']"
2025-08-04 23:06:53 线程2：[信息] [信息] ✅ 第1次重试成功：已到达第二页，继续正常流程 (进度: 100%)
2025-08-04 23:06:53 [信息] 第1次重试成功：已到达第二页
2025-08-04 23:06:53 线程2：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-04 23:06:53 线程2：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-04 23:06:53 线程2：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 23:06:53 线程2：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-04 23:06:56 [信息] [线程1] 第1次触发邮箱验证码获取...（最多20次）
2025-08-04 23:06:56 线程3：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-04 23:06:56 线程2：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-04 23:06:56 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 23:06:56
2025-08-04 23:06:56 线程3：[信息] [信息] 🔍 第2次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 23:06:56 线程3：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 未发现验证码 (进度: 100%)
2025-08-04 23:06:56 线程3：[信息] [信息] ✅ 第一页未检测到图形验证码，继续流程 (进度: 100%)
2025-08-04 23:06:56 线程3：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-04 23:06:56 线程3：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-04 23:06:56 线程3：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-04 23:06:56 线程3：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-04 23:06:56 [信息] [线程3] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-04 23:06:56 线程3：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-04 23:06:56 线程3：[信息] 账户注册流程已启动: <EMAIL>
2025-08-04 23:06:56 线程3：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-04 23:06:56 [信息] [线程3] 等待2秒后开始第一次触发...
2025-08-04 23:06:56 线程2：[信息] [信息] 🔍 第2次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 23:06:56 线程2：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 未发现验证码 (进度: 100%)
2025-08-04 23:06:56 线程2：[信息] [信息] ✅ 第一页未检测到图形验证码，继续流程 (进度: 100%)
2025-08-04 23:06:56 线程2：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-04 23:06:56 线程2：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-04 23:06:56 线程2：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-04 23:06:56 线程2：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-04 23:06:56 线程2：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-04 23:06:56 线程2：[信息] 账户注册流程已启动: <EMAIL>
2025-08-04 23:06:56 线程2：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-04 23:06:56 [信息] [线程2] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-04 23:06:56 [信息] [线程2] 已删除旧的响应文件
2025-08-04 23:06:56 [信息] [线程2] 等待2秒后开始第一次触发...
2025-08-04 23:06:58 [信息] [线程3] 第1次触发邮箱验证码获取...（最多20次）
2025-08-04 23:06:58 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-04 23:06:58
2025-08-04 23:06:58 [信息] [线程2] 第1次触发邮箱验证码获取...（最多20次）
2025-08-04 23:06:58 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-04 23:06:58
2025-08-04 23:06:59 [信息] [线程1] 第2次触发邮箱验证码获取...（最多20次）
2025-08-04 23:06:59 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 23:06:59
2025-08-04 23:07:01 [信息] [线程3] 第2次触发邮箱验证码获取...（最多20次）
2025-08-04 23:07:01 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-04 23:07:01
2025-08-04 23:07:01 [信息] [线程2] 第2次触发邮箱验证码获取...（最多20次）
2025-08-04 23:07:01 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-04 23:07:01
2025-08-04 23:07:02 [信息] [线程1] 第3次触发邮箱验证码获取...（最多20次）
2025-08-04 23:07:02 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 23:07:02
2025-08-04 23:07:04 [信息] [线程3] 第3次触发邮箱验证码获取...（最多20次）
2025-08-04 23:07:04 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-04 23:07:04
2025-08-04 23:07:04 [信息] [线程2] 第3次触发邮箱验证码获取...（最多20次）
2025-08-04 23:07:04 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-04 23:07:04
2025-08-04 23:07:05 [信息] [线程1] 邮箱验证码获取成功: 556482，立即停止重复请求
2025-08-04 23:07:05 [信息] [线程1] 已清理请求文件，停止重复触发
2025-08-04 23:07:05 [信息] [线程1] 已清理响应文件
2025-08-04 23:07:05 线程1：[信息] [信息] 验证码获取成功: 556482，正在自动填入... (进度: 25%)
2025-08-04 23:07:05 线程1：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 25%)
2025-08-04 23:07:05 线程1：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 25%)
2025-08-04 23:07:05 线程1：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 25%)
2025-08-04 23:07:05 [信息] 线程1完成第二页事件已处理
2025-08-04 23:07:05 [信息] 线程1完成第二页，开始批量获取手机号码...
2025-08-04 23:07:05 线程1：[信息] [信息] 线程1第二页验证完成，已通知管理器 (进度: 35%)
2025-08-04 23:07:05 [信息] 开始批量获取3个手机号码，服务商: Durian
2025-08-04 23:07:05 [信息] [手机API] 批量获取3个手机号码，URL: https://api.durianrcs.com/out/ext_api/getMobile?name=oneone&ApiKey=NnlaR01xQm9hMlAwRnJDSVB2SG1kQT09&cuy=mx&pid=0209&num=3&noblack=0&serial=2&secret_key=null&vip=null
2025-08-04 23:07:06 [信息] [线程2] 邮箱验证码获取成功: 643831，立即停止重复请求
2025-08-04 23:07:06 [信息] [线程2] 已清理请求文件，停止重复触发
2025-08-04 23:07:06 [信息] [线程2] 已清理响应文件
2025-08-04 23:07:06 线程2：[信息] [信息] 验证码获取成功: 643831，正在自动填入... (进度: 25%)
2025-08-04 23:07:06 线程2：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 25%)
2025-08-04 23:07:06 线程2：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 25%)
2025-08-04 23:07:06 线程2：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 25%)
2025-08-04 23:07:06 [信息] 线程2完成第二页事件已处理
2025-08-04 23:07:06 [信息] 线程2完成第二页，手机号码已获取，无需重复获取
2025-08-04 23:07:06 线程2：[信息] [信息] 线程2第二页验证完成，已通知管理器 (进度: 35%)
2025-08-04 23:07:07 [信息] [线程3] 第4次触发邮箱验证码获取...（最多20次）
2025-08-04 23:07:07 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-04 23:07:07
2025-08-04 23:07:07 [信息] [线程3] 邮箱验证码获取成功: 266079，立即停止重复请求
2025-08-04 23:07:07 [信息] [线程3] 已清理请求文件，停止重复触发
2025-08-04 23:07:07 [信息] [线程3] 已清理响应文件
2025-08-04 23:07:07 线程3：[信息] [信息] 验证码获取成功: 266079，正在自动填入... (进度: 25%)
2025-08-04 23:07:07 线程3：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 25%)
2025-08-04 23:07:07 线程3：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 25%)
2025-08-04 23:07:07 线程3：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 25%)
2025-08-04 23:07:07 [信息] 线程3完成第二页事件已处理
2025-08-04 23:07:07 [信息] 线程3完成第二页，手机号码已获取，无需重复获取
2025-08-04 23:07:07 线程3：[信息] [信息] 线程3第二页验证完成，已通知管理器 (进度: 35%)
2025-08-04 23:07:08 线程1：[信息] [信息] 等待密码设置页面加载... (进度: 38%)
2025-08-04 23:07:08 线程1：[信息] [信息] 开始填写密码信息... (进度: 38%)
2025-08-04 23:07:08 线程1：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-04 23:07:08 线程1：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-04 23:07:08 线程1：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 38%)
2025-08-04 23:07:09 线程2：[信息] [信息] 等待密码设置页面加载... (进度: 38%)
2025-08-04 23:07:09 线程2：[信息] [信息] 开始填写密码信息... (进度: 38%)
2025-08-04 23:07:09 线程2：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-04 23:07:09 线程2：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-04 23:07:09 线程2：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 38%)
2025-08-04 23:07:09 线程1：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 38%)
2025-08-04 23:07:10 线程2：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 38%)
2025-08-04 23:07:10 线程3：[信息] [信息] 等待密码设置页面加载... (进度: 38%)
2025-08-04 23:07:10 线程3：[信息] [信息] 开始填写密码信息... (进度: 38%)
2025-08-04 23:07:11 线程3：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-04 23:07:11 线程3：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-04 23:07:11 线程3：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 38%)
2025-08-04 23:07:11 [信息] [手机API] 批量获取响应内容: {"code":200,"msg":"Success","data":["+522851083517","+526863079027","+525583343473"]}
2025-08-04 23:07:11 [信息] [手机API] 检测到数组格式，元素数量: 3
2025-08-04 23:07:11 [信息] [手机API] 批量获取成功，获得3个手机号码
2025-08-04 23:07:11 [信息] 线程1分配榴莲手机号码: +522851083517
2025-08-04 23:07:11 [信息] 线程2分配榴莲手机号码: +526863079027
2025-08-04 23:07:11 [信息] 线程3分配榴莲手机号码: +525583343473
2025-08-04 23:07:11 [信息] 榴莲API批量获取手机号码成功，已分配给3个线程
2025-08-04 23:07:11 [信息] 批量获取3个手机号码成功
2025-08-04 23:07:12 线程3：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 38%)
2025-08-04 23:07:12 线程1：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 38%)
2025-08-04 23:07:12 线程1：[信息] [信息] 等待账户类型确认页面加载... (进度: 38%)
2025-08-04 23:07:12 线程1：[信息] [信息] 开始处理账户类型确认... (进度: 38%)
2025-08-04 23:07:13 线程2：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 38%)
2025-08-04 23:07:13 线程2：[信息] [信息] 等待账户类型确认页面加载... (进度: 38%)
2025-08-04 23:07:13 线程2：[信息] [信息] 开始处理账户类型确认... (进度: 38%)
2025-08-04 23:07:15 线程3：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 38%)
2025-08-04 23:07:15 线程3：[信息] [信息] 等待账户类型确认页面加载... (进度: 38%)
2025-08-04 23:07:15 线程3：[信息] [信息] 开始处理账户类型确认... (进度: 38%)
2025-08-04 23:07:20 线程1：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 38%)
2025-08-04 23:07:20 线程1：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 38%)
2025-08-04 23:07:23 线程2：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 38%)
2025-08-04 23:07:23 线程2：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 38%)
2025-08-04 23:07:23 线程3：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 38%)
2025-08-04 23:07:23 线程3：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 38%)
2025-08-04 23:07:28 线程2：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 38%)
2025-08-04 23:07:28 [信息] 线程2获取已分配的榴莲手机号码: +526863079027
2025-08-04 23:07:28 线程2：[信息] [信息] 多线程模式：使用已分配的手机号码 +526863079027 (进度: 38%)
2025-08-04 23:07:31 线程2：[信息] [信息] 数据国家代码为CL，需要选择Chile (进度: 38%)
2025-08-04 23:07:31 线程2：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 38%)
2025-08-04 23:07:33 线程3：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 38%)
2025-08-04 23:07:33 [信息] 线程3获取已分配的榴莲手机号码: +525583343473
2025-08-04 23:07:33 线程3：[信息] [信息] 多线程模式：使用已分配的手机号码 +525583343473 (进度: 38%)
2025-08-04 23:07:35 线程2：[信息] [信息] 已选择国家: Chile (进度: 38%)
2025-08-04 23:07:35 线程2：[信息] [信息] 已成功选择国家: Chile (进度: 38%)
2025-08-04 23:07:35 线程2：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 38%)
2025-08-04 23:07:35 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-08-04 23:07:36 线程3：[信息] [信息] 数据国家代码为CL，需要选择Chile (进度: 38%)
2025-08-04 23:07:37 线程3：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 38%)
2025-08-04 23:07:38 线程3：[信息] [信息] 已选择国家: Chile (进度: 38%)
2025-08-04 23:07:38 线程3：[信息] [信息] 已成功选择国家: Chile (进度: 38%)
2025-08-04 23:07:38 线程3：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 38%)
2025-08-04 23:07:38 线程3：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-08-04 23:07:39 线程2：[信息] [信息] 已选择国家代码 +52 (进度: 38%)
2025-08-04 23:07:39 线程2：[信息] [信息] 等待后台获取的手机号码结果... (进度: 38%)
2025-08-04 23:07:40 线程2：[信息] [信息] 已自动获取并填入手机号码: +526863079027 (进度: 38%)
2025-08-04 23:07:41 线程2：[信息] [信息] 使用已获取的手机号码: +526863079027（保存本地号码: +526863079027） (进度: 38%)
2025-08-04 23:07:41 线程2：[信息] [信息] 联系信息完成，等待页面加载... (进度: 38%)
2025-08-04 23:07:42 线程1：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 38%)
2025-08-04 23:07:42 [信息] 线程1获取已分配的榴莲手机号码: +522851083517
2025-08-04 23:07:42 线程1：[信息] [信息] 多线程模式：使用已分配的手机号码 +522851083517 (进度: 38%)
2025-08-04 23:07:42 线程3：[信息] [信息] 已选择国家代码 +52 (进度: 38%)
2025-08-04 23:07:43 线程1：[信息] [信息] 数据国家代码为CL，需要选择Chile (进度: 38%)
2025-08-04 23:07:43 线程1：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 38%)
2025-08-04 23:07:43 线程3：[信息] [信息] 等待后台获取的手机号码结果... (进度: 38%)
2025-08-04 23:07:44 线程3：[信息] [信息] 已自动获取并填入手机号码: +525583343473 (进度: 38%)
2025-08-04 23:07:45 线程3：[信息] [信息] 使用已获取的手机号码: +525583343473（保存本地号码: +525583343473） (进度: 38%)
2025-08-04 23:07:45 线程3：[信息] [信息] 联系信息完成，等待页面加载... (进度: 38%)
2025-08-04 23:07:45 线程1：[信息] [信息] 已选择国家: Chile (进度: 38%)
2025-08-04 23:07:45 线程1：[信息] [信息] 已成功选择国家: Chile (进度: 38%)
2025-08-04 23:07:45 线程1：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 38%)
2025-08-04 23:07:46 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-08-04 23:07:48 线程2：[信息] [信息] 进入付款信息页面... (进度: 38%)
2025-08-04 23:07:49 线程2：[信息] [信息] 正在选择月份: April (进度: 38%)
2025-08-04 23:07:49 线程2：[信息] [信息] 已选择月份（标准选项）: April (进度: 38%)
2025-08-04 23:07:50 线程2：[信息] [信息] 正在选择年份: 2027 (进度: 38%)
2025-08-04 23:07:50 线程2：[信息] [信息] 已选择年份（标准选项）: 2027 (进度: 38%)
2025-08-04 23:07:50 线程1：[信息] [信息] 已选择国家代码 +52 (进度: 38%)
2025-08-04 23:07:51 线程2：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 38%)
2025-08-04 23:07:51 线程2：[信息] [信息] 开始填写验证码验证页面... (进度: 38%)
2025-08-04 23:07:51 线程2：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 38%)
2025-08-04 23:07:51 线程1：[信息] [信息] 等待后台获取的手机号码结果... (进度: 38%)
2025-08-04 23:07:51 线程1：[信息] [信息] 已自动获取并填入手机号码: +522851083517 (进度: 38%)
2025-08-04 23:07:52 线程3：[信息] [信息] 进入付款信息页面... (进度: 38%)
2025-08-04 23:07:52 线程1：[信息] [信息] 使用已获取的手机号码: +522851083517（保存本地号码: +522851083517） (进度: 38%)
2025-08-04 23:07:52 线程1：[信息] [信息] 联系信息完成，等待页面加载... (进度: 38%)
2025-08-04 23:07:53 线程3：[信息] [信息] 正在选择月份: August (进度: 38%)
2025-08-04 23:07:53 线程3：[信息] [信息] 已选择月份（标准选项）: August (进度: 38%)
2025-08-04 23:07:54 线程3：[信息] [信息] 正在选择年份: 2027 (进度: 38%)
2025-08-04 23:07:54 线程3：[信息] [信息] 已选择年份（标准选项）: 2027 (进度: 38%)
2025-08-04 23:07:56 线程3：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 38%)
2025-08-04 23:07:56 线程3：[信息] [信息] 开始填写验证码验证页面... (进度: 38%)
2025-08-04 23:07:56 线程3：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 38%)
2025-08-04 23:07:56 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-08-04 23:07:56 线程1：[信息] [信息] 进入付款信息页面... (进度: 38%)
2025-08-04 23:07:57 线程2：[信息] [信息] 已选择国家代码: +52 (进度: 38%)
2025-08-04 23:07:57 线程2：[信息] [信息] 已清空并重新填写手机号码: +526863079027 (进度: 38%)
2025-08-04 23:07:57 线程2：[信息] [信息] 已点击发送验证码按钮 (进度: 38%)
2025-08-04 23:07:57 线程1：[信息] [信息] 正在选择月份: January (进度: 38%)
2025-08-04 23:07:58 线程1：[信息] [信息] 已选择月份（标准选项）: January (进度: 38%)
2025-08-04 23:07:58 线程1：[信息] [信息] 正在选择年份: 2027 (进度: 38%)
2025-08-04 23:07:59 线程2：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 38%)
2025-08-04 23:07:59 线程2：[信息] [信息] ⚠️ 检测到错误信息，开始重试机制... (进度: 38%)
2025-08-04 23:07:59 [信息] 检测到错误信息，开始重试机制
2025-08-04 23:07:59 线程2：[信息] [信息] 🔄 第1次重试发送验证码按钮... (进度: 38%)
2025-08-04 23:07:59 [信息] 第1次重试发送验证码按钮
2025-08-04 23:07:59 线程1：[信息] [信息] 已选择年份（标准选项）: 2027 (进度: 38%)
2025-08-04 23:08:00 线程1：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 38%)
2025-08-04 23:08:00 线程1：[信息] [信息] 开始填写验证码验证页面... (进度: 38%)
2025-08-04 23:08:00 线程1：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 38%)
2025-08-04 23:08:02 线程3：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-08-04 23:08:02 线程2：[信息] [信息] ✅ 第1次重试：已点击发送验证码按钮 (进度: 100%)
2025-08-04 23:08:02 [信息] 第1次重试：已点击发送验证码按钮
2025-08-04 23:08:03 线程3：[信息] [信息] 已选择国家代码: +52 (进度: 38%)
2025-08-04 23:08:03 线程3：[信息] [信息] 已清空并重新填写手机号码: +525583343473 (进度: 38%)
2025-08-04 23:08:03 线程3：[信息] [信息] 已点击发送验证码按钮 (进度: 38%)
2025-08-04 23:08:05 线程2：[信息] [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']" (进度: 100%)
2025-08-04 23:08:05 [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']"
2025-08-04 23:08:05 线程2：[信息] [信息] ✅ 第1次重试成功：错误信息消失，继续正常流程 (进度: 100%)
2025-08-04 23:08:05 [信息] 第1次重试成功：错误信息消失
2025-08-04 23:08:05 线程2：[信息] [信息] 手机号码自动模式 + 图形验证码自动模式：开始自动处理... (进度: 100%)
2025-08-04 23:08:05 线程2：[信息] [信息] 自动模式：开始处理图形验证码... (进度: 100%)
2025-08-04 23:08:05 线程2：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-04 23:08:05 线程3：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 38%)
2025-08-04 23:08:05 线程3：[信息] [信息] ⚠️ 检测到错误信息，开始重试机制... (进度: 38%)
2025-08-04 23:08:05 [信息] 检测到错误信息，开始重试机制
2025-08-04 23:08:05 线程3：[信息] [信息] 🔄 第1次重试发送验证码按钮... (进度: 38%)
2025-08-04 23:08:05 [信息] 第1次重试发送验证码按钮
2025-08-04 23:08:06 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-08-04 23:08:07 线程1：[信息] [信息] 已选择国家代码: +52 (进度: 38%)
2025-08-04 23:08:08 线程1：[信息] [信息] 已清空并重新填写手机号码: +522851083517 (进度: 38%)
2025-08-04 23:08:08 线程2：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-04 23:08:08 线程2：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 23:08:08 线程1：[信息] [信息] 已点击发送验证码按钮 (进度: 38%)
2025-08-04 23:08:08 线程3：[信息] [信息] ✅ 第1次重试：已点击发送验证码按钮 (进度: 100%)
2025-08-04 23:08:08 [信息] 第1次重试：已点击发送验证码按钮
2025-08-04 23:08:10 线程1：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 38%)
2025-08-04 23:08:10 线程1：[信息] [信息] ⚠️ 检测到错误信息，开始重试机制... (进度: 38%)
2025-08-04 23:08:10 [信息] 检测到错误信息，开始重试机制
2025-08-04 23:08:10 线程1：[信息] [信息] 🔄 第1次重试发送验证码按钮... (进度: 38%)
2025-08-04 23:08:10 [信息] 第1次重试发送验证码按钮
2025-08-04 23:08:10 线程3：[信息] [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']" (进度: 100%)
2025-08-04 23:08:10 [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']"
2025-08-04 23:08:10 线程3：[信息] [信息] ✅ 第1次重试成功：错误信息消失，继续正常流程 (进度: 100%)
2025-08-04 23:08:10 [信息] 第1次重试成功：错误信息消失
2025-08-04 23:08:10 线程3：[信息] [信息] 手机号码自动模式 + 图形验证码自动模式：开始自动处理... (进度: 100%)
2025-08-04 23:08:10 线程3：[信息] [信息] 自动模式：开始处理图形验证码... (进度: 100%)
2025-08-04 23:08:10 线程3：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-04 23:08:11 线程2：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 34742 字节 (进度: 100%)
2025-08-04 23:08:11 线程2：[信息] [信息] ✅ 图片验证通过：201x70px，34742字节，复杂度符合要求 (进度: 100%)
2025-08-04 23:08:11 线程2：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 23:08:13 线程2：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"3gbbxp"},"taskId":"d71b97f0-7144-11f0-b896-029e46bc98e3"} (进度: 100%)
2025-08-04 23:08:13 线程2：[信息] [信息] 第六页第1次识别结果: 3gbbxp → 转换为小写: 3gbbxp (进度: 100%)
2025-08-04 23:08:13 线程2：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 23:08:13 线程2：[信息] [信息] 第六页已填入验证码: 3gbbxp (进度: 100%)
2025-08-04 23:08:13 线程2：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 23:08:13 线程1：[信息] [信息] ✅ 第1次重试：已点击发送验证码按钮 (进度: 100%)
2025-08-04 23:08:13 [信息] 第1次重试：已点击发送验证码按钮
2025-08-04 23:08:13 线程3：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-04 23:08:13 线程3：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 23:08:15 线程1：[信息] [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']" (进度: 100%)
2025-08-04 23:08:15 [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']"
2025-08-04 23:08:15 线程1：[信息] [信息] ✅ 第1次重试成功：错误信息消失，继续正常流程 (进度: 100%)
2025-08-04 23:08:15 [信息] 第1次重试成功：错误信息消失
2025-08-04 23:08:15 线程1：[信息] [信息] 手机号码自动模式 + 图形验证码自动模式：开始自动处理... (进度: 100%)
2025-08-04 23:08:15 线程1：[信息] [信息] 自动模式：开始处理图形验证码... (进度: 100%)
2025-08-04 23:08:15 线程1：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-04 23:08:18 线程1：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-04 23:08:18 线程1：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 23:08:21 线程2：[信息] [信息] 第1次图形验证码识别结果错误，等待新验证码 (进度: 100%)
2025-08-04 23:08:21 线程2：[信息] [信息] 第六页第1次识别异常: 验证码错误 (进度: 100%)
2025-08-04 23:08:21 线程3：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 35128 字节 (进度: 100%)
2025-08-04 23:08:21 线程3：[信息] [信息] ✅ 图片验证通过：201x70px，35128字节，复杂度符合要求 (进度: 100%)
2025-08-04 23:08:21 线程3：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 23:08:22 线程3：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"g73hr7"},"taskId":"dca42c64-7144-11f0-9f1c-62c5329370b7"} (进度: 100%)
2025-08-04 23:08:22 线程3：[信息] [信息] 第六页第1次识别结果: g73hr7 → 转换为小写: g73hr7 (进度: 100%)
2025-08-04 23:08:22 线程3：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 23:08:22 线程1：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 34816 字节 (进度: 100%)
2025-08-04 23:08:22 线程1：[信息] [信息] ✅ 图片验证通过：201x70px，34816字节，复杂度符合要求 (进度: 100%)
2025-08-04 23:08:22 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 23:08:22 线程3：[信息] [信息] 第六页已填入验证码: g73hr7 (进度: 100%)
2025-08-04 23:08:22 线程3：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 23:08:23 线程2：[信息] [信息] 第六页第2次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 23:08:23 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"5pb54b"},"taskId":"dd4cd134-7144-11f0-9ab0-ba03bdd70631"} (进度: 100%)
2025-08-04 23:08:23 线程1：[信息] [信息] 第六页第1次识别结果: 5pb54b → 转换为小写: 5pb54b (进度: 100%)
2025-08-04 23:08:23 线程1：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 23:08:23 线程1：[信息] [信息] 第六页已填入验证码: 5pb54b (进度: 100%)
2025-08-04 23:08:23 线程1：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 23:08:25 线程3：[信息] [信息] 第1次图形验证码识别成功 (进度: 100%)
2025-08-04 23:08:25 线程3：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-08-04 23:08:26 线程2：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 31344 字节 (进度: 100%)
2025-08-04 23:08:26 线程2：[信息] [信息] ✅ 图片验证通过：200x70px，31344字节，复杂度符合要求 (进度: 100%)
2025-08-04 23:08:26 线程2：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 23:08:26 线程1：[信息] [信息] 第1次图形验证码识别成功 (进度: 100%)
2025-08-04 23:08:26 线程1：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-08-04 23:08:27 线程2：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"tzpsxbs"},"taskId":"dfd5c942-7144-11f0-8267-9ab6db23b9b7"} (进度: 100%)
2025-08-04 23:08:27 线程2：[信息] [信息] 第六页第2次识别结果: tzpsxbs → 转换为小写: tzpsxbs (进度: 100%)
2025-08-04 23:08:27 线程2：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 23:08:27 线程2：[信息] [信息] 第六页已填入验证码: tzpsxbs (进度: 100%)
2025-08-04 23:08:27 线程2：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 23:08:28 线程3：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-08-04 23:08:29 线程1：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-08-04 23:08:31 线程3：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-04 23:08:31 线程3：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 23:08:31 线程3：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 23:08:31 线程3：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-08-04 23:08:32 线程2：[信息] [信息] 第2次图形验证码识别结果错误，等待新验证码 (进度: 100%)
2025-08-04 23:08:32 线程2：[信息] [信息] 第六页第2次识别异常: 验证码错误 (进度: 100%)
2025-08-04 23:08:32 线程1：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-04 23:08:32 线程1：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 23:08:32 线程1：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 23:08:32 线程1：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-08-04 23:08:34 线程2：[信息] [信息] 第六页第3次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 23:08:37 线程3：[信息] [信息] 线程3开始独立获取验证码... (进度: 100%)
2025-08-04 23:08:37 线程3：[信息] [信息] 线程3开始获取验证码，最多尝试8次，每次间隔8秒 (进度: 100%)
2025-08-04 23:08:37 线程3：[信息] [信息] 线程3第1次尝试获取验证码...（剩余7次尝试） (进度: 100%)
2025-08-04 23:08:37 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-04 23:08:37 线程2：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 31036 字节 (进度: 100%)
2025-08-04 23:08:37 线程2：[信息] [信息] ✅ 图片验证通过：200x70px，31036字节，复杂度符合要求 (进度: 100%)
2025-08-04 23:08:37 线程2：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 23:08:37 线程1：[信息] [信息] 线程1开始独立获取验证码... (进度: 100%)
2025-08-04 23:08:37 线程1：[信息] [信息] 线程1开始获取验证码，最多尝试8次，每次间隔8秒 (进度: 100%)
2025-08-04 23:08:37 线程1：[信息] [信息] 线程1第1次尝试获取验证码...（剩余7次尝试） (进度: 100%)
2025-08-04 23:08:37 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-04 23:08:38 线程3：[信息] [信息] 线程3验证码获取成功: 6932 (进度: 100%)
2025-08-04 23:08:38 [信息] 线程3手机号码已加入释放队列: +525583343473 (原因: 获取验证码成功)
2025-08-04 23:08:38 线程3：[信息] [信息] 线程3验证码获取成功: 6932，立即填入验证码... (进度: 100%)
2025-08-04 23:08:38 线程3：[信息] [信息] 第七页：找到验证码输入框 (Verify code) (进度: 100%)
2025-08-04 23:08:38 线程1：[信息] [信息] 线程1验证码获取成功: 4287 (进度: 100%)
2025-08-04 23:08:38 [信息] 线程1手机号码已加入释放队列: +522851083517 (原因: 获取验证码成功)
2025-08-04 23:08:38 线程1：[信息] [信息] 线程1验证码获取成功: 4287，立即填入验证码... (进度: 100%)
2025-08-04 23:08:38 线程1：[信息] [信息] 第七页：找到验证码输入框 (Verify code) (进度: 100%)
2025-08-04 23:08:38 线程1：[信息] [信息] 线程1已自动填入手机验证码: 4287 (进度: 100%)
2025-08-04 23:08:38 线程2：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"n47yf"},"taskId":"e645838a-7144-11f0-9ab0-ba03bdd70631"} (进度: 100%)
2025-08-04 23:08:38 线程2：[信息] [信息] 第六页第3次识别结果: n47yf → 转换为小写: n47yf (进度: 100%)
2025-08-04 23:08:38 线程2：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 23:08:38 线程2：[信息] [信息] 第六页已填入验证码: n47yf (进度: 100%)
2025-08-04 23:08:38 线程2：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 23:08:38 线程3：[信息] [信息] 线程3已自动填入手机验证码: 6932 (进度: 100%)
2025-08-04 23:08:39 线程1：[信息] [信息] 线程1正在自动点击Continue按钮... (进度: 100%)
2025-08-04 23:08:39 线程1：[信息] [信息] 线程1手机验证码验证完成，继续执行后续步骤... (进度: 100%)
2025-08-04 23:08:39 线程3：[信息] [信息] 线程3正在自动点击Continue按钮... (进度: 100%)
2025-08-04 23:08:39 线程3：[信息] [信息] 线程3手机验证码验证完成，继续执行后续步骤... (进度: 100%)
2025-08-04 23:08:42 线程1：[信息] [信息] 验证码验证完成，等待页面加载... (进度: 100%)
2025-08-04 23:08:42 线程1：[信息] [信息] 已点击完成注册按钮，正在处理... (进度: 100%)
2025-08-04 23:08:43 线程2：[信息] [信息] 第3次图形验证码识别结果错误，等待新验证码 (进度: 100%)
2025-08-04 23:08:43 线程2：[信息] [信息] 第六页第3次识别异常: 验证码错误 (进度: 100%)
2025-08-04 23:08:43 线程2：[信息] [信息] 当前验证码抓取出错，需要手动完成图形验证码，并点击继续注册 (进度: 100%)
2025-08-04 23:08:43 线程1：[信息] [信息] 等待完成注册页面加载... (进度: 100%)
2025-08-04 23:08:44 线程3：[信息] [信息] 检测到无资格错误提示: You are not eligible for new customer credits (进度: 100%)
2025-08-04 23:08:44 线程3：[信息] [信息] 线程3检测到卡号已被关联错误，注册失败 (进度: 100%)
2025-08-04 23:08:44 线程3：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：43XYmK692 ③AWS密码：eEZFya44 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联 (进度: 100%)
2025-08-04 23:08:44 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：43XYmK692 ③AWS密码：eEZFya44 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-04 23:08:44 线程3：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：43XYmK692 ③AWS密码：eEZFya44 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-04 23:08:44 [信息] 线程3请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：43XYmK692 ③AWS密码：eEZFya44 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-04 23:08:44 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：43XYmK692 ③AWS密码：eEZFya44 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-04 23:08:44 [信息] 线程3剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：43XYmK692 ③AWS密码：eEZFya44 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-04 23:08:44 线程3：[信息] 收到失败数据保存请求: 卡号已被关联, 数据: <EMAIL>
2025-08-04 23:08:44 [信息] 线程3请求保存失败数据: 卡号已被关联, 数据: <EMAIL>
2025-08-04 23:08:44 [信息] 已处理失败数据: <EMAIL>, 失败原因: 卡号已被关联
2025-08-04 23:08:44 [信息] 线程3失败数据已保存: 卡号已被关联, 数据: <EMAIL>
2025-08-04 23:08:44 线程3：[信息] [信息] 已通知保存失败数据，失败原因: 卡号已被关联 (进度: 0%)
2025-08-04 23:08:44 线程3：[信息] [信息] 线程3注册失败，数据已归类，注册流程终止 (进度: 0%)
2025-08-04 23:08:44 线程1：[信息] [信息] 自动模式验证完成，API手机号码已加入黑名单: 加入黑名单成功 (进度: 100%)
2025-08-04 23:08:46 线程1：[信息] [信息] 完成注册页面加载完成 (进度: 100%)
2025-08-04 23:08:46 线程1：[信息] [信息] 🔍 正在查找'Go to the AWS Management Console'链接... (进度: 100%)
2025-08-04 23:08:51 线程1：[信息] [信息] 🖱️ 已点击跳转管理控制台链接 (进度: 100%)
2025-08-04 23:08:51 线程1：[信息] [信息] 🔗 通过导航进入IAM安全凭证页面... (进度: 100%)
2025-08-04 23:08:51 线程1：[信息] [信息] ⏳ 等待管理控制台页面加载，智能检测'更多'按钮（超时20秒）... (进度: 100%)
2025-08-04 23:09:00 [信息] 定时检查发现2个待释放手机号码，开始批量释放
2025-08-04 23:09:00 [信息] 开始释放2个手机号码
2025-08-04 23:09:00 [信息] [手机API] 开始批量释放2个手机号码
2025-08-04 23:09:00 [信息] [手机API] 释放手机号码: +525583343473
2025-08-04 23:09:00 [信息] [手机API] 手机号码释放成功: +525583343473
2025-08-04 23:09:01 [信息] [手机API] 释放手机号码: +522851083517
2025-08-04 23:09:01 [信息] [手机API] 手机号码释放成功: +522851083517
2025-08-04 23:09:02 [信息] [手机API] 批量释放完成: 成功2个, 失败0个
2025-08-04 23:09:02 [信息] 定时批量释放完成: 批量释放完成: 成功2个, 失败0个
2025-08-04 23:09:07 线程1：[信息] [信息] ✅ 找到'更多'按钮，页面加载完成 (进度: 100%)
2025-08-04 23:09:07 线程1：[信息] [信息] 🖱️ 正在点击更多按钮... (进度: 100%)
2025-08-04 23:09:12 线程1：[信息] [信息] ✅ 成功点击更多按钮 (进度: 100%)
2025-08-04 23:09:12 [信息] 成功点击更多按钮
2025-08-04 23:09:13 线程1：[信息] [信息] 🖱️ 正在点击账户信息按钮... (进度: 100%)
2025-08-04 23:09:13 线程1：[信息] [信息] ✅ 成功点击账户信息按钮 (进度: 100%)
2025-08-04 23:09:13 [信息] 成功点击账户信息按钮
2025-08-04 23:09:14 线程1：[信息] [信息] 🖱️ 正在点击'安全凭证'菜单项... (进度: 100%)
2025-08-04 23:09:14 线程1：[信息] [信息] 🔍 正在定位'安全凭证'链接... (进度: 100%)
2025-08-04 23:09:14 线程1：[信息] [信息] ✅ 成功定位到'安全凭证'链接 (进度: 100%)
2025-08-04 23:09:14 [信息] 成功定位到'安全凭证'链接
2025-08-04 23:09:21 线程1：[信息] [信息] ✅ 成功点击'安全凭证'菜单项 (进度: 100%)
2025-08-04 23:09:21 [信息] 成功点击'安全凭证'链接
2025-08-04 23:09:21 线程1：[信息] [信息] ⏳ 等待IAM安全凭证页面加载（20秒超时，循环检测账单问题和创建密钥按钮）... (进度: 100%)
2025-08-04 23:09:34 线程2：[信息] [信息]  继续注册被调用，当前状态: WaitingForSMSVerification，当前步骤: 6 (进度: 100%)
2025-08-04 23:09:34 线程2：[信息] [信息] 第六页手动模式继续注册，检测当前页面状态... (进度: 100%)
2025-08-04 23:09:34 线程2：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-04 23:09:34 线程2：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-04 23:09:35 线程2：[信息] [信息] 🎯 找到匹配按钮: 'Continue (step 4 of 5)' → 第7页 (进度: 100%)
2025-08-04 23:09:35 线程2：[信息] [信息] ✅ 直接确认为第7页 (进度: 100%)
2025-08-04 23:09:35 线程2：[信息] [信息] 检测到已跳转到第七页，更新步骤并执行第七页逻辑 (进度: 100%)
2025-08-04 23:09:35 线程2：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-04 23:09:35 线程2：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 23:09:35 线程2：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 23:09:35 线程2：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-08-04 23:09:40 线程2：[信息] [信息] 线程2开始独立获取验证码... (进度: 100%)
2025-08-04 23:09:40 线程2：[信息] [信息] 线程2开始获取验证码，最多尝试8次，每次间隔8秒 (进度: 100%)
2025-08-04 23:09:40 线程2：[信息] [信息] 线程2第1次尝试获取验证码...（剩余7次尝试） (进度: 100%)
2025-08-04 23:09:40 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-04 23:09:40 线程2：[信息] [信息] 线程2验证码获取成功: 6247 (进度: 100%)
2025-08-04 23:09:40 [信息] 线程2手机号码已加入释放队列: +526863079027 (原因: 获取验证码成功)
2025-08-04 23:09:40 线程2：[信息] [信息] 线程2验证码获取成功: 6247，立即填入验证码... (进度: 100%)
2025-08-04 23:09:40 线程2：[信息] [信息] 第七页：找到验证码输入框 (Verify code) (进度: 100%)
2025-08-04 23:09:40 线程2：[信息] [信息] 线程2已自动填入手机验证码: 6247 (进度: 100%)
2025-08-04 23:09:45 线程1：[信息] [信息] ⚠️ 20秒超时未找到创建密钥按钮，尝试通用页面状态检查 (进度: 100%)
2025-08-04 23:09:45 线程1：[信息] [信息] 🔍 检查页面状态... (进度: 100%)
2025-08-04 23:09:45 线程1：[信息] [信息] 当前页面URL: https://us-east-1.console.aws.amazon.com/iam/home?region=ap-southeast-2#/security_credentials (进度: 100%)
2025-08-04 23:09:45 线程2：[信息] [信息] 线程2正在自动点击Continue按钮... (进度: 100%)
2025-08-04 23:09:50 线程1：[信息] [信息] ✅ 页面状态正常，开始密钥提取流程 (进度: 100%)
2025-08-04 23:09:50 线程1：[信息] [信息] 🔍 设置页面缩放为50%... (进度: 100%)
2025-08-04 23:09:50 线程2：[信息] [信息] 线程2手机验证码验证完成，继续执行后续步骤... (进度: 100%)
2025-08-04 23:09:51 线程1：[信息] [信息] ✅ 页面缩放设置完成 (进度: 100%)
2025-08-04 23:09:51 [信息] 页面缩放设置为50%完成
2025-08-04 23:09:51 线程1：[信息] [信息] 🔍 正在查找并点击'下一步'按钮关闭向导... (进度: 100%)
2025-08-04 23:09:51 [信息] 开始查找并点击'下一步'按钮关闭向导
2025-08-04 23:09:51 线程1：[信息] [信息] ✅ 找到可见的下一步按钮，使用选择器: Locator(button[data-testid='awsc-nav-services-tooltip-confirm-button']) (进度: 100%)
2025-08-04 23:09:51 [信息] 找到可见的下一步按钮，使用选择器: Locator(button[data-testid='awsc-nav-services-tooltip-confirm-button'])
2025-08-04 23:09:51 线程1：[信息] [信息] ✅ 第一次点击'下一步'按钮完成 (选择器: Locator(button[data-testid='awsc-nav-services-tooltip-confirm-button'])) (进度: 100%)
2025-08-04 23:09:51 [信息] 第一次点击'下一步'按钮完成 (选择器: Locator(button[data-testid='awsc-nav-services-tooltip-confirm-button']))
2025-08-04 23:09:52 线程1：[信息] [信息] ℹ️ 第二次点击时按钮已消失，向导已关闭 (进度: 100%)
2025-08-04 23:09:52 [信息] 第二次点击时按钮已消失，向导已关闭
2025-08-04 23:09:52 线程1：[信息] [信息] ✅ '下一步'按钮点击流程完成 (进度: 100%)
2025-08-04 23:09:52 [信息] '下一步'按钮点击流程完成
2025-08-04 23:09:52 线程1：[信息] [信息] 🔑 第四阶段：开始创建和复制访问密钥 (进度: 100%)
2025-08-04 23:09:52 [信息] 开始创建和复制访问密钥
2025-08-04 23:09:52 线程1：[信息] [信息] 🔑 正在点击'创建访问密钥'按钮... (进度: 100%)
2025-08-04 23:09:52 线程1：[信息] [信息] 🔍 正在定位'创建访问密钥'按钮... (进度: 100%)
2025-08-04 23:09:52 线程1：[信息] [信息] ✅ 成功定位到'创建访问密钥'按钮 (进度: 100%)
2025-08-04 23:09:52 [信息] 成功定位到'创建访问密钥'按钮
2025-08-04 23:09:53 线程1：[信息] [信息] ✅ 成功点击'创建访问密钥'按钮 (进度: 100%)
2025-08-04 23:09:53 [信息] 成功点击'创建访问密钥'按钮
2025-08-04 23:09:53 线程2：[信息] [信息] 验证码验证完成，等待页面加载... (进度: 100%)
2025-08-04 23:09:53 线程2：[信息] [信息] 已点击完成注册按钮，正在处理... (进度: 100%)
2025-08-04 23:09:54 线程2：[信息] [信息] 自动模式验证完成，API手机号码已加入黑名单: 加入黑名单成功 (进度: 100%)
2025-08-04 23:09:54 线程2：[信息] [信息] 等待完成注册页面加载... (进度: 100%)
2025-08-04 23:09:55 线程1：[信息] [信息] ☑️ 正在勾选确认复选框... (进度: 100%)
2025-08-04 23:09:55 线程1：[信息] [信息] ✅ 使用id属性定位到确认复选框 (进度: 100%)
2025-08-04 23:09:55 [信息] 使用id属性定位到确认复选框
2025-08-04 23:09:55 线程1：[信息] [信息] ✅ 成功勾选确认复选框 (进度: 100%)
2025-08-04 23:09:55 [信息] 成功勾选确认复选框
2025-08-04 23:09:56 线程1：[信息] [信息] 🔑 正在点击'创建访问密钥'确认按钮... (进度: 100%)
2025-08-04 23:09:56 线程1：[信息] [信息] ✅ 成功点击'创建访问密钥'确认按钮 (进度: 100%)
2025-08-04 23:09:56 [信息] 成功点击'创建访问密钥'确认按钮
2025-08-04 23:09:57 线程2：[信息] [信息] 完成注册页面加载完成 (进度: 100%)
2025-08-04 23:09:57 线程2：[信息] [信息] 🔍 正在查找'Go to the AWS Management Console'链接... (进度: 100%)
2025-08-04 23:09:59 线程2：[信息] [信息] 🖱️ 已点击跳转管理控制台链接 (进度: 100%)
2025-08-04 23:09:59 线程2：[信息] [信息] 🔗 通过导航进入IAM安全凭证页面... (进度: 100%)
2025-08-04 23:09:59 线程2：[信息] [信息] ⏳ 等待管理控制台页面加载，智能检测'更多'按钮（超时20秒）... (进度: 100%)
2025-08-04 23:09:59 线程1：[信息] [信息] 📋 开始复制访问密钥... (进度: 100%)
2025-08-04 23:09:59 [信息] 开始复制访问密钥
2025-08-04 23:10:00 [信息] 定时检查发现1个待释放手机号码，开始批量释放
2025-08-04 23:10:00 [信息] 开始释放1个手机号码
2025-08-04 23:10:00 [信息] [手机API] 开始批量释放1个手机号码
2025-08-04 23:10:00 [信息] [手机API] 释放手机号码: +526863079027
2025-08-04 23:10:00 [信息] [手机API] 手机号码释放成功: +526863079027
2025-08-04 23:10:01 [信息] [手机API] 批量释放完成: 成功1个, 失败0个
2025-08-04 23:10:01 [信息] 定时批量释放完成: 批量释放完成: 成功1个, 失败0个
2025-08-04 23:10:01 线程1：[信息] [信息] 🔍 方法2找到 2 个单元格 (进度: 100%)
2025-08-04 23:10:01 [信息] 方法2找到 2 个单元格
2025-08-04 23:10:01 线程1：[信息] [信息] 🔍 总共找到 2 个单元格，开始分析... (进度: 100%)
2025-08-04 23:10:01 [信息] 总共找到 2 个单元格，开始分析
2025-08-04 23:10:01 [信息] 单元格[0]: 'AKIAWPDS7HE7I2K4AHHW'
2025-08-04 23:10:03 [信息] 单元格[1]: '***************Mostrar'
2025-08-04 23:10:03 线程1：[信息] [信息] 📋 正在查找并复制访问密钥... (进度: 100%)
2025-08-04 23:10:06 线程1：[信息] [信息] ✅ 找到访问密钥: AKIAWPDS7HE7I2K4AHHW (进度: 100%)
2025-08-04 23:10:06 [信息] 找到访问密钥: AKIAWPDS7HE7I2K4AHHW
2025-08-04 23:10:07 线程1：[信息] [信息] ✅ 方法1成功点击访问密钥复制按钮 (进度: 100%)
2025-08-04 23:10:07 [信息] 方法1成功点击访问密钥复制按钮
2025-08-04 23:10:08 线程1：[信息] [信息] 📋 正在查找并复制秘密访问密钥... (进度: 100%)
2025-08-04 23:10:08 线程1：[信息] [信息] ✅ 找到秘密访问密钥: ***************Mostrar (进度: 100%)
2025-08-04 23:10:08 [信息] 找到秘密访问密钥: ***************Mostrar
2025-08-04 23:10:08 线程1：[信息] [信息] 🔍 正在寻找向导阻挡... (进度: 100%)
2025-08-04 23:10:08 线程1：[信息] [信息] 🔍 检测到隐藏的秘密访问密钥，尝试点击显示按钮... (进度: 100%)
2025-08-04 23:10:08 [信息] 检测到隐藏的秘密访问密钥，尝试点击显示按钮
2025-08-04 23:10:08 线程1：[信息] [信息] ✅ 使用TestId定位到显示按钮 (进度: 100%)
2025-08-04 23:10:08 [信息] 使用TestId定位到显示按钮
2025-08-04 23:10:09 线程1：[信息] [信息] ✅ 显示按钮点击成功，新文本: fB5f/Ose8CxS2G31/8CqFLi05YXDn3ZwHXq5PvQdOcultar (进度: 100%)
2025-08-04 23:10:09 [信息] 显示按钮点击成功，新文本: fB5f/Ose8CxS2G31/8CqFLi05YXDn3ZwHXq5PvQdOcultar
2025-08-04 23:10:09 线程1：[信息] [信息] ✅ 直接从显示文本提取秘密访问密钥: fB5f/Ose8CxS2G31/8CqFLi05YXDn3ZwHXq5PvQdOcultar (进度: 100%)
2025-08-04 23:10:09 [信息] 直接从显示文本提取秘密访问密钥: fB5f/Ose8CxS2G31/8CqFLi05YXDn3ZwHXq5PvQdOcultar
2025-08-04 23:10:09 线程1：[信息] [信息] ✅ 访问密钥复制完成 (进度: 100%)
2025-08-04 23:10:09 [信息] 访问密钥复制完成 - AccessKey: AKIAWPDS7HE7I2K4AHHW, SecretKey: fB5f/Ose8CxS2G31/8CqFLi05YXDn3ZwHXq5PvQdOcultar
2025-08-04 23:10:10 线程1：[信息] [信息] 🖱️ 正在点击'已完成'按钮... (进度: 100%)
2025-08-04 23:10:12 线程1：[信息] [信息] ✅ 成功点击'已完成'按钮 (进度: 100%)
2025-08-04 23:10:12 [信息] 成功点击'已完成'按钮
2025-08-04 23:10:12 线程1：[信息] [信息] ✅ 密钥已保存到数据对象 - AccessKey: AKIAWPDS7HE7I2K4AHHW, SecretKey: fB5f/Ose8CxS2G31/8CqFLi05YXDn3ZwHXq5PvQdOcultar (进度: 100%)
2025-08-04 23:10:12 [信息] 密钥已保存到数据对象 - AccessKey: AKIAWPDS7HE7I2K4AHHW, SecretKey: fB5f/Ose8CxS2G31/8CqFLi05YXDn3ZwHXq5PvQdOcultar
2025-08-04 23:10:13 线程1：[信息] [信息] 🖱️ 正在点击'继续'按钮... (进度: 100%)
2025-08-04 23:10:16 线程1：[信息] [信息] ❌ 点击'继续'按钮失败: Timeout 3000ms exceeded.
Call log:
  - waiting for Locator("button[data-awsui-analytics*='navigate']") to be visible (进度: 100%)
2025-08-04 23:10:16 [信息] 点击'继续'按钮失败: Timeout 3000ms exceeded.
Call log:
  - waiting for Locator("button[data-awsui-analytics*='navigate']") to be visible
2025-08-04 23:10:16 线程1：[信息] [信息] 🔐 开始设置MFA设备... (进度: 100%)
2025-08-04 23:10:16 [信息] 开始设置MFA设备
2025-08-04 23:10:16 线程1：[信息] [信息] 🖱️ 正在点击'分配 MFA 设备'按钮... (进度: 100%)
2025-08-04 23:10:17 线程1：[信息] [信息] ✅ 成功点击'分配 MFA 设备'按钮 (进度: 100%)
2025-08-04 23:10:17 [信息] 成功点击'分配 MFA 设备'按钮
2025-08-04 23:10:17 线程1：[信息] [信息] ⏳ 等待MFA设备设置页面加载（20秒超时）... (进度: 100%)
2025-08-04 23:10:20 [信息] 获取线程1当前数据: <EMAIL>
2025-08-04 23:10:20 线程1：[信息] 终止时正在处理的数据: <EMAIL>
2025-08-04 23:10:20 线程1：[信息] 数据详情: <EMAIL>|e6l1HngP|Carrasco Alex|CAP|Avenida Per 8796|Region Metropolitana|Santiago|8230000|4513680566170214|01|27|648|Carrasco Alex|0Q088N038WY|CL
2025-08-04 23:10:20 线程1：[信息] 多线程终止 - 复制密钥信息到剪贴板
2025-08-04 23:10:26 线程1：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：0Q088N038WY ③AWS密码：e6l1HngP ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止 (进度: 100%)
2025-08-04 23:10:26 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：0Q088N038WY ③AWS密码：e6l1HngP ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 23:10:26 线程1：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：0Q088N038WY ③AWS密码：e6l1HngP ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 23:10:26 [信息] 线程1请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：0Q088N038WY ③AWS密码：e6l1HngP ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 23:10:26 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：0Q088N038WY ③AWS密码：e6l1HngP ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 23:10:26 [信息] 线程1剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：0Q088N038WY ③AWS密码：e6l1HngP ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 23:10:26 线程1：[信息] [信息] 注册已终止 (进度: 100%)
2025-08-04 23:10:26 线程1：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-08-04 23:10:28 线程1：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：0Q088N038WY ③AWS密码：e6l1HngP ④访问密钥：AKIAWPDS7HE7I2K4AHHW ⑤秘密访问密钥：fB5f/Ose8CxS2G31/8CqFLi05YXDn3ZwHXq5PvQdOcultar ⑥MFA信息：   //手动终止 (进度: 100%)
2025-08-04 23:10:28 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：0Q088N038WY ③AWS密码：e6l1HngP ④访问密钥：AKIAWPDS7HE7I2K4AHHW ⑤秘密访问密钥：fB5f/Ose8CxS2G31/8CqFLi05YXDn3ZwHXq5PvQdOcultar ⑥MFA信息：   //手动终止
2025-08-04 23:10:28 线程1：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：0Q088N038WY ③AWS密码：e6l1HngP ④访问密钥：AKIAWPDS7HE7I2K4AHHW ⑤秘密访问密钥：fB5f/Ose8CxS2G31/8CqFLi05YXDn3ZwHXq5PvQdOcultar ⑥MFA信息：   //手动终止
2025-08-04 23:10:28 [信息] 线程1请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：0Q088N038WY ③AWS密码：e6l1HngP ④访问密钥：AKIAWPDS7HE7I2K4AHHW ⑤秘密访问密钥：fB5f/Ose8CxS2G31/8CqFLi05YXDn3ZwHXq5PvQdOcultar ⑥MFA信息：   //手动终止
2025-08-04 23:10:28 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：0Q088N038WY ③AWS密码：e6l1HngP ④访问密钥：AKIAWPDS7HE7I2K4AHHW ⑤秘密访问密钥：fB5f/Ose8CxS2G31/8CqFLi05YXDn3ZwHXq5PvQdOcultar ⑥MFA信息：   //手动终止
2025-08-04 23:10:28 [信息] 线程1剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：0Q088N038WY ③AWS密码：e6l1HngP ④访问密钥：AKIAWPDS7HE7I2K4AHHW ⑤秘密访问密钥：fB5f/Ose8CxS2G31/8CqFLi05YXDn3ZwHXq5PvQdOcultar ⑥MFA信息：   //手动终止
2025-08-04 23:10:28 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-08-04 23:10:28 线程1：[信息] 临时目录已清理: C:\Users\<USER>\AppData\Local\Temp\AWS_Thread_1_20250804_230600
2025-08-04 23:10:28 线程1：[信息] 已终止
2025-08-04 23:10:28 [信息] 线程1已终止
2025-08-04 23:10:28 [信息] 开始处理线程1终止数据，共1个数据
2025-08-04 23:10:28 [信息] 处理线程1终止数据: <EMAIL>
2025-08-04 23:10:28 [信息] 从注册数据列表中移除: <EMAIL>
2025-08-04 23:10:28 [信息] 线程1终止 - 数据已移动到终止列表: <EMAIL>
2025-08-04 23:10:28 [信息] 线程1终止数据处理完成，成功移动1个数据
2025-08-04 23:10:28 [信息] UniformGrid列数已更新为: 1
2025-08-04 23:10:28 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-08-04 23:10:28 [信息] 线程1已终止
2025-08-04 23:10:28 线程2：[信息] [信息] ⚠️ 20秒内未找到'更多'按钮，继续执行后续流程... (进度: 100%)
2025-08-04 23:10:28 [信息] 20秒内未找到'更多'按钮，但继续执行
2025-08-04 23:10:28 线程2：[信息] [信息] 🔍 智能检测更多按钮... (进度: 100%)
2025-08-04 23:10:28 线程2：[信息] [信息] 🔍 第1次检查更多按钮... (进度: 100%)
2025-08-04 23:10:28 线程2：[信息] [信息] ✅ 第1次检查成功找到更多按钮 (进度: 100%)
2025-08-04 23:10:28 [信息] 第1次检查成功找到更多按钮
2025-08-04 23:10:28 线程2：[信息] [信息] 🖱️ 正在点击更多按钮... (进度: 100%)
2025-08-04 23:10:29 线程2：[信息] [信息] ✅ 成功点击更多按钮 (进度: 100%)
2025-08-04 23:10:29 [信息] 成功点击更多按钮
2025-08-04 23:10:30 线程2：[信息] [信息] 🖱️ 正在点击账户信息按钮... (进度: 100%)
2025-08-04 23:10:31 线程2：[信息] [信息] ✅ 成功点击账户信息按钮 (进度: 100%)
2025-08-04 23:10:31 [信息] 成功点击账户信息按钮
2025-08-04 23:10:32 线程2：[信息] [信息] 🖱️ 正在点击'安全凭证'菜单项... (进度: 100%)
2025-08-04 23:10:32 线程2：[信息] [信息] 🔍 正在定位'安全凭证'链接... (进度: 100%)
2025-08-04 23:10:32 线程2：[信息] [信息] ✅ 成功定位到'安全凭证'链接 (进度: 100%)
2025-08-04 23:10:32 [信息] 成功定位到'安全凭证'链接
2025-08-04 23:10:37 线程1：[信息] [信息] ⚠️ 20秒内未找到'设备名称'输入框，需要手动处理 (进度: 0%)
2025-08-04 23:10:37 线程1：[信息] [信息] ⚠️ 设备名称页面加载超时，需要手动处理 (进度: 0%)
2025-08-04 23:10:37 [信息] 设备名称页面加载超时，需要手动处理
2025-08-04 23:10:37 线程1：[信息] [信息] 页面加载完成后，手动点击继续注册 (进度: 0%)
2025-08-04 23:10:37 线程1：[信息] [信息] MFA页面需要手动处理，等待用户操作 (进度: 0%)
2025-08-04 23:10:52 线程2：[信息] [信息] ✅ 成功点击'安全凭证'菜单项 (进度: 100%)
2025-08-04 23:10:52 [信息] 成功点击'安全凭证'链接
2025-08-04 23:10:52 线程2：[信息] [信息] ⏳ 等待IAM安全凭证页面加载（20秒超时，循环检测账单问题和创建密钥按钮）... (进度: 100%)
2025-08-04 23:11:06 线程2：[信息] [信息] ✅ 找到'创建访问密钥'按钮，页面加载完成 (进度: 100%)
2025-08-04 23:11:06 线程2：[信息] [信息] ✅ 页面状态正常，找到创建密钥按钮，开始密钥提取流程 (进度: 100%)
2025-08-04 23:11:06 线程2：[信息] [信息] 🔍 设置页面缩放为50%... (进度: 100%)
2025-08-04 23:11:08 线程2：[信息] [信息] ✅ 页面缩放设置完成 (进度: 100%)
2025-08-04 23:11:08 [信息] 页面缩放设置为50%完成
2025-08-04 23:11:08 线程2：[信息] [信息] 🔍 正在查找并点击'下一步'按钮关闭向导... (进度: 100%)
2025-08-04 23:11:08 [信息] 开始查找并点击'下一步'按钮关闭向导
2025-08-04 23:11:08 线程2：[信息] [信息] ✅ 找到可见的下一步按钮，使用选择器: Locator(button[data-testid='awsc-nav-services-tooltip-confirm-button']) (进度: 100%)
2025-08-04 23:11:08 [信息] 找到可见的下一步按钮，使用选择器: Locator(button[data-testid='awsc-nav-services-tooltip-confirm-button'])
2025-08-04 23:11:08 线程2：[信息] [信息] ✅ 第一次点击'下一步'按钮完成 (选择器: Locator(button[data-testid='awsc-nav-services-tooltip-confirm-button'])) (进度: 100%)
2025-08-04 23:11:08 [信息] 第一次点击'下一步'按钮完成 (选择器: Locator(button[data-testid='awsc-nav-services-tooltip-confirm-button']))
2025-08-04 23:11:09 线程2：[信息] [信息] ℹ️ 第二次点击时按钮已消失，向导已关闭 (进度: 100%)
2025-08-04 23:11:09 [信息] 第二次点击时按钮已消失，向导已关闭
2025-08-04 23:11:09 线程2：[信息] [信息] ✅ '下一步'按钮点击流程完成 (进度: 100%)
2025-08-04 23:11:09 [信息] '下一步'按钮点击流程完成
2025-08-04 23:11:09 线程2：[信息] [信息] 🔑 第四阶段：开始创建和复制访问密钥 (进度: 100%)
2025-08-04 23:11:09 [信息] 开始创建和复制访问密钥
2025-08-04 23:11:09 线程2：[信息] [信息] 🔑 正在点击'创建访问密钥'按钮... (进度: 100%)
2025-08-04 23:11:09 线程2：[信息] [信息] 🔍 正在定位'创建访问密钥'按钮... (进度: 100%)
2025-08-04 23:11:09 线程2：[信息] [信息] ✅ 成功定位到'创建访问密钥'按钮 (进度: 100%)
2025-08-04 23:11:09 [信息] 成功定位到'创建访问密钥'按钮
2025-08-04 23:11:10 线程2：[信息] [信息] ✅ 成功点击'创建访问密钥'按钮 (进度: 100%)
2025-08-04 23:11:10 [信息] 成功点击'创建访问密钥'按钮
2025-08-04 23:11:12 线程2：[信息] [信息] ☑️ 正在勾选确认复选框... (进度: 100%)
2025-08-04 23:11:12 线程2：[信息] [信息] ✅ 使用id属性定位到确认复选框 (进度: 100%)
2025-08-04 23:11:12 [信息] 使用id属性定位到确认复选框
2025-08-04 23:11:12 线程2：[信息] [信息] ✅ 成功勾选确认复选框 (进度: 100%)
2025-08-04 23:11:12 [信息] 成功勾选确认复选框
2025-08-04 23:11:13 线程2：[信息] [信息] 🔑 正在点击'创建访问密钥'确认按钮... (进度: 100%)
2025-08-04 23:11:13 线程2：[信息] [信息] ✅ 成功点击'创建访问密钥'确认按钮 (进度: 100%)
2025-08-04 23:11:13 [信息] 成功点击'创建访问密钥'确认按钮
2025-08-04 23:11:16 线程2：[信息] [信息] 📋 开始复制访问密钥... (进度: 100%)
2025-08-04 23:11:16 [信息] 开始复制访问密钥
2025-08-04 23:11:18 线程2：[信息] [信息] 🔍 方法2找到 2 个单元格 (进度: 100%)
2025-08-04 23:11:18 [信息] 方法2找到 2 个单元格
2025-08-04 23:11:18 线程2：[信息] [信息] 🔍 总共找到 2 个单元格，开始分析... (进度: 100%)
2025-08-04 23:11:18 [信息] 总共找到 2 个单元格，开始分析
2025-08-04 23:11:18 [信息] 单元格[0]: 'AKIAQ2YXIGKVMYM6N6PS'
2025-08-04 23:11:18 [信息] 单元格[1]: '***************Mostrar'
2025-08-04 23:11:18 线程2：[信息] [信息] 📋 正在查找并复制访问密钥... (进度: 100%)
2025-08-04 23:11:19 线程2：[信息] [信息] ✅ 找到访问密钥: AKIAQ2YXIGKVMYM6N6PS (进度: 100%)
2025-08-04 23:11:19 [信息] 找到访问密钥: AKIAQ2YXIGKVMYM6N6PS
2025-08-04 23:11:19 线程2：[信息] [信息] ✅ 方法1成功点击访问密钥复制按钮 (进度: 100%)
2025-08-04 23:11:19 [信息] 方法1成功点击访问密钥复制按钮
2025-08-04 23:11:20 线程2：[信息] [信息] 📋 正在查找并复制秘密访问密钥... (进度: 100%)
2025-08-04 23:11:20 线程2：[信息] [信息] ✅ 找到秘密访问密钥: ***************Mostrar (进度: 100%)
2025-08-04 23:11:20 [信息] 找到秘密访问密钥: ***************Mostrar
2025-08-04 23:11:20 线程2：[信息] [信息] 🔍 正在寻找向导阻挡... (进度: 100%)
2025-08-04 23:11:20 线程2：[信息] [信息] 🔍 检测到隐藏的秘密访问密钥，尝试点击显示按钮... (进度: 100%)
2025-08-04 23:11:20 [信息] 检测到隐藏的秘密访问密钥，尝试点击显示按钮
2025-08-04 23:11:20 线程2：[信息] [信息] ✅ 使用TestId定位到显示按钮 (进度: 100%)
2025-08-04 23:11:20 [信息] 使用TestId定位到显示按钮
2025-08-04 23:11:21 线程2：[信息] [信息] ✅ 显示按钮点击成功，新文本: 5XkgRmX/XKVzVCob7xzoSDN0DjIT3gNX5db9CULhOcultar (进度: 100%)
2025-08-04 23:11:21 [信息] 显示按钮点击成功，新文本: 5XkgRmX/XKVzVCob7xzoSDN0DjIT3gNX5db9CULhOcultar
2025-08-04 23:11:21 线程2：[信息] [信息] ✅ 直接从显示文本提取秘密访问密钥: 5XkgRmX/XKVzVCob7xzoSDN0DjIT3gNX5db9CULhOcultar (进度: 100%)
2025-08-04 23:11:21 [信息] 直接从显示文本提取秘密访问密钥: 5XkgRmX/XKVzVCob7xzoSDN0DjIT3gNX5db9CULhOcultar
2025-08-04 23:11:21 线程2：[信息] [信息] ✅ 访问密钥复制完成 (进度: 100%)
2025-08-04 23:11:21 [信息] 访问密钥复制完成 - AccessKey: AKIAQ2YXIGKVMYM6N6PS, SecretKey: 5XkgRmX/XKVzVCob7xzoSDN0DjIT3gNX5db9CULhOcultar
2025-08-04 23:11:22 线程2：[信息] [信息] 🖱️ 正在点击'已完成'按钮... (进度: 100%)
2025-08-04 23:11:23 线程2：[信息] [信息] ✅ 成功点击'已完成'按钮 (进度: 100%)
2025-08-04 23:11:23 [信息] 成功点击'已完成'按钮
2025-08-04 23:11:23 线程2：[信息] [信息] ✅ 密钥已保存到数据对象 - AccessKey: AKIAQ2YXIGKVMYM6N6PS, SecretKey: 5XkgRmX/XKVzVCob7xzoSDN0DjIT3gNX5db9CULhOcultar (进度: 100%)
2025-08-04 23:11:23 [信息] 密钥已保存到数据对象 - AccessKey: AKIAQ2YXIGKVMYM6N6PS, SecretKey: 5XkgRmX/XKVzVCob7xzoSDN0DjIT3gNX5db9CULhOcultar
2025-08-04 23:11:24 线程2：[信息] [信息] 🖱️ 正在点击'继续'按钮... (进度: 100%)
2025-08-04 23:11:27 线程2：[信息] [信息] ❌ 点击'继续'按钮失败: Timeout 3000ms exceeded.
Call log:
  - waiting for Locator("button[data-awsui-analytics*='navigate']") to be visible (进度: 100%)
2025-08-04 23:11:27 [信息] 点击'继续'按钮失败: Timeout 3000ms exceeded.
Call log:
  - waiting for Locator("button[data-awsui-analytics*='navigate']") to be visible
2025-08-04 23:11:27 线程2：[信息] [信息] 🔐 开始设置MFA设备... (进度: 100%)
2025-08-04 23:11:27 [信息] 开始设置MFA设备
2025-08-04 23:11:27 线程2：[信息] [信息] 🖱️ 正在点击'分配 MFA 设备'按钮... (进度: 100%)
2025-08-04 23:11:28 线程2：[信息] [信息] ✅ 成功点击'分配 MFA 设备'按钮 (进度: 100%)
2025-08-04 23:11:28 [信息] 成功点击'分配 MFA 设备'按钮
2025-08-04 23:11:28 线程2：[信息] [信息] ⏳ 等待MFA设备设置页面加载（20秒超时）... (进度: 100%)
2025-08-04 23:11:49 线程2：[信息] [信息] ⚠️ 20秒内未找到'设备名称'输入框，需要手动处理 (进度: 100%)
2025-08-04 23:11:49 线程2：[信息] [信息] ⚠️ 设备名称页面加载超时，需要手动处理 (进度: 100%)
2025-08-04 23:11:49 [信息] 设备名称页面加载超时，需要手动处理
2025-08-04 23:11:49 线程2：[信息] [信息] 页面加载完成后，手动点击继续注册 (进度: 100%)
2025-08-04 23:11:49 线程2：[信息] [信息] MFA页面需要手动处理，等待用户操作 (进度: 100%)
2025-08-04 23:11:49 线程2：[信息] 已继续
2025-08-04 23:11:49 [信息] 线程2已继续
2025-08-04 23:11:50 线程2：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-08-04 23:11:50 线程2：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 100%)
2025-08-04 23:11:50 线程2：[信息] 已暂停
2025-08-04 23:11:50 [信息] 线程2已暂停
2025-08-04 23:11:50 [信息] 线程2已暂停
2025-08-04 23:12:59 [信息] 获取线程2当前数据: <EMAIL>
2025-08-04 23:12:59 线程2：[信息] 终止时正在处理的数据: <EMAIL>
2025-08-04 23:12:59 线程2：[信息] 数据详情: <EMAIL>|NNL7spG6|Gatita Rosa Virginia|Antofagasta|Nataniel Cox 135 departamento|Region Metropolitana|Santiago Centro|8330238|4593110000003303|04|27|691|Gatita Rosa Virginia|gu7T420630|CL
2025-08-04 23:12:59 线程2：[信息] 多线程终止 - 复制密钥信息到剪贴板
2025-08-04 23:13:00 线程2：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：gu7T420630 ③AWS密码：NNL7spG6 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止 (进度: 100%)
2025-08-04 23:13:00 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：gu7T420630 ③AWS密码：NNL7spG6 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 23:13:00 线程2：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：gu7T420630 ③AWS密码：NNL7spG6 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 23:13:00 [信息] 线程2请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：gu7T420630 ③AWS密码：NNL7spG6 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 23:13:00 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：gu7T420630 ③AWS密码：NNL7spG6 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 23:13:00 [信息] 线程2剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：gu7T420630 ③AWS密码：NNL7spG6 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 23:13:00 线程2：[信息] [信息] 注册已终止 (进度: 100%)
2025-08-04 23:13:00 线程2：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-08-04 23:13:00 线程2：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：gu7T420630 ③AWS密码：NNL7spG6 ④访问密钥：AKIAQ2YXIGKVMYM6N6PS ⑤秘密访问密钥：5XkgRmX/XKVzVCob7xzoSDN0DjIT3gNX5db9CULhOcultar ⑥MFA信息：   //手动终止 (进度: 100%)
2025-08-04 23:13:00 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：gu7T420630 ③AWS密码：NNL7spG6 ④访问密钥：AKIAQ2YXIGKVMYM6N6PS ⑤秘密访问密钥：5XkgRmX/XKVzVCob7xzoSDN0DjIT3gNX5db9CULhOcultar ⑥MFA信息：   //手动终止
2025-08-04 23:13:00 线程2：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：gu7T420630 ③AWS密码：NNL7spG6 ④访问密钥：AKIAQ2YXIGKVMYM6N6PS ⑤秘密访问密钥：5XkgRmX/XKVzVCob7xzoSDN0DjIT3gNX5db9CULhOcultar ⑥MFA信息：   //手动终止
2025-08-04 23:13:00 [信息] 线程2请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：gu7T420630 ③AWS密码：NNL7spG6 ④访问密钥：AKIAQ2YXIGKVMYM6N6PS ⑤秘密访问密钥：5XkgRmX/XKVzVCob7xzoSDN0DjIT3gNX5db9CULhOcultar ⑥MFA信息：   //手动终止
2025-08-04 23:13:00 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：gu7T420630 ③AWS密码：NNL7spG6 ④访问密钥：AKIAQ2YXIGKVMYM6N6PS ⑤秘密访问密钥：5XkgRmX/XKVzVCob7xzoSDN0DjIT3gNX5db9CULhOcultar ⑥MFA信息：   //手动终止
2025-08-04 23:13:00 [信息] 线程2剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：gu7T420630 ③AWS密码：NNL7spG6 ④访问密钥：AKIAQ2YXIGKVMYM6N6PS ⑤秘密访问密钥：5XkgRmX/XKVzVCob7xzoSDN0DjIT3gNX5db9CULhOcultar ⑥MFA信息：   //手动终止
2025-08-04 23:13:00 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-08-04 23:13:00 线程2：[信息] 临时目录已清理: C:\Users\<USER>\AppData\Local\Temp\AWS_Thread_2_20250804_230600
2025-08-04 23:13:00 线程2：[信息] 已终止
2025-08-04 23:13:00 [信息] 线程2已终止
2025-08-04 23:13:00 [信息] 开始处理线程2终止数据，共1个数据
2025-08-04 23:13:00 [信息] 处理线程2终止数据: <EMAIL>
2025-08-04 23:13:00 [信息] 检测到多线程处理中数据为0，已重置按钮状态
2025-08-04 23:13:00 [信息] 从注册数据列表中移除: <EMAIL>
2025-08-04 23:13:00 [信息] 线程2终止 - 数据已移动到终止列表: <EMAIL>
2025-08-04 23:13:00 [信息] 线程2终止数据处理完成，成功移动1个数据
2025-08-04 23:13:00 [信息] UniformGrid列数已更新为: 1
2025-08-04 23:13:00 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 1, 列数: 1
2025-08-04 23:13:00 [信息] 线程2已终止
2025-08-04 23:13:00 [信息] 多线程窗口引用已清理
2025-08-04 23:13:00 [信息] 主窗口已恢复正常状态，多线程窗口引用已清理
2025-08-04 23:13:00 [信息] 多线程管理窗口正在关闭
2025-08-04 23:16:34 [信息] 程序正在退出，开始清理工作...
2025-08-04 23:16:34 [信息] 程序退出，开始清理所有待拉黑千川手机号码
2025-08-04 23:16:34 [信息] 程序退出清理完成: 没有需要拉黑的千川手机号码
2025-08-04 23:16:34 [信息] 程序退出，开始清理所有待释放手机号码
2025-08-04 23:16:34 [信息] 程序退出清理完成: 没有需要释放的手机号码
2025-08-04 23:16:34 [信息] 程序退出清理工作完成
2025-08-04 23:16:58 [信息] AWS自动注册工具启动
2025-08-04 23:16:58 [信息] 程序版本: 1.0.0.0
2025-08-04 23:16:58 [信息] 启动时间: 2025-08-04 23:16:58
2025-08-04 23:16:58 [信息] 浏览器模式切换: 无痕 Chrome 模式
2025-08-04 23:16:58 [信息] 线程数量已选择: 1
2025-08-04 23:16:58 [信息] 线程数量选择初始化完成
2025-08-04 23:16:58 [信息] 程序初始化完成
2025-08-04 23:17:00 [按钮操作] 选择文件 -> 打开文件选择对话框
2025-08-04 23:17:02 [信息] 已选择文件: C:\Users\<USER>\Desktop\2025-08-04-智利.txt
2025-08-04 23:17:02 [按钮操作] 加载信息 -> 加载数据文件: C:\Users\<USER>\Desktop\2025-08-04-智利.txt
2025-08-04 23:17:03 [信息] 成功加载 6 条数据
2025-08-04 23:17:04 [信息] 线程数量已选择: 3
2025-08-04 23:17:06 [按钮操作] 开始注册 -> 启动注册流程
2025-08-04 23:17:06 [信息] 开始启动多线程注册，线程数量: 3
2025-08-04 23:17:06 [信息] 开始启动多线程注册，线程数量: 3，数据条数: 6
2025-08-04 23:17:06 [信息] 所有线程已停止并清理
2025-08-04 23:17:06 [信息] 正在初始化多线程服务...
2025-08-04 23:17:06 [信息] 榴莲手机API服务已初始化
2025-08-04 23:17:06 [信息] 手机号码管理器已初始化，服务商: Durian，将在第一个线程完成第二页后获取手机号码
2025-08-04 23:17:06 [信息] 多线程服务初始化完成
2025-08-04 23:17:06 [信息] 数据分配完成：共6条数据分配给3个线程
2025-08-04 23:17:06 [信息] 线程1分配到2条数据
2025-08-04 23:17:06 [信息] 线程2分配到2条数据
2025-08-04 23:17:06 [信息] 线程3分配到2条数据
2025-08-04 23:17:06 [信息] 屏幕工作区域: 1280x672
2025-08-04 23:17:06 [信息] 线程1窗口布局: 位置(0, 0), 大小(384x200), 列1行1, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-04 23:17:06 [信息] 线程1获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-04 23:17:06 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 23:17:06 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_011, WebGL=ANGLE (NVIDIA GeForce RTX 3080 Direct3D11 vs_5_0 ps_5_0), CPU=32核, RAM=20 GB
2025-08-04 23:17:06 线程1：[信息] 已创建，窗口位置: (0, 0)
2025-08-04 23:17:06 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-04 23:17:06 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-04 23:17:06 [信息] 线程1已创建，窗口位置: (0, 0)，指纹: 国家=CL, 时区=America/Santiago
2025-08-04 23:17:06 [信息] 屏幕工作区域: 1280x672
2025-08-04 23:17:06 [信息] 线程2窗口布局: 位置(0, 219), 大小(384x200), 列1行2, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-04 23:17:06 [信息] 线程2获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-04 23:17:06 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 23:17:06 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_011, WebGL=ANGLE (NVIDIA GeForce RTX 3070 Direct3D11 vs_5_0 ps_5_0), CPU=8核, RAM=32 GB
2025-08-04 23:17:06 线程2：[信息] 已创建，窗口位置: (0, 219)
2025-08-04 23:17:06 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-04 23:17:06 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-04 23:17:06 [信息] 线程2已创建，窗口位置: (0, 219)，指纹: 国家=CL, 时区=America/Santiago
2025-08-04 23:17:06 [信息] 屏幕工作区域: 1280x672
2025-08-04 23:17:06 [信息] 线程3窗口布局: 位置(0, 438), 大小(384x200), 列1行3, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-04 23:17:06 [信息] 线程3获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-04 23:17:06 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 23:17:06 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_010, WebGL=ANGLE (Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0), CPU=12核, RAM=48 GB
2025-08-04 23:17:06 线程3：[信息] 已创建，窗口位置: (0, 438)
2025-08-04 23:17:06 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-04 23:17:06 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-04 23:17:06 [信息] 线程3已创建，窗口位置: (0, 438)，指纹: 国家=CL, 时区=America/Santiago
2025-08-04 23:17:06 [信息] 多线程注册启动成功，共3个线程
2025-08-04 23:17:06 线程1：[信息] 开始启动注册流程
2025-08-04 23:17:06 线程2：[信息] 开始启动注册流程
2025-08-04 23:17:06 线程3：[信息] 开始启动注册流程
2025-08-04 23:17:06 线程1：[信息] 开始启动浏览器: 位置(0, 0), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0
2025-08-04 23:17:06 线程2：[信息] 开始启动浏览器: 位置(0, 219), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-08-04 23:17:06 线程2：[信息] 启动无痕Chrome浏览器...
2025-08-04 23:17:06 线程1：[信息] 启动无痕Chrome浏览器...
2025-08-04 23:17:06 线程3：[信息] 开始启动浏览器: 位置(0, 438), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0
2025-08-04 23:17:06 线程3：[信息] 启动无痕Chrome浏览器...
2025-08-04 23:17:06 线程2：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-04 23:17:06 线程1：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-04 23:17:06 线程3：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-04 23:17:06 [信息] 多线程管理窗口已初始化
2025-08-04 23:17:06 [信息] UniformGrid列数已更新为: 1
2025-08-04 23:17:06 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 0, 列数: 1
2025-08-04 23:17:06 [信息] 多线程管理窗口已打开
2025-08-04 23:17:06 [信息] 多线程注册启动成功，共3个线程
2025-08-04 23:17:11 [信息] UniformGrid列数已更新为: 1
2025-08-04 23:17:11 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 1, 列数: 1
2025-08-04 23:17:11 线程2：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-04 23:17:11 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-04 23:17:11 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36
2025-08-04 23:17:11 线程2：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-04 23:17:11 [信息] UniformGrid列数已更新为: 1
2025-08-04 23:17:11 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-08-04 23:17:11 线程3：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-04 23:17:11 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-04 23:17:11 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0
2025-08-04 23:17:11 线程3：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-04 23:17:11 [信息] UniformGrid列数已更新为: 2
2025-08-04 23:17:11 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 3, 列数: 2
2025-08-04 23:17:11 线程1：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-04 23:17:11 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-04 23:17:11 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36
2025-08-04 23:17:11 线程1：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-04 23:17:15 线程3：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-04 23:17:15 线程2：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-04 23:17:15 线程1：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-04 23:17:17 线程3：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-04 23:17:17 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-04 23:17:17 线程3：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-04 23:17:17 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 23:17:17 线程3：[信息] [信息] 多线程模式使用指纹地理位置: -33.4489, -70.6693 (进度: 0%)
2025-08-04 23:17:17 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-33.4489, 经度=-70.6693
2025-08-04 23:17:17 线程3：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_010, CPU: 12核 (进度: 0%)
2025-08-04 23:17:17 [信息] 浏览器指纹注入: Canvas=canvas_fp_010, WebGL=ANGLE (Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0), CPU=12核, RAM=48 GB
2025-08-04 23:17:17 线程2：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-04 23:17:17 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-04 23:17:17 线程2：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-04 23:17:17 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 23:17:17 线程2：[信息] [信息] 多线程模式使用指纹地理位置: -33.4489, -70.6693 (进度: 0%)
2025-08-04 23:17:17 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-33.4489, 经度=-70.6693
2025-08-04 23:17:17 线程2：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_011, CPU: 8核 (进度: 0%)
2025-08-04 23:17:17 [信息] 浏览器指纹注入: Canvas=canvas_fp_011, WebGL=ANGLE (NVIDIA GeForce RTX 3070 Direct3D11 vs_5_0 ps_5_0), CPU=8核, RAM=32 GB
2025-08-04 23:17:17 线程1：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-04 23:17:17 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-04 23:17:17 线程1：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-04 23:17:17 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 23:17:17 线程1：[信息] [信息] 多线程模式使用指纹地理位置: -33.4489, -70.6693 (进度: 0%)
2025-08-04 23:17:17 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-33.4489, 经度=-70.6693
2025-08-04 23:17:17 线程1：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_011, CPU: 32核 (进度: 0%)
2025-08-04 23:17:17 [信息] 浏览器指纹注入: Canvas=canvas_fp_011, WebGL=ANGLE (NVIDIA GeForce RTX 3080 Direct3D11 vs_5_0 ps_5_0), CPU=32核, RAM=20 GB
2025-08-04 23:17:18 线程3：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-04 23:17:18 线程2：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-04 23:17:19 线程1：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-04 23:17:20 线程3：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 12
   • 设备内存: 48 GB
   • 平台信息: Win32
   • Do Not Track: 0

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (NVIDIA Corporation)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 0426959D
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_002
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-A1B2C3D
   • MAC地址: 34-56-78-9A-BC-DE
   • 屏幕分辨率: 2035x1092
   • 可用区域: 2035x1052

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: C9D0E1F2
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: 2g
   • 电池API支持: True
   • 电池电量: 0.70
   • 电池充电状态: True
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-04 23:17:20 线程1：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 32
   • 设备内存: 20 GB
   • 平台信息: Win32
   • Do Not Track: null

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 3080 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (NVIDIA)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 3C4D5E6F
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_011
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-A1B2C3D
   • MAC地址: 12-34-56-78-9A-BC
   • 屏幕分辨率: 1917x1104
   • 可用区域: 1917x1064

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: E7F8A9B0
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: ethernet
   • 电池API支持: True
   • 电池电量: 0.23
   • 电池充电状态: True
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-04 23:17:20 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 12    • 设备内存: 48 GB    • 平台信息: Win32    • Do Not Track: 0   图形渲染信息:    • WebGL渲染器: ANGLE (Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (NVIDIA Corporation)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 0426959D    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_002    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-A1B2C3D    • MAC地址: 34-56-78-9A-BC-DE    • 屏幕分辨率: 2035x1092    • 可用区域: 2035x1052   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: C9D0E1F2    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: 2g    • 电池API支持: True    • 电池电量: 0.70    • 电池充电状态: True    • 性能API支持: True  ================================
2025-08-04 23:17:20 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 32    • 设备内存: 20 GB    • 平台信息: Win32    • Do Not Track: null   图形渲染信息:    • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 3080 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (NVIDIA)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 3C4D5E6F    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_011    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-A1B2C3D    • MAC地址: 12-34-56-78-9A-BC    • 屏幕分辨率: 1917x1104    • 可用区域: 1917x1064   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: E7F8A9B0    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: ethernet    • 电池API支持: True    • 电池电量: 0.23    • 电池充电状态: True    • 性能API支持: True  ================================
2025-08-04 23:17:20 线程2：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 8
   • 设备内存: 32 GB
   • 平台信息: Win32
   • Do Not Track: unspecified

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 3070 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (NVIDIA)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 1A2B3C4D
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_010
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-A1B2C3D
   • MAC地址: 56-78-9A-BC-DE-F0
   • 屏幕分辨率: 2087x1151
   • 可用区域: 2087x1111

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: C3D4E5F6
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: 2g
   • 电池API支持: True
   • 电池电量: 0.65
   • 电池充电状态: False
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-04 23:17:20 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 8    • 设备内存: 32 GB    • 平台信息: Win32    • Do Not Track: unspecified   图形渲染信息:    • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 3070 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (NVIDIA)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 1A2B3C4D    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_010    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-A1B2C3D    • MAC地址: 56-78-9A-BC-DE-F0    • 屏幕分辨率: 2087x1151    • 可用区域: 2087x1111   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: C3D4E5F6    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: 2g    • 电池API支持: True    • 电池电量: 0.65    • 电池充电状态: False    • 性能API支持: True  ================================
2025-08-04 23:17:20 线程3：[信息] [信息] 已设置浏览器标题: 线程3 - AWS注册工具 (进度: 5%)
2025-08-04 23:17:20 线程3：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-04 23:17:20 线程3：[信息] 浏览器启动成功
2025-08-04 23:17:20 线程3：[信息] 获取下一个数据: <EMAIL>
2025-08-04 23:17:21 线程3：[信息] 开始处理账户: <EMAIL>
2025-08-04 23:17:21 线程3：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-04 23:17:21 线程3：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-04 23:17:21 线程3：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-04 23:17:21 线程3：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-04 23:17:21 线程3：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-04 23:17:21 线程2：[信息] [信息] 已设置浏览器标题: 线程2 - AWS注册工具 (进度: 5%)
2025-08-04 23:17:21 线程2：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-04 23:17:21 线程2：[信息] 浏览器启动成功
2025-08-04 23:17:21 线程2：[信息] 获取下一个数据: <EMAIL>
2025-08-04 23:17:21 线程2：[信息] 开始处理账户: <EMAIL>
2025-08-04 23:17:21 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-04 23:17:21 线程2：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-04 23:17:21 线程2：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-04 23:17:21 线程2：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-04 23:17:21 线程2：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-04 23:17:21 线程1：[信息] [信息] 已设置浏览器标题: 线程1 - AWS注册工具 (进度: 5%)
2025-08-04 23:17:21 线程1：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-04 23:17:21 线程1：[信息] 浏览器启动成功
2025-08-04 23:17:21 线程1：[信息] 获取下一个数据: <EMAIL>
2025-08-04 23:17:21 线程1：[信息] 开始处理账户: <EMAIL>
2025-08-04 23:17:21 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-04 23:17:21 线程1：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-04 23:17:21 线程1：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-04 23:17:21 线程1：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-04 23:17:21 线程1：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-04 23:17:23 线程2：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-04 23:17:23 线程2：[信息] [信息] 已重新设置浏览器标题: 线程2 - AWS注册工具 (进度: 98%)
2025-08-04 23:17:23 线程2：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-04 23:17:23 线程3：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-04 23:17:23 线程2：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-04 23:17:23 线程2：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-04 23:17:23 线程2：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-04 23:17:23 线程3：[信息] [信息] 已重新设置浏览器标题: 线程3 - AWS注册工具 (进度: 98%)
2025-08-04 23:17:23 线程3：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-04 23:17:23 线程3：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-04 23:17:23 线程3：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-04 23:17:23 线程3：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-04 23:17:23 线程1：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-04 23:17:23 线程1：[信息] [信息] 已重新设置浏览器标题: 线程1 - AWS注册工具 (进度: 98%)
2025-08-04 23:17:23 线程1：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-04 23:17:23 线程1：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-04 23:17:23 线程1：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-04 23:17:23 线程1：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-04 23:17:51 线程1：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程1 - AWS注册 (进度: 98%)
2025-08-04 23:17:51 线程1：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-04 23:17:51 线程1：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-04 23:17:51 线程3：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程3 - AWS注册 (进度: 98%)
2025-08-04 23:17:51 线程3：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-04 23:17:51 线程3：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-04 23:17:51 线程2：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程2 - AWS注册 (进度: 98%)
2025-08-04 23:17:51 线程2：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-04 23:17:51 线程2：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-04 23:17:51 线程1：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-04 23:17:51 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-04 23:17:51 线程3：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-04 23:17:51 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-04 23:17:52 线程2：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-04 23:17:52 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-04 23:17:52 线程1：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-04 23:17:52 线程3：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-04 23:17:52 线程2：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-04 23:17:55 线程1：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-04 23:17:55 线程1：[信息] [信息] ⚠️ 检测到错误信息，开始重试机制... (进度: 98%)
2025-08-04 23:17:55 [信息] 检测到错误信息，开始重试机制
2025-08-04 23:17:55 线程1：[信息] [信息] 🔄 第1次重试点击验证邮箱按钮... (进度: 98%)
2025-08-04 23:17:55 [信息] 第1次重试点击验证邮箱按钮
2025-08-04 23:17:55 线程3：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-04 23:17:55 线程3：[信息] [信息] ⚠️ 检测到错误信息，开始重试机制... (进度: 98%)
2025-08-04 23:17:55 [信息] 检测到错误信息，开始重试机制
2025-08-04 23:17:55 线程3：[信息] [信息] 🔄 第1次重试点击验证邮箱按钮... (进度: 98%)
2025-08-04 23:17:55 [信息] 第1次重试点击验证邮箱按钮
2025-08-04 23:17:55 线程2：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-04 23:17:55 线程2：[信息] [信息] ⚠️ 检测到错误信息，开始重试机制... (进度: 98%)
2025-08-04 23:17:55 [信息] 检测到错误信息，开始重试机制
2025-08-04 23:17:55 线程2：[信息] [信息] 🔄 第1次重试点击验证邮箱按钮... (进度: 98%)
2025-08-04 23:17:55 [信息] 第1次重试点击验证邮箱按钮
2025-08-04 23:17:57 线程1：[信息] [信息] ✅ 第1次重试：已点击验证邮箱按钮 (进度: 100%)
2025-08-04 23:17:57 [信息] 第1次重试：已点击验证邮箱按钮
2025-08-04 23:17:57 线程3：[信息] [信息] ✅ 第1次重试：已点击验证邮箱按钮 (进度: 100%)
2025-08-04 23:17:57 [信息] 第1次重试：已点击验证邮箱按钮
2025-08-04 23:17:57 线程2：[信息] [信息] ✅ 第1次重试：已点击验证邮箱按钮 (进度: 100%)
2025-08-04 23:17:57 [信息] 第1次重试：已点击验证邮箱按钮
2025-08-04 23:17:59 线程1：[信息] [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']" (进度: 100%)
2025-08-04 23:17:59 [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']"
2025-08-04 23:17:59 线程1：[信息] [信息] ✅ 第1次重试成功：已到达第二页，继续正常流程 (进度: 100%)
2025-08-04 23:17:59 [信息] 第1次重试成功：已到达第二页
2025-08-04 23:17:59 线程1：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-04 23:17:59 线程1：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-04 23:17:59 线程1：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 23:17:59 线程1：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-04 23:17:59 线程3：[信息] [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']" (进度: 100%)
2025-08-04 23:17:59 [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']"
2025-08-04 23:17:59 线程3：[信息] [信息] ✅ 第1次重试成功：已到达第二页，继续正常流程 (进度: 100%)
2025-08-04 23:17:59 [信息] 第1次重试成功：已到达第二页
2025-08-04 23:17:59 线程3：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-04 23:17:59 线程3：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-04 23:17:59 线程3：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 23:17:59 线程3：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-04 23:17:59 线程2：[信息] [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']" (进度: 100%)
2025-08-04 23:17:59 [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']"
2025-08-04 23:17:59 线程2：[信息] [信息] ✅ 第1次重试成功：已到达第二页，继续正常流程 (进度: 100%)
2025-08-04 23:17:59 [信息] 第1次重试成功：已到达第二页
2025-08-04 23:17:59 线程2：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-04 23:17:59 线程2：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-04 23:17:59 线程2：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 23:17:59 线程2：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-04 23:18:01 线程1：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-04 23:18:01 线程1：[信息] [信息] 🔍 第2次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 23:18:01 线程1：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 未发现验证码 (进度: 100%)
2025-08-04 23:18:01 线程1：[信息] [信息] ✅ 第一页未检测到图形验证码，继续流程 (进度: 100%)
2025-08-04 23:18:01 线程1：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-04 23:18:01 线程1：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-04 23:18:01 线程1：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-04 23:18:01 线程1：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-04 23:18:01 线程1：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-04 23:18:01 线程1：[信息] 账户注册流程已启动: <EMAIL>
2025-08-04 23:18:01 线程1：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-04 23:18:01 [信息] [线程1] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-04 23:18:01 [信息] [线程1] 等待2秒后开始第一次触发...
2025-08-04 23:18:01 线程3：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-04 23:18:01 线程3：[信息] [信息] 🔍 第2次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 23:18:01 线程3：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 未发现验证码 (进度: 100%)
2025-08-04 23:18:01 线程3：[信息] [信息] ✅ 第一页未检测到图形验证码，继续流程 (进度: 100%)
2025-08-04 23:18:01 线程3：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-04 23:18:01 线程3：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-04 23:18:01 线程3：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-04 23:18:01 线程3：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-04 23:18:01 [信息] [线程3] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-04 23:18:01 线程3：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-04 23:18:01 线程3：[信息] 账户注册流程已启动: <EMAIL>
2025-08-04 23:18:01 线程3：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-04 23:18:01 [信息] [线程3] 已删除旧的响应文件
2025-08-04 23:18:01 [信息] [线程3] 等待2秒后开始第一次触发...
2025-08-04 23:18:01 线程2：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-04 23:18:01 线程2：[信息] [信息] 🔍 第2次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 23:18:01 线程2：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 未发现验证码 (进度: 100%)
2025-08-04 23:18:01 线程2：[信息] [信息] ✅ 第一页未检测到图形验证码，继续流程 (进度: 100%)
2025-08-04 23:18:01 线程2：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-04 23:18:01 线程2：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-04 23:18:01 线程2：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-04 23:18:02 线程2：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-04 23:18:02 线程2：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-04 23:18:02 线程2：[信息] 账户注册流程已启动: <EMAIL>
2025-08-04 23:18:02 线程2：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-04 23:18:02 [信息] [线程2] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-04 23:18:02 [信息] [线程2] 等待2秒后开始第一次触发...
2025-08-04 23:18:03 [信息] [线程1] 第1次触发邮箱验证码获取...（最多20次）
2025-08-04 23:18:03 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 23:18:03
2025-08-04 23:18:03 [信息] [线程3] 第1次触发邮箱验证码获取...（最多20次）
2025-08-04 23:18:03 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-04 23:18:03
2025-08-04 23:18:04 [信息] [线程2] 第1次触发邮箱验证码获取...（最多20次）
2025-08-04 23:18:04 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-04 23:18:04
2025-08-04 23:18:06 [信息] [线程1] 第2次触发邮箱验证码获取...（最多20次）
2025-08-04 23:18:06 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 23:18:06
2025-08-04 23:18:06 [信息] [线程3] 第2次触发邮箱验证码获取...（最多20次）
2025-08-04 23:18:06 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-04 23:18:06
2025-08-04 23:18:07 [信息] [线程2] 第2次触发邮箱验证码获取...（最多20次）
2025-08-04 23:18:07 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-04 23:18:07
2025-08-04 23:18:09 [信息] [线程1] 第3次触发邮箱验证码获取...（最多20次）
2025-08-04 23:18:09 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 23:18:09
2025-08-04 23:18:09 [信息] [线程3] 第3次触发邮箱验证码获取...（最多20次）
2025-08-04 23:18:09 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-04 23:18:09
2025-08-04 23:18:09 [信息] [线程3] 邮箱验证码获取成功: 641843，立即停止重复请求
2025-08-04 23:18:09 [信息] [线程3] 已清理请求文件，停止重复触发
2025-08-04 23:18:09 [信息] [线程3] 已清理响应文件
2025-08-04 23:18:09 线程3：[信息] [信息] 验证码获取成功: 641843，正在自动填入... (进度: 25%)
2025-08-04 23:18:09 线程3：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 25%)
2025-08-04 23:18:09 线程3：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 25%)
2025-08-04 23:18:09 线程3：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 25%)
2025-08-04 23:18:09 [信息] 线程3完成第二页事件已处理
2025-08-04 23:18:09 [信息] 线程3完成第二页，开始批量获取手机号码...
2025-08-04 23:18:09 线程3：[信息] [信息] 线程3第二页验证完成，已通知管理器 (进度: 35%)
2025-08-04 23:18:09 [信息] 开始批量获取3个手机号码，服务商: Durian
2025-08-04 23:18:09 [信息] [手机API] 批量获取3个手机号码，URL: https://api.durianrcs.com/out/ext_api/getMobile?name=oneone&ApiKey=NnlaR01xQm9hMlAwRnJDSVB2SG1kQT09&cuy=mx&pid=0209&num=3&noblack=0&serial=2&secret_key=null&vip=null
2025-08-04 23:18:10 [信息] [线程2] 第3次触发邮箱验证码获取...（最多20次）
2025-08-04 23:18:10 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-04 23:18:10
2025-08-04 23:18:10 [信息] [线程1] 邮箱验证码获取成功: 467764，立即停止重复请求
2025-08-04 23:18:10 [信息] [线程1] 已清理请求文件，停止重复触发
2025-08-04 23:18:10 [信息] [线程1] 已清理响应文件
2025-08-04 23:18:10 线程1：[信息] [信息] 验证码获取成功: 467764，正在自动填入... (进度: 25%)
2025-08-04 23:18:10 线程1：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 25%)
2025-08-04 23:18:10 线程1：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 25%)
2025-08-04 23:18:10 线程1：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 25%)
2025-08-04 23:18:10 [信息] 线程1完成第二页事件已处理
2025-08-04 23:18:10 [信息] 线程1完成第二页，手机号码已获取，无需重复获取
2025-08-04 23:18:10 线程1：[信息] [信息] 线程1第二页验证完成，已通知管理器 (进度: 35%)
2025-08-04 23:18:10 [信息] [线程2] 邮箱验证码获取成功: 767033，立即停止重复请求
2025-08-04 23:18:10 [信息] [线程2] 已清理请求文件，停止重复触发
2025-08-04 23:18:10 [信息] [线程2] 已清理响应文件
2025-08-04 23:18:10 线程2：[信息] [信息] 验证码获取成功: 767033，正在自动填入... (进度: 25%)
2025-08-04 23:18:10 线程2：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 25%)
2025-08-04 23:18:10 线程2：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 25%)
2025-08-04 23:18:10 线程2：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 25%)
2025-08-04 23:18:10 [信息] 线程2完成第二页事件已处理
2025-08-04 23:18:10 [信息] 线程2完成第二页，手机号码已获取，无需重复获取
2025-08-04 23:18:10 线程2：[信息] [信息] 线程2第二页验证完成，已通知管理器 (进度: 35%)
2025-08-04 23:18:11 [信息] [手机API] 批量获取响应内容: {"code":200,"msg":"Success","data":["+529623502411","+526642542598","+527531026919"]}
2025-08-04 23:18:11 [信息] [手机API] 检测到数组格式，元素数量: 3
2025-08-04 23:18:11 [信息] [手机API] 批量获取成功，获得3个手机号码
2025-08-04 23:18:11 [信息] 线程1分配榴莲手机号码: +529623502411
2025-08-04 23:18:11 [信息] 线程2分配榴莲手机号码: +526642542598
2025-08-04 23:18:11 [信息] 线程3分配榴莲手机号码: +527531026919
2025-08-04 23:18:11 [信息] 榴莲API批量获取手机号码成功，已分配给3个线程
2025-08-04 23:18:11 [信息] 批量获取3个手机号码成功
2025-08-04 23:18:12 线程3：[信息] [信息] 等待密码设置页面加载... (进度: 38%)
2025-08-04 23:18:13 线程3：[信息] [信息] 开始填写密码信息... (进度: 38%)
2025-08-04 23:18:13 线程3：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-04 23:18:13 线程3：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-04 23:18:13 线程3：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 38%)
2025-08-04 23:18:13 线程1：[信息] [信息] 等待密码设置页面加载... (进度: 38%)
2025-08-04 23:18:13 线程1：[信息] [信息] 开始填写密码信息... (进度: 38%)
2025-08-04 23:18:13 线程1：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-04 23:18:13 线程1：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-04 23:18:13 线程1：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 38%)
2025-08-04 23:18:13 线程2：[信息] [信息] 等待密码设置页面加载... (进度: 38%)
2025-08-04 23:18:13 线程2：[信息] [信息] 开始填写密码信息... (进度: 38%)
2025-08-04 23:18:14 线程3：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 38%)
2025-08-04 23:18:14 线程2：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-04 23:18:14 线程1：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 38%)
2025-08-04 23:18:15 线程2：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-04 23:18:15 线程2：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 38%)
2025-08-04 23:18:16 线程2：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 38%)
2025-08-04 23:18:17 线程3：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 38%)
2025-08-04 23:18:17 线程3：[信息] [信息] 等待账户类型确认页面加载... (进度: 38%)
2025-08-04 23:18:17 线程3：[信息] [信息] 开始处理账户类型确认... (进度: 38%)
2025-08-04 23:18:17 线程1：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 38%)
2025-08-04 23:18:17 线程1：[信息] [信息] 等待账户类型确认页面加载... (进度: 38%)
2025-08-04 23:18:17 线程1：[信息] [信息] 开始处理账户类型确认... (进度: 38%)
2025-08-04 23:18:19 线程2：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 38%)
2025-08-04 23:18:19 线程2：[信息] [信息] 等待账户类型确认页面加载... (进度: 38%)
2025-08-04 23:18:19 线程2：[信息] [信息] 开始处理账户类型确认... (进度: 38%)
2025-08-04 23:18:23 线程1：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 38%)
2025-08-04 23:18:23 线程1：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 38%)
2025-08-04 23:18:27 线程2：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 38%)
2025-08-04 23:18:27 线程2：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 38%)
2025-08-04 23:18:29 线程3：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 38%)
2025-08-04 23:18:29 线程3：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 38%)
2025-08-04 23:18:46 线程1：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 38%)
2025-08-04 23:18:46 [信息] 线程1获取已分配的榴莲手机号码: +529623502411
2025-08-04 23:18:46 线程1：[信息] [信息] 多线程模式：使用已分配的手机号码 +529623502411 (进度: 38%)
2025-08-04 23:18:47 线程3：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 38%)
2025-08-04 23:18:47 [信息] 线程3获取已分配的榴莲手机号码: +527531026919
2025-08-04 23:18:47 线程3：[信息] [信息] 多线程模式：使用已分配的手机号码 +527531026919 (进度: 38%)
2025-08-04 23:18:47 线程2：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 38%)
2025-08-04 23:18:47 [信息] 线程2获取已分配的榴莲手机号码: +526642542598
2025-08-04 23:18:47 线程2：[信息] [信息] 多线程模式：使用已分配的手机号码 +526642542598 (进度: 38%)
2025-08-04 23:18:48 线程1：[信息] [信息] 数据国家代码为CL，需要选择Chile (进度: 38%)
2025-08-04 23:18:48 线程3：[信息] [信息] 数据国家代码为CL，需要选择Chile (进度: 38%)
2025-08-04 23:18:48 线程2：[信息] [信息] 数据国家代码为CL，需要选择Chile (进度: 38%)
2025-08-04 23:18:48 线程1：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 38%)
2025-08-04 23:18:49 线程2：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 38%)
2025-08-04 23:18:49 线程3：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 38%)
2025-08-04 23:18:50 线程1：[信息] [信息] 已选择国家: Chile (进度: 38%)
2025-08-04 23:18:50 线程1：[信息] [信息] 已成功选择国家: Chile (进度: 38%)
2025-08-04 23:18:50 线程1：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 38%)
2025-08-04 23:18:50 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-08-04 23:18:50 线程3：[信息] [信息] 已选择国家: Chile (进度: 38%)
2025-08-04 23:18:50 线程2：[信息] [信息] 已选择国家: Chile (进度: 38%)
2025-08-04 23:18:50 线程3：[信息] [信息] 已成功选择国家: Chile (进度: 38%)
2025-08-04 23:18:50 线程2：[信息] [信息] 已成功选择国家: Chile (进度: 38%)
2025-08-04 23:18:50 线程3：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 38%)
2025-08-04 23:18:50 线程2：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 38%)
2025-08-04 23:18:51 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-08-04 23:18:51 线程3：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-08-04 23:18:54 线程1：[信息] [信息] 已选择国家代码 +52 (进度: 38%)
2025-08-04 23:18:55 线程1：[信息] [信息] 等待后台获取的手机号码结果... (进度: 38%)
2025-08-04 23:18:55 线程1：[信息] [信息] 已自动获取并填入手机号码: +529623502411 (进度: 38%)
2025-08-04 23:18:56 线程1：[信息] [信息] 使用已获取的手机号码: +529623502411（保存本地号码: +529623502411） (进度: 38%)
2025-08-04 23:18:56 线程1：[信息] [信息] 联系信息完成，等待页面加载... (进度: 38%)
2025-08-04 23:18:56 线程2：[信息] [信息] 已选择国家代码 +52 (进度: 38%)
2025-08-04 23:18:56 线程3：[信息] [信息] 已选择国家代码 +52 (进度: 38%)
2025-08-04 23:18:57 线程2：[信息] [信息] 等待后台获取的手机号码结果... (进度: 38%)
2025-08-04 23:18:57 线程3：[信息] [信息] 等待后台获取的手机号码结果... (进度: 38%)
2025-08-04 23:18:57 线程2：[信息] [信息] 已自动获取并填入手机号码: +526642542598 (进度: 38%)
2025-08-04 23:18:57 线程3：[信息] [信息] 已自动获取并填入手机号码: +527531026919 (进度: 38%)
2025-08-04 23:18:58 线程2：[信息] [信息] 使用已获取的手机号码: +526642542598（保存本地号码: +526642542598） (进度: 38%)
2025-08-04 23:18:58 线程3：[信息] [信息] 使用已获取的手机号码: +527531026919（保存本地号码: +527531026919） (进度: 38%)
2025-08-04 23:18:58 线程2：[信息] [信息] 联系信息完成，等待页面加载... (进度: 38%)
2025-08-04 23:18:58 线程3：[信息] [信息] 联系信息完成，等待页面加载... (进度: 38%)
2025-08-04 23:18:59 线程1：[信息] [信息] 进入付款信息页面... (进度: 38%)
2025-08-04 23:19:00 线程1：[信息] [信息] 正在选择月份: August (进度: 38%)
2025-08-04 23:19:00 线程1：[信息] [信息] 已选择月份（标准选项）: August (进度: 38%)
2025-08-04 23:19:01 线程1：[信息] [信息] 正在选择年份: 2028 (进度: 38%)
2025-08-04 23:19:01 线程2：[信息] [信息] 进入付款信息页面... (进度: 38%)
2025-08-04 23:19:01 线程3：[信息] [信息] 进入付款信息页面... (进度: 38%)
2025-08-04 23:19:01 线程1：[信息] [信息] 已选择年份（标准选项）: 2028 (进度: 38%)
2025-08-04 23:19:02 线程1：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 38%)
2025-08-04 23:19:02 线程1：[信息] [信息] 开始填写验证码验证页面... (进度: 38%)
2025-08-04 23:19:02 线程1：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 38%)
2025-08-04 23:19:02 线程2：[信息] [信息] 正在选择月份: April (进度: 38%)
2025-08-04 23:19:02 线程3：[信息] [信息] 正在选择月份: September (进度: 38%)
2025-08-04 23:19:02 线程2：[信息] [信息] 已选择月份（标准选项）: April (进度: 38%)
2025-08-04 23:19:03 线程3：[信息] [信息] 已选择月份（标准选项）: September (进度: 38%)
2025-08-04 23:19:03 线程3：[信息] [信息] 正在选择年份: 2031 (进度: 38%)
2025-08-04 23:19:03 线程2：[信息] [信息] 正在选择年份: 2029 (进度: 38%)
2025-08-04 23:19:04 线程3：[信息] [信息] 已选择年份（标准选项）: 2031 (进度: 38%)
2025-08-04 23:19:04 线程2：[信息] [信息] 已选择年份（标准选项）: 2029 (进度: 38%)
2025-08-04 23:19:05 线程2：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 38%)
2025-08-04 23:19:05 线程2：[信息] [信息] 开始填写验证码验证页面... (进度: 38%)
2025-08-04 23:19:05 线程2：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 38%)
2025-08-04 23:19:05 线程3：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 38%)
2025-08-04 23:19:05 线程3：[信息] [信息] 开始填写验证码验证页面... (进度: 38%)
2025-08-04 23:19:05 线程3：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 38%)
2025-08-04 23:19:07 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-08-04 23:19:08 线程1：[信息] [信息] 已选择国家代码: +52 (进度: 38%)
2025-08-04 23:19:09 线程1：[信息] [信息] 已清空并重新填写手机号码: +529623502411 (进度: 38%)
2025-08-04 23:19:09 线程1：[信息] [信息] 已点击发送验证码按钮 (进度: 38%)
2025-08-04 23:19:10 线程3：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-08-04 23:19:11 线程1：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 38%)
2025-08-04 23:19:11 线程1：[信息] [信息] ⚠️ 检测到错误信息，开始重试机制... (进度: 38%)
2025-08-04 23:19:11 [信息] 检测到错误信息，开始重试机制
2025-08-04 23:19:11 线程1：[信息] [信息] 🔄 第1次重试发送验证码按钮... (进度: 38%)
2025-08-04 23:19:11 [信息] 第1次重试发送验证码按钮
2025-08-04 23:19:11 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-08-04 23:19:11 线程3：[信息] [信息] 已选择国家代码: +52 (进度: 38%)
2025-08-04 23:19:11 线程3：[信息] [信息] 已清空并重新填写手机号码: +527531026919 (进度: 38%)
2025-08-04 23:19:12 线程3：[信息] [信息] 已点击发送验证码按钮 (进度: 38%)
2025-08-04 23:19:12 线程2：[信息] [信息] 已选择国家代码: +52 (进度: 38%)
2025-08-04 23:19:12 线程2：[信息] [信息] 已清空并重新填写手机号码: +526642542598 (进度: 38%)
2025-08-04 23:19:12 线程2：[信息] [信息] 已点击发送验证码按钮 (进度: 38%)
2025-08-04 23:19:14 线程3：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 38%)
2025-08-04 23:19:14 线程3：[信息] [信息] ⚠️ 检测到错误信息，开始重试机制... (进度: 38%)
2025-08-04 23:19:14 [信息] 检测到错误信息，开始重试机制
2025-08-04 23:19:14 线程3：[信息] [信息] 🔄 第1次重试发送验证码按钮... (进度: 38%)
2025-08-04 23:19:14 [信息] 第1次重试发送验证码按钮
2025-08-04 23:19:14 线程1：[信息] [信息] ✅ 第1次重试：已点击发送验证码按钮 (进度: 100%)
2025-08-04 23:19:14 [信息] 第1次重试：已点击发送验证码按钮
2025-08-04 23:19:14 线程2：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 38%)
2025-08-04 23:19:17 线程2：[信息] [信息] ⚠️ 检测到错误信息，开始重试机制... (进度: 38%)
2025-08-04 23:19:17 [信息] 检测到错误信息，开始重试机制
2025-08-04 23:19:17 线程2：[信息] [信息] 🔄 第1次重试发送验证码按钮... (进度: 38%)
2025-08-04 23:19:17 [信息] 第1次重试发送验证码按钮
2025-08-04 23:19:17 线程1：[信息] [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']" (进度: 100%)
2025-08-04 23:19:17 [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']"
2025-08-04 23:19:17 线程1：[信息] [信息] ✅ 第1次重试成功：错误信息消失，继续正常流程 (进度: 100%)
2025-08-04 23:19:17 [信息] 第1次重试成功：错误信息消失
2025-08-04 23:19:17 线程1：[信息] [信息] 手机号码自动模式 + 图形验证码自动模式：开始自动处理... (进度: 100%)
2025-08-04 23:19:17 线程1：[信息] [信息] 自动模式：开始处理图形验证码... (进度: 100%)
2025-08-04 23:19:17 线程1：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-04 23:19:17 线程3：[信息] [信息] ✅ 第1次重试：已点击发送验证码按钮 (进度: 100%)
2025-08-04 23:19:17 [信息] 第1次重试：已点击发送验证码按钮
2025-08-04 23:19:19 线程3：[信息] [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']" (进度: 100%)
2025-08-04 23:19:19 [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']"
2025-08-04 23:19:19 线程3：[信息] [信息] ✅ 第1次重试成功：错误信息消失，继续正常流程 (进度: 100%)
2025-08-04 23:19:19 [信息] 第1次重试成功：错误信息消失
2025-08-04 23:19:19 线程3：[信息] [信息] 手机号码自动模式 + 图形验证码自动模式：开始自动处理... (进度: 100%)
2025-08-04 23:19:19 线程3：[信息] [信息] 自动模式：开始处理图形验证码... (进度: 100%)
2025-08-04 23:19:19 线程3：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-04 23:19:20 线程1：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-04 23:19:20 线程1：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 23:19:20 线程2：[信息] [信息] ✅ 第1次重试：已点击发送验证码按钮 (进度: 100%)
2025-08-04 23:19:20 [信息] 第1次重试：已点击发送验证码按钮
2025-08-04 23:19:22 线程3：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-04 23:19:22 线程3：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 23:19:23 线程2：[信息] [信息] ✅ 第1次重试成功：错误信息消失，继续正常流程 (进度: 100%)
2025-08-04 23:19:23 [信息] 第1次重试成功：错误信息消失
2025-08-04 23:19:23 线程2：[信息] [信息] 手机号码自动模式 + 图形验证码自动模式：开始自动处理... (进度: 100%)
2025-08-04 23:19:23 线程2：[信息] [信息] 自动模式：开始处理图形验证码... (进度: 100%)
2025-08-04 23:19:23 线程2：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-04 23:19:24 线程1：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 35099 字节 (进度: 100%)
2025-08-04 23:19:24 线程1：[信息] [信息] ✅ 图片验证通过：201x70px，35099字节，复杂度符合要求 (进度: 100%)
2025-08-04 23:19:24 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 23:19:25 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"3bh6c8"},"taskId":"67b01cb8-7146-11f0-b896-029e46bc98e3"} (进度: 100%)
2025-08-04 23:19:25 线程1：[信息] [信息] 第六页第1次识别结果: 3bh6c8 → 转换为小写: 3bh6c8 (进度: 100%)
2025-08-04 23:19:25 线程1：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 23:19:25 线程1：[信息] [信息] 第六页已填入验证码: 3bh6c8 (进度: 100%)
2025-08-04 23:19:25 线程1：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 23:19:26 线程2：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-04 23:19:26 线程2：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 23:19:28 线程1：[信息] [信息] 第1次图形验证码识别成功 (进度: 100%)
2025-08-04 23:19:28 线程1：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-08-04 23:19:29 线程2：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 35186 字节 (进度: 100%)
2025-08-04 23:19:29 线程2：[信息] [信息] ✅ 图片验证通过：201x71px，35186字节，复杂度符合要求 (进度: 100%)
2025-08-04 23:19:29 线程2：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 23:19:30 线程2：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"mzrmz4"},"taskId":"6ad3c1ec-7146-11f0-b896-029e46bc98e3"} (进度: 100%)
2025-08-04 23:19:30 线程2：[信息] [信息] 第六页第1次识别结果: mzrmz4 → 转换为小写: mzrmz4 (进度: 100%)
2025-08-04 23:19:30 线程2：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 23:19:30 线程2：[信息] [信息] 第六页已填入验证码: mzrmz4 (进度: 100%)
2025-08-04 23:19:30 线程2：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 23:19:31 线程1：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-08-04 23:19:33 线程2：[信息] [信息] 第1次图形验证码识别成功 (进度: 100%)
2025-08-04 23:19:33 线程2：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-08-04 23:19:34 线程1：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-04 23:19:34 线程1：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 23:19:34 线程1：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 23:19:34 线程1：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-08-04 23:19:35 线程3：[信息] [信息] 第六页验证码元素等待超时，转为手动模式 (进度: 100%)
2025-08-04 23:19:35 线程3：[信息] [信息] 第六页第1次识别异常: 验证码元素等待超时，需要手动处理 (进度: 100%)
2025-08-04 23:19:35 线程3：[信息] [信息] 第六页验证码元素等待超时，直接转为手动模式 (进度: 100%)
2025-08-04 23:19:35 线程3：[信息] [信息] 当前验证码抓取出错，需要手动完成图形验证码，并点击继续注册 (进度: 100%)
2025-08-04 23:19:37 线程2：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-08-04 23:19:39 线程1：[信息] [信息] 线程1开始独立获取验证码... (进度: 100%)
2025-08-04 23:19:39 线程1：[信息] [信息] 线程1开始获取验证码，最多尝试8次，每次间隔8秒 (进度: 100%)
2025-08-04 23:19:39 线程1：[信息] [信息] 线程1第1次尝试获取验证码...（剩余7次尝试） (进度: 100%)
2025-08-04 23:19:39 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-04 23:19:40 线程2：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-04 23:19:40 线程2：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 23:19:40 线程2：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 23:19:40 线程2：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-08-04 23:19:40 线程1：[信息] [信息] 线程1验证码获取成功: 4782 (进度: 100%)
2025-08-04 23:19:40 [信息] 线程1手机号码已加入释放队列: +529623502411 (原因: 获取验证码成功)
2025-08-04 23:19:40 线程1：[信息] [信息] 线程1验证码获取成功: 4782，立即填入验证码... (进度: 100%)
2025-08-04 23:19:40 线程1：[信息] [信息] 第七页：找到验证码输入框 (Verify code) (进度: 100%)
2025-08-04 23:19:40 线程1：[信息] [信息] 线程1已自动填入手机验证码: 4782 (进度: 100%)
2025-08-04 23:19:41 线程1：[信息] [信息] 线程1正在自动点击Continue按钮... (进度: 100%)
2025-08-04 23:19:41 线程1：[信息] [信息] 线程1手机验证码验证完成，继续执行后续步骤... (进度: 100%)
2025-08-04 23:19:47 线程2：[信息] [信息] 线程2开始独立获取验证码... (进度: 100%)
2025-08-04 23:19:47 线程2：[信息] [信息] 线程2开始获取验证码，最多尝试8次，每次间隔8秒 (进度: 100%)
2025-08-04 23:19:47 线程2：[信息] [信息] 线程2第1次尝试获取验证码...（剩余7次尝试） (进度: 100%)
2025-08-04 23:19:47 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-04 23:19:47 线程1：[信息] [信息] 验证码验证完成，等待页面加载... (进度: 100%)
2025-08-04 23:19:48 线程2：[信息] [信息] 线程2验证码获取成功: 3018 (进度: 100%)
2025-08-04 23:19:48 [信息] 线程2手机号码已加入释放队列: +526642542598 (原因: 获取验证码成功)
2025-08-04 23:19:48 线程2：[信息] [信息] 线程2验证码获取成功: 3018，立即填入验证码... (进度: 100%)
2025-08-04 23:19:48 线程2：[信息] [信息] 第七页：找到验证码输入框 (Verify code) (进度: 100%)
2025-08-04 23:19:48 线程2：[信息] [信息] 线程2已自动填入手机验证码: 3018 (进度: 100%)
2025-08-04 23:19:48 线程1：[信息] [信息] 自动模式验证完成，API手机号码已加入黑名单: 加入黑名单成功 (进度: 100%)
2025-08-04 23:19:49 线程2：[信息] [信息] 线程2正在自动点击Continue按钮... (进度: 100%)
2025-08-04 23:19:49 线程2：[信息] [信息] 线程2手机验证码验证完成，继续执行后续步骤... (进度: 100%)
2025-08-04 23:19:52 线程2：[信息] [信息] 验证码验证完成，等待页面加载... (进度: 100%)
2025-08-04 23:19:52 线程2：[信息] [信息] 已点击完成注册按钮，正在处理... (进度: 100%)
2025-08-04 23:19:53 线程2：[信息] [信息] 自动模式验证完成，API手机号码已加入黑名单: 加入黑名单成功 (进度: 100%)
2025-08-04 23:19:53 线程2：[信息] [信息] 等待完成注册页面加载... (进度: 100%)
2025-08-04 23:19:56 线程2：[信息] [信息] 完成注册页面加载完成 (进度: 100%)
2025-08-04 23:19:56 线程2：[信息] [信息] 🔍 正在查找'Go to the AWS Management Console'链接... (进度: 100%)
2025-08-04 23:20:00 线程2：[信息] [信息] 🖱️ 已点击跳转管理控制台链接 (进度: 100%)
2025-08-04 23:20:00 线程2：[信息] [信息] 🔗 通过导航进入IAM安全凭证页面... (进度: 100%)
2025-08-04 23:20:00 线程2：[信息] [信息] ⏳ 等待管理控制台页面加载，智能检测'更多'按钮（超时20秒）... (进度: 100%)
2025-08-04 23:20:06 [信息] 定时检查发现2个待释放手机号码，开始批量释放
2025-08-04 23:20:06 [信息] 开始释放2个手机号码
2025-08-04 23:20:06 [信息] [手机API] 开始批量释放2个手机号码
2025-08-04 23:20:06 [信息] [手机API] 释放手机号码: +529623502411
2025-08-04 23:20:06 [信息] [手机API] 手机号码释放成功: +529623502411
2025-08-04 23:20:07 [信息] [手机API] 释放手机号码: +526642542598
2025-08-04 23:20:07 [信息] [手机API] 手机号码释放成功: +526642542598
2025-08-04 23:20:08 [信息] [手机API] 批量释放完成: 成功2个, 失败0个
2025-08-04 23:20:08 [信息] 定时批量释放完成: 批量释放完成: 成功2个, 失败0个
2025-08-04 23:20:15 线程2：[信息] [信息] ✅ 找到'更多'按钮，页面加载完成 (进度: 100%)
2025-08-04 23:20:15 线程2：[信息] [信息] 🖱️ 正在点击更多按钮... (进度: 100%)
2025-08-04 23:20:16 线程2：[信息] [信息] ✅ 成功点击更多按钮 (进度: 100%)
2025-08-04 23:20:16 [信息] 成功点击更多按钮
2025-08-04 23:20:17 线程1：[信息] [信息] 点击完成注册按钮失败: Timeout 30000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Button, new() { Name = "Complete sign up" }) (进度: 100%)
2025-08-04 23:20:17 线程1：[信息] [信息] 等待完成注册页面加载... (进度: 100%)
2025-08-04 23:20:17 线程2：[信息] [信息] 🖱️ 正在点击账户信息按钮... (进度: 100%)
2025-08-04 23:20:20 线程1：[信息] [信息] 完成注册页面加载完成 (进度: 100%)
2025-08-04 23:20:20 线程1：[信息] [信息] 🔍 正在查找'Go to the AWS Management Console'链接... (进度: 100%)
2025-08-04 23:20:20 线程2：[信息] [信息] ✅ 成功点击账户信息按钮 (进度: 100%)
2025-08-04 23:20:20 [信息] 成功点击账户信息按钮
2025-08-04 23:20:21 线程2：[信息] [信息] 🖱️ 正在点击'安全凭证'菜单项... (进度: 100%)
2025-08-04 23:20:21 线程2：[信息] [信息] 🔍 正在定位'安全凭证'链接... (进度: 100%)
2025-08-04 23:20:21 线程2：[信息] [信息] ✅ 成功定位到'安全凭证'链接 (进度: 100%)
2025-08-04 23:20:21 [信息] 成功定位到'安全凭证'链接
2025-08-04 23:20:25 线程1：[信息] [信息] ❌ 控制台按钮处理失败: Timeout 5000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Link, new() { Name = "Go to the AWS Management Console" }) to be visible (进度: 100%)
2025-08-04 23:20:25 [信息] 控制台按钮处理失败: Timeout 5000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Link, new() { Name = "Go to the AWS Management Console" }) to be visible
2025-08-04 23:20:25 线程1：[信息] [信息] ⚠️ 密钥提取失败，按原本逻辑完成注册 (进度: 100%)
2025-08-04 23:20:25 [信息] 密钥提取失败，按原本逻辑完成注册
2025-08-04 23:20:25 线程1：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：2M3d8v5m6 ③AWS密码：s48KpUAK ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息： (进度: 100%)
2025-08-04 23:20:25 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：2M3d8v5m6 ③AWS密码：s48KpUAK ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 23:20:25 线程1：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：2M3d8v5m6 ③AWS密码：s48KpUAK ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 23:20:25 [信息] 线程1请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：2M3d8v5m6 ③AWS密码：s48KpUAK ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 23:20:25 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：2M3d8v5m6 ③AWS密码：s48KpUAK ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 23:20:25 [信息] 线程1剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：2M3d8v5m6 ③AWS密码：s48KpUAK ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 23:20:25 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-08-04 23:20:25 线程1：[信息] 账户注册完成(无密钥)，等待后续操作: <EMAIL>
2025-08-04 23:20:25 线程1：[信息] [信息] ✅ 注册成功（密钥提取失败） (进度: 98%)
2025-08-04 23:20:25 [信息] 注册完成（密钥提取失败）
2025-08-04 23:20:25 [信息] 开始处理数据完成事件: <EMAIL>
2025-08-04 23:20:25 [信息] 已将完成数据移动到注册成功区域: <EMAIL>
2025-08-04 23:20:25 [信息] 已完成数据移除: <EMAIL>
2025-08-04 23:20:25 [信息] 数据完成事件处理完毕: <EMAIL>
2025-08-04 23:20:25 线程1：[信息] 最终完成: 注册完成，无法提取密钥: <EMAIL> (类型: WithoutKeys)
2025-08-04 23:20:25 [信息] 线程1数据完成事件已通知主窗口并最终完成: <EMAIL>
2025-08-04 23:20:28 线程2：[信息] [信息] ✅ 成功点击'安全凭证'菜单项 (进度: 100%)
2025-08-04 23:20:28 [信息] 成功点击'安全凭证'链接
2025-08-04 23:20:28 线程2：[信息] [信息] ⏳ 等待IAM安全凭证页面加载（20秒超时，循环检测账单问题和创建密钥按钮）... (进度: 100%)
2025-08-04 23:20:43 线程3：[信息] [信息]  继续注册被调用，当前状态: WaitingForSMSVerification，当前步骤: 6 (进度: 100%)
2025-08-04 23:20:43 线程3：[信息] [信息] 第六页手动模式继续注册，检测当前页面状态... (进度: 100%)
2025-08-04 23:20:43 线程3：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-04 23:20:43 线程3：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-04 23:20:43 线程3：[信息] [信息] 🎯 找到匹配按钮: 'Continue (step 4 of 5)' → 第7页 (进度: 100%)
2025-08-04 23:20:43 线程3：[信息] [信息] ✅ 直接确认为第7页 (进度: 100%)
2025-08-04 23:20:43 线程3：[信息] [信息] 检测到已跳转到第七页，更新步骤并执行第七页逻辑 (进度: 100%)
2025-08-04 23:20:43 线程3：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-04 23:20:43 线程3：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 23:20:43 线程3：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 23:20:43 线程3：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-08-04 23:20:48 线程2：[信息] [信息] ✅ 找到'创建访问密钥'按钮，页面加载完成 (进度: 100%)
2025-08-04 23:20:48 线程2：[信息] [信息] ✅ 页面状态正常，找到创建密钥按钮，开始密钥提取流程 (进度: 100%)
2025-08-04 23:20:48 线程2：[信息] [信息] 🔍 设置页面缩放为50%... (进度: 100%)
2025-08-04 23:20:51 线程3：[信息] [信息] 线程3开始独立获取验证码... (进度: 100%)
2025-08-04 23:20:51 线程3：[信息] [信息] 线程3开始获取验证码，最多尝试8次，每次间隔8秒 (进度: 100%)
2025-08-04 23:20:51 线程3：[信息] [信息] 线程3第1次尝试获取验证码...（剩余7次尝试） (进度: 100%)
2025-08-04 23:20:51 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-04 23:20:51 线程3：[信息] [信息] 线程3验证码获取成功: 8503 (进度: 100%)
2025-08-04 23:20:51 [信息] 线程3手机号码已加入释放队列: +527531026919 (原因: 获取验证码成功)
2025-08-04 23:20:51 线程3：[信息] [信息] 线程3验证码获取成功: 8503，立即填入验证码... (进度: 100%)
2025-08-04 23:20:51 线程3：[信息] [信息] 第七页：找到验证码输入框 (Verify code) (进度: 100%)
2025-08-04 23:20:51 线程3：[信息] [信息] 线程3已自动填入手机验证码: 8503 (进度: 100%)
2025-08-04 23:20:52 线程2：[信息] [信息] ✅ 页面缩放设置完成 (进度: 100%)
2025-08-04 23:20:52 [信息] 页面缩放设置为50%完成
2025-08-04 23:20:52 线程2：[信息] [信息] 🔍 正在查找并点击'下一步'按钮关闭向导... (进度: 100%)
2025-08-04 23:20:52 [信息] 开始查找并点击'下一步'按钮关闭向导
2025-08-04 23:20:52 线程2：[信息] [信息] ✅ 找到可见的下一步按钮，使用选择器: Locator(button[data-testid='awsc-nav-services-tooltip-confirm-button']) (进度: 100%)
2025-08-04 23:20:52 [信息] 找到可见的下一步按钮，使用选择器: Locator(button[data-testid='awsc-nav-services-tooltip-confirm-button'])
2025-08-04 23:20:52 线程3：[信息] [信息] 线程3正在自动点击Continue按钮... (进度: 100%)
2025-08-04 23:20:52 线程3：[信息] [信息] 线程3手机验证码验证完成，继续执行后续步骤... (进度: 100%)
2025-08-04 23:20:53 线程2：[信息] [信息] ✅ 第一次点击'下一步'按钮完成 (选择器: Locator(button[data-testid='awsc-nav-services-tooltip-confirm-button'])) (进度: 100%)
2025-08-04 23:20:53 [信息] 第一次点击'下一步'按钮完成 (选择器: Locator(button[data-testid='awsc-nav-services-tooltip-confirm-button']))
2025-08-04 23:20:54 线程2：[信息] [信息] ℹ️ 第二次点击时按钮已消失，向导已关闭 (进度: 100%)
2025-08-04 23:20:54 [信息] 第二次点击时按钮已消失，向导已关闭
2025-08-04 23:20:54 线程2：[信息] [信息] ✅ '下一步'按钮点击流程完成 (进度: 100%)
2025-08-04 23:20:54 [信息] '下一步'按钮点击流程完成
2025-08-04 23:20:54 线程2：[信息] [信息] 🔑 第四阶段：开始创建和复制访问密钥 (进度: 100%)
2025-08-04 23:20:54 [信息] 开始创建和复制访问密钥
2025-08-04 23:20:54 线程2：[信息] [信息] 🔑 正在点击'创建访问密钥'按钮... (进度: 100%)
2025-08-04 23:20:54 线程2：[信息] [信息] 🔍 正在定位'创建访问密钥'按钮... (进度: 100%)
2025-08-04 23:20:54 线程2：[信息] [信息] ✅ 成功定位到'创建访问密钥'按钮 (进度: 100%)
2025-08-04 23:20:54 [信息] 成功定位到'创建访问密钥'按钮
2025-08-04 23:20:54 线程2：[信息] [信息] ✅ 成功点击'创建访问密钥'按钮 (进度: 100%)
2025-08-04 23:20:54 [信息] 成功点击'创建访问密钥'按钮
2025-08-04 23:20:56 线程3：[信息] [信息] 验证码验证完成，等待页面加载... (进度: 100%)
2025-08-04 23:20:56 线程3：[信息] [信息] 已点击完成注册按钮，正在处理... (进度: 100%)
2025-08-04 23:20:56 线程2：[信息] [信息] ☑️ 正在勾选确认复选框... (进度: 100%)
2025-08-04 23:20:56 线程2：[信息] [信息] ✅ 使用id属性定位到确认复选框 (进度: 100%)
2025-08-04 23:20:56 [信息] 使用id属性定位到确认复选框
2025-08-04 23:20:56 线程3：[信息] [信息] 自动模式验证完成，API手机号码已加入黑名单: 加入黑名单成功 (进度: 100%)
2025-08-04 23:20:56 线程2：[信息] [信息] ✅ 成功勾选确认复选框 (进度: 100%)
2025-08-04 23:20:56 [信息] 成功勾选确认复选框
2025-08-04 23:20:57 线程3：[信息] [信息] 等待完成注册页面加载... (进度: 100%)
2025-08-04 23:20:57 线程2：[信息] [信息] 🔑 正在点击'创建访问密钥'确认按钮... (进度: 100%)
2025-08-04 23:20:58 线程2：[信息] [信息] ✅ 成功点击'创建访问密钥'确认按钮 (进度: 100%)
2025-08-04 23:20:58 [信息] 成功点击'创建访问密钥'确认按钮
2025-08-04 23:21:00 线程3：[信息] [信息] 完成注册页面加载完成 (进度: 100%)
2025-08-04 23:21:00 线程3：[信息] [信息] 🔍 正在查找'Go to the AWS Management Console'链接... (进度: 100%)
2025-08-04 23:21:01 线程2：[信息] [信息] 📋 开始复制访问密钥... (进度: 100%)
2025-08-04 23:21:01 [信息] 开始复制访问密钥
2025-08-04 23:21:03 线程2：[信息] [信息] 🔍 方法2找到 2 个单元格 (进度: 100%)
2025-08-04 23:21:03 [信息] 方法2找到 2 个单元格
2025-08-04 23:21:03 线程2：[信息] [信息] 🔍 总共找到 2 个单元格，开始分析... (进度: 100%)
2025-08-04 23:21:03 [信息] 总共找到 2 个单元格，开始分析
2025-08-04 23:21:03 [信息] 单元格[0]: 'AKIAZY3ZX44NUUEF4FAK'
2025-08-04 23:21:03 [信息] 单元格[1]: '***************Mostrar'
2025-08-04 23:21:03 线程2：[信息] [信息] 📋 正在查找并复制访问密钥... (进度: 100%)
2025-08-04 23:21:03 线程2：[信息] [信息] ✅ 找到访问密钥: AKIAZY3ZX44NUUEF4FAK (进度: 100%)
2025-08-04 23:21:03 [信息] 找到访问密钥: AKIAZY3ZX44NUUEF4FAK
2025-08-04 23:21:03 线程2：[信息] [信息] ✅ 方法1成功点击访问密钥复制按钮 (进度: 100%)
2025-08-04 23:21:03 [信息] 方法1成功点击访问密钥复制按钮
2025-08-04 23:21:03 线程3：[信息] [信息] 🖱️ 已点击跳转管理控制台链接 (进度: 100%)
2025-08-04 23:21:03 线程3：[信息] [信息] 🔗 通过导航进入IAM安全凭证页面... (进度: 100%)
2025-08-04 23:21:03 线程3：[信息] [信息] ⏳ 等待管理控制台页面加载，智能检测'更多'按钮（超时20秒）... (进度: 100%)
2025-08-04 23:21:04 线程2：[信息] [信息] 📋 正在查找并复制秘密访问密钥... (进度: 100%)
2025-08-04 23:21:04 线程2：[信息] [信息] ✅ 找到秘密访问密钥: ***************Mostrar (进度: 100%)
2025-08-04 23:21:04 [信息] 找到秘密访问密钥: ***************Mostrar
2025-08-04 23:21:04 线程2：[信息] [信息] 🔍 正在寻找向导阻挡... (进度: 100%)
2025-08-04 23:21:04 线程2：[信息] [信息] 🔍 检测到隐藏的秘密访问密钥，尝试点击显示按钮... (进度: 100%)
2025-08-04 23:21:04 [信息] 检测到隐藏的秘密访问密钥，尝试点击显示按钮
2025-08-04 23:21:05 线程2：[信息] [信息] ✅ 使用TestId定位到显示按钮 (进度: 100%)
2025-08-04 23:21:05 [信息] 使用TestId定位到显示按钮
2025-08-04 23:21:06 [信息] 定时检查发现1个待释放手机号码，开始批量释放
2025-08-04 23:21:06 [信息] 开始释放1个手机号码
2025-08-04 23:21:06 [信息] [手机API] 开始批量释放1个手机号码
2025-08-04 23:21:06 [信息] [手机API] 释放手机号码: +527531026919
2025-08-04 23:21:06 线程2：[信息] [信息] ✅ 显示按钮点击成功，新文本: eafK2hOk/O3sRkvZ79eCuwq4npDH860UKZCqRcUTOcultar (进度: 100%)
2025-08-04 23:21:06 [信息] 显示按钮点击成功，新文本: eafK2hOk/O3sRkvZ79eCuwq4npDH860UKZCqRcUTOcultar
2025-08-04 23:21:06 线程2：[信息] [信息] ✅ 直接从显示文本提取秘密访问密钥: eafK2hOk/O3sRkvZ79eCuwq4npDH860UKZCqRcUTOcultar (进度: 100%)
2025-08-04 23:21:06 [信息] 直接从显示文本提取秘密访问密钥: eafK2hOk/O3sRkvZ79eCuwq4npDH860UKZCqRcUTOcultar
2025-08-04 23:21:06 线程2：[信息] [信息] ✅ 访问密钥复制完成 (进度: 100%)
2025-08-04 23:21:06 [信息] 访问密钥复制完成 - AccessKey: AKIAZY3ZX44NUUEF4FAK, SecretKey: eafK2hOk/O3sRkvZ79eCuwq4npDH860UKZCqRcUTOcultar
2025-08-04 23:21:06 [信息] [手机API] 手机号码释放成功: +527531026919
2025-08-04 23:21:07 [信息] [手机API] 批量释放完成: 成功1个, 失败0个
2025-08-04 23:21:07 [信息] 定时批量释放完成: 批量释放完成: 成功1个, 失败0个
2025-08-04 23:21:07 线程2：[信息] [信息] 🖱️ 正在点击'已完成'按钮... (进度: 100%)
2025-08-04 23:21:09 线程2：[信息] [信息] ✅ 成功点击'已完成'按钮 (进度: 100%)
2025-08-04 23:21:09 [信息] 成功点击'已完成'按钮
2025-08-04 23:21:09 线程2：[信息] [信息] ✅ 密钥已保存到数据对象 - AccessKey: AKIAZY3ZX44NUUEF4FAK, SecretKey: eafK2hOk/O3sRkvZ79eCuwq4npDH860UKZCqRcUTOcultar (进度: 100%)
2025-08-04 23:21:09 [信息] 密钥已保存到数据对象 - AccessKey: AKIAZY3ZX44NUUEF4FAK, SecretKey: eafK2hOk/O3sRkvZ79eCuwq4npDH860UKZCqRcUTOcultar
2025-08-04 23:21:10 线程2：[信息] [信息] 🖱️ 正在点击'继续'按钮... (进度: 100%)
2025-08-04 23:21:13 线程2：[信息] [信息] ❌ 点击'继续'按钮失败: Timeout 3000ms exceeded.
Call log:
  - waiting for Locator("button[data-awsui-analytics*='navigate']") to be visible (进度: 100%)
2025-08-04 23:21:13 [信息] 点击'继续'按钮失败: Timeout 3000ms exceeded.
Call log:
  - waiting for Locator("button[data-awsui-analytics*='navigate']") to be visible
2025-08-04 23:21:13 线程2：[信息] [信息] 🔐 开始设置MFA设备... (进度: 100%)
2025-08-04 23:21:13 [信息] 开始设置MFA设备
2025-08-04 23:21:13 线程2：[信息] [信息] 🖱️ 正在点击'分配 MFA 设备'按钮... (进度: 100%)
2025-08-04 23:21:13 线程2：[信息] [信息] ✅ 成功点击'分配 MFA 设备'按钮 (进度: 100%)
2025-08-04 23:21:13 [信息] 成功点击'分配 MFA 设备'按钮
2025-08-04 23:21:13 线程2：[信息] [信息] ⏳ 等待MFA设备设置页面加载（20秒超时）... (进度: 100%)
2025-08-04 23:21:22 [信息] 获取线程2当前数据: <EMAIL>
2025-08-04 23:21:22 线程2：[信息] 终止时正在处理的数据: <EMAIL>
2025-08-04 23:21:22 线程2：[信息] 数据详情: <EMAIL>|N5cjn8SG|Cuevas Alejandra|LAN Airlines|Coronel Pereira 140|Region Metropolitana|Santiago|7580064|4757749019159663|04|29|827|Cuevas Alejandra|50BoifV69|CL
2025-08-04 23:21:22 线程2：[信息] 多线程终止 - 复制密钥信息到剪贴板
2025-08-04 23:21:25 线程3：[信息] [信息] ⚠️ 20秒内未找到'更多'按钮，继续执行后续流程... (进度: 100%)
2025-08-04 23:21:25 [信息] 20秒内未找到'更多'按钮，但继续执行
2025-08-04 23:21:25 线程3：[信息] [信息] 🔍 智能检测更多按钮... (进度: 100%)
2025-08-04 23:21:25 线程3：[信息] [信息] 🔍 第1次检查更多按钮... (进度: 100%)
2025-08-04 23:21:25 线程2：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：50BoifV69 ③AWS密码：N5cjn8SG ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止 (进度: 100%)
2025-08-04 23:21:25 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：50BoifV69 ③AWS密码：N5cjn8SG ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 23:21:25 线程2：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：50BoifV69 ③AWS密码：N5cjn8SG ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 23:21:25 [信息] 线程2请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：50BoifV69 ③AWS密码：N5cjn8SG ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 23:21:25 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：50BoifV69 ③AWS密码：N5cjn8SG ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 23:21:25 [信息] 线程2剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：50BoifV69 ③AWS密码：N5cjn8SG ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 23:21:25 线程2：[信息] [信息] 注册已终止 (进度: 100%)
2025-08-04 23:21:25 线程2：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-08-04 23:21:25 线程2：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：50BoifV69 ③AWS密码：N5cjn8SG ④访问密钥：AKIAZY3ZX44NUUEF4FAK ⑤秘密访问密钥：eafK2hOk/O3sRkvZ79eCuwq4npDH860UKZCqRcUTOcultar ⑥MFA信息：   //手动终止 (进度: 100%)
2025-08-04 23:21:25 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：50BoifV69 ③AWS密码：N5cjn8SG ④访问密钥：AKIAZY3ZX44NUUEF4FAK ⑤秘密访问密钥：eafK2hOk/O3sRkvZ79eCuwq4npDH860UKZCqRcUTOcultar ⑥MFA信息：   //手动终止
2025-08-04 23:21:25 线程2：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：50BoifV69 ③AWS密码：N5cjn8SG ④访问密钥：AKIAZY3ZX44NUUEF4FAK ⑤秘密访问密钥：eafK2hOk/O3sRkvZ79eCuwq4npDH860UKZCqRcUTOcultar ⑥MFA信息：   //手动终止
2025-08-04 23:21:25 [信息] 线程2请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：50BoifV69 ③AWS密码：N5cjn8SG ④访问密钥：AKIAZY3ZX44NUUEF4FAK ⑤秘密访问密钥：eafK2hOk/O3sRkvZ79eCuwq4npDH860UKZCqRcUTOcultar ⑥MFA信息：   //手动终止
2025-08-04 23:21:25 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：50BoifV69 ③AWS密码：N5cjn8SG ④访问密钥：AKIAZY3ZX44NUUEF4FAK ⑤秘密访问密钥：eafK2hOk/O3sRkvZ79eCuwq4npDH860UKZCqRcUTOcultar ⑥MFA信息：   //手动终止
2025-08-04 23:21:25 [信息] 线程2剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：50BoifV69 ③AWS密码：N5cjn8SG ④访问密钥：AKIAZY3ZX44NUUEF4FAK ⑤秘密访问密钥：eafK2hOk/O3sRkvZ79eCuwq4npDH860UKZCqRcUTOcultar ⑥MFA信息：   //手动终止
2025-08-04 23:21:25 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-08-04 23:21:25 线程2：[信息] 临时目录已清理: C:\Users\<USER>\AppData\Local\Temp\AWS_Thread_2_20250804_231706
2025-08-04 23:21:25 线程2：[信息] 已终止
2025-08-04 23:21:25 [信息] 线程2已终止
2025-08-04 23:21:25 [信息] 开始处理线程2终止数据，共1个数据
2025-08-04 23:21:25 [信息] 处理线程2终止数据: <EMAIL>
2025-08-04 23:21:25 [信息] 从注册数据列表中移除: <EMAIL>
2025-08-04 23:21:25 [信息] 线程2终止 - 数据已移动到终止列表: <EMAIL>
2025-08-04 23:21:25 [信息] 线程2终止数据处理完成，成功移动1个数据
2025-08-04 23:21:25 [信息] UniformGrid列数已更新为: 1
2025-08-04 23:21:25 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-08-04 23:21:25 [信息] 线程2已终止
2025-08-04 23:21:25 线程3：[信息] [信息] ✅ 第1次检查成功找到更多按钮 (进度: 100%)
2025-08-04 23:21:25 [信息] 第1次检查成功找到更多按钮
2025-08-04 23:21:25 线程3：[信息] [信息] 🖱️ 正在点击更多按钮... (进度: 100%)
2025-08-04 23:21:28 线程3：[信息] [信息] ✅ 成功点击更多按钮 (进度: 100%)
2025-08-04 23:21:28 [信息] 成功点击更多按钮
2025-08-04 23:21:29 线程3：[信息] [信息] 🖱️ 正在点击账户信息按钮... (进度: 100%)
2025-08-04 23:21:30 线程3：[信息] [信息] ✅ 成功点击账户信息按钮 (进度: 100%)
2025-08-04 23:21:30 [信息] 成功点击账户信息按钮
2025-08-04 23:21:31 线程3：[信息] [信息] 🖱️ 正在点击'安全凭证'菜单项... (进度: 100%)
2025-08-04 23:21:31 线程3：[信息] [信息] 🔍 正在定位'安全凭证'链接... (进度: 100%)
2025-08-04 23:21:31 线程3：[信息] [信息] ✅ 成功定位到'安全凭证'链接 (进度: 100%)
2025-08-04 23:21:31 [信息] 成功定位到'安全凭证'链接
2025-08-04 23:21:35 线程3：[信息] [信息] ✅ 成功点击'安全凭证'菜单项 (进度: 100%)
2025-08-04 23:21:35 [信息] 成功点击'安全凭证'链接
2025-08-04 23:21:35 线程3：[信息] [信息] ⏳ 等待IAM安全凭证页面加载（20秒超时，循环检测账单问题和创建密钥按钮）... (进度: 100%)
2025-08-04 23:21:35 线程2：[信息] [信息] ⚠️ 20秒内未找到'设备名称'输入框，需要手动处理 (进度: 0%)
2025-08-04 23:21:35 线程2：[信息] [信息] ⚠️ 设备名称页面加载超时，需要手动处理 (进度: 0%)
2025-08-04 23:21:35 [信息] 设备名称页面加载超时，需要手动处理
2025-08-04 23:21:35 线程2：[信息] [信息] 页面加载完成后，手动点击继续注册 (进度: 0%)
2025-08-04 23:21:35 线程2：[信息] [信息] MFA页面需要手动处理，等待用户操作 (进度: 0%)
2025-08-04 23:21:56 线程3：[信息] [信息] ⚠️ 20秒超时未找到创建密钥按钮，尝试通用页面状态检查 (进度: 100%)
2025-08-04 23:21:56 线程3：[信息] [信息] 🔍 检查页面状态... (进度: 100%)
2025-08-04 23:21:56 线程3：[信息] [信息] 当前页面URL: https://us-east-1.console.aws.amazon.com/iam/home?region=ap-southeast-2#/security_credentials (进度: 100%)
2025-08-04 23:21:57 线程3：[信息] [信息] ✅ 页面状态正常，开始密钥提取流程 (进度: 100%)
2025-08-04 23:21:57 线程3：[信息] [信息] 🔍 设置页面缩放为50%... (进度: 100%)
2025-08-04 23:21:58 线程3：[信息] [信息] ✅ 页面缩放设置完成 (进度: 100%)
2025-08-04 23:21:58 [信息] 页面缩放设置为50%完成
2025-08-04 23:21:58 线程3：[信息] [信息] 🔍 正在查找并点击'下一步'按钮关闭向导... (进度: 100%)
2025-08-04 23:21:58 [信息] 开始查找并点击'下一步'按钮关闭向导
2025-08-04 23:21:58 线程3：[信息] [信息] ℹ️ 未检测到向导元素，向导可能已自动关闭 (进度: 100%)
2025-08-04 23:21:58 [信息] 未检测到向导元素，向导可能已自动关闭
2025-08-04 23:21:58 线程3：[信息] [信息] 🔑 第四阶段：开始创建和复制访问密钥 (进度: 100%)
2025-08-04 23:21:58 [信息] 开始创建和复制访问密钥
2025-08-04 23:21:58 线程3：[信息] [信息] 🔑 正在点击'创建访问密钥'按钮... (进度: 100%)
2025-08-04 23:21:58 线程3：[信息] [信息] 🔍 正在定位'创建访问密钥'按钮... (进度: 100%)
2025-08-04 23:22:00 线程3：[信息] [信息] ✅ 成功定位到'创建访问密钥'按钮 (进度: 100%)
2025-08-04 23:22:00 [信息] 成功定位到'创建访问密钥'按钮
2025-08-04 23:22:01 线程3：[信息] [信息] ✅ 成功点击'创建访问密钥'按钮 (进度: 100%)
2025-08-04 23:22:01 [信息] 成功点击'创建访问密钥'按钮
2025-08-04 23:22:03 线程3：[信息] [信息] ☑️ 正在勾选确认复选框... (进度: 100%)
2025-08-04 23:22:05 线程3：[信息] [信息] ✅ 使用id属性定位到确认复选框 (进度: 100%)
2025-08-04 23:22:05 [信息] 使用id属性定位到确认复选框
2025-08-04 23:22:06 线程3：[信息] [信息] ✅ 成功勾选确认复选框 (进度: 100%)
2025-08-04 23:22:06 [信息] 成功勾选确认复选框
2025-08-04 23:22:07 线程3：[信息] [信息] 🔑 正在点击'创建访问密钥'确认按钮... (进度: 100%)
2025-08-04 23:22:07 线程3：[信息] [信息] ✅ 成功点击'创建访问密钥'确认按钮 (进度: 100%)
2025-08-04 23:22:07 [信息] 成功点击'创建访问密钥'确认按钮
2025-08-04 23:22:10 线程3：[信息] [信息] 📋 开始复制访问密钥... (进度: 100%)
2025-08-04 23:22:10 [信息] 开始复制访问密钥
2025-08-04 23:22:12 线程3：[信息] [信息] 🔍 方法2找到 2 个单元格 (进度: 100%)
2025-08-04 23:22:12 [信息] 方法2找到 2 个单元格
2025-08-04 23:22:12 线程3：[信息] [信息] 🔍 总共找到 2 个单元格，开始分析... (进度: 100%)
2025-08-04 23:22:12 [信息] 总共找到 2 个单元格，开始分析
2025-08-04 23:22:12 [信息] 单元格[0]: 'AKIA24EWJQMMY7IVMXML'
2025-08-04 23:22:12 [信息] 单元格[1]: '***************Mostrar'
2025-08-04 23:22:12 线程3：[信息] [信息] 📋 正在查找并复制访问密钥... (进度: 100%)
2025-08-04 23:22:12 线程3：[信息] [信息] ✅ 找到访问密钥: AKIA24EWJQMMY7IVMXML (进度: 100%)
2025-08-04 23:22:12 [信息] 找到访问密钥: AKIA24EWJQMMY7IVMXML
2025-08-04 23:22:23 线程3：[信息] [信息] ✅ 方法1成功点击访问密钥复制按钮 (进度: 100%)
2025-08-04 23:22:23 [信息] 方法1成功点击访问密钥复制按钮
2025-08-04 23:22:24 线程3：[信息] [信息] 📋 正在查找并复制秘密访问密钥... (进度: 100%)
2025-08-04 23:22:24 线程3：[信息] [信息] ✅ 找到秘密访问密钥: ***************Mostrar (进度: 100%)
2025-08-04 23:22:24 [信息] 找到秘密访问密钥: ***************Mostrar
2025-08-04 23:22:24 线程3：[信息] [信息] 🔍 正在寻找向导阻挡... (进度: 100%)
2025-08-04 23:22:24 线程3：[信息] [信息] 🔍 检测到隐藏的秘密访问密钥，尝试点击显示按钮... (进度: 100%)
2025-08-04 23:22:24 [信息] 检测到隐藏的秘密访问密钥，尝试点击显示按钮
2025-08-04 23:22:24 线程3：[信息] [信息] ✅ 使用TestId定位到显示按钮 (进度: 100%)
2025-08-04 23:22:24 [信息] 使用TestId定位到显示按钮
2025-08-04 23:22:25 线程3：[信息] [信息] ✅ 显示按钮点击成功，新文本: IrmLfiGtu/v772SNjvBpqSwD0WbPGl86RNoPqMcsOcultar (进度: 100%)
2025-08-04 23:22:25 [信息] 显示按钮点击成功，新文本: IrmLfiGtu/v772SNjvBpqSwD0WbPGl86RNoPqMcsOcultar
2025-08-04 23:22:25 线程3：[信息] [信息] ✅ 直接从显示文本提取秘密访问密钥: IrmLfiGtu/v772SNjvBpqSwD0WbPGl86RNoPqMcsOcultar (进度: 100%)
2025-08-04 23:22:25 [信息] 直接从显示文本提取秘密访问密钥: IrmLfiGtu/v772SNjvBpqSwD0WbPGl86RNoPqMcsOcultar
2025-08-04 23:22:25 线程3：[信息] [信息] ✅ 访问密钥复制完成 (进度: 100%)
2025-08-04 23:22:25 [信息] 访问密钥复制完成 - AccessKey: AKIA24EWJQMMY7IVMXML, SecretKey: IrmLfiGtu/v772SNjvBpqSwD0WbPGl86RNoPqMcsOcultar
2025-08-04 23:22:26 线程3：[信息] [信息] 🖱️ 正在点击'已完成'按钮... (进度: 100%)
2025-08-04 23:22:28 线程3：[信息] [信息] ✅ 成功点击'已完成'按钮 (进度: 100%)
2025-08-04 23:22:28 [信息] 成功点击'已完成'按钮
2025-08-04 23:22:28 线程3：[信息] [信息] ✅ 密钥已保存到数据对象 - AccessKey: AKIA24EWJQMMY7IVMXML, SecretKey: IrmLfiGtu/v772SNjvBpqSwD0WbPGl86RNoPqMcsOcultar (进度: 100%)
2025-08-04 23:22:28 [信息] 密钥已保存到数据对象 - AccessKey: AKIA24EWJQMMY7IVMXML, SecretKey: IrmLfiGtu/v772SNjvBpqSwD0WbPGl86RNoPqMcsOcultar
2025-08-04 23:22:29 线程3：[信息] [信息] 🖱️ 正在点击'继续'按钮... (进度: 100%)
2025-08-04 23:22:33 线程3：[信息] [信息] ❌ 点击'继续'按钮失败: Timeout 3000ms exceeded.
Call log:
  - waiting for Locator("button[data-awsui-analytics*='navigate']") to be visible (进度: 100%)
2025-08-04 23:22:33 [信息] 点击'继续'按钮失败: Timeout 3000ms exceeded.
Call log:
  - waiting for Locator("button[data-awsui-analytics*='navigate']") to be visible
2025-08-04 23:22:33 线程3：[信息] [信息] 🔐 开始设置MFA设备... (进度: 100%)
2025-08-04 23:22:33 [信息] 开始设置MFA设备
2025-08-04 23:22:33 线程3：[信息] [信息] 🖱️ 正在点击'分配 MFA 设备'按钮... (进度: 100%)
2025-08-04 23:22:33 线程3：[信息] [信息] ✅ 成功点击'分配 MFA 设备'按钮 (进度: 100%)
2025-08-04 23:22:34 [信息] 成功点击'分配 MFA 设备'按钮
2025-08-04 23:22:34 线程3：[信息] [信息] ⏳ 等待MFA设备设置页面加载（20秒超时）... (进度: 100%)
2025-08-04 23:22:50 [信息] 获取线程3当前数据: <EMAIL>
2025-08-04 23:22:50 线程3：[信息] 终止时正在处理的数据: <EMAIL>
2025-08-04 23:22:50 线程3：[信息] 数据详情: <EMAIL>|tkyQzap3|Escobar Lesly|Antofagasta|Av vicua mackenna 881 1302 b|Region Metropolitana|Santiago|8320000|4830310072933519|09|31|404|Escobar Lesly|CxbGEYssJ04|CL
2025-08-04 23:22:50 线程3：[信息] 多线程终止 - 复制密钥信息到剪贴板
2025-08-04 23:22:50 线程3：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：CxbGEYssJ04 ③AWS密码：tkyQzap3 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止 (进度: 100%)
2025-08-04 23:22:50 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：CxbGEYssJ04 ③AWS密码：tkyQzap3 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 23:22:50 线程3：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：CxbGEYssJ04 ③AWS密码：tkyQzap3 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 23:22:50 [信息] 线程3请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：CxbGEYssJ04 ③AWS密码：tkyQzap3 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 23:22:50 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：CxbGEYssJ04 ③AWS密码：tkyQzap3 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 23:22:50 [信息] 线程3剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：CxbGEYssJ04 ③AWS密码：tkyQzap3 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 23:22:50 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 23:22:50 [信息] 多线程状态已重置
2025-08-04 23:22:50 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 23:22:50 [信息] 多线程状态已重置
2025-08-04 23:22:50 线程3：[信息] [信息] 注册已终止 (进度: 100%)
2025-08-04 23:22:50 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 23:22:50 [信息] 多线程状态已重置
2025-08-04 23:22:50 线程3：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-08-04 23:22:50 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 23:22:50 [信息] 多线程状态已重置
2025-08-04 23:22:50 线程3：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：CxbGEYssJ04 ③AWS密码：tkyQzap3 ④访问密钥：AKIA24EWJQMMY7IVMXML ⑤秘密访问密钥：IrmLfiGtu/v772SNjvBpqSwD0WbPGl86RNoPqMcsOcultar ⑥MFA信息：   //手动终止 (进度: 100%)
2025-08-04 23:22:50 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：CxbGEYssJ04 ③AWS密码：tkyQzap3 ④访问密钥：AKIA24EWJQMMY7IVMXML ⑤秘密访问密钥：IrmLfiGtu/v772SNjvBpqSwD0WbPGl86RNoPqMcsOcultar ⑥MFA信息：   //手动终止
2025-08-04 23:22:50 线程3：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：CxbGEYssJ04 ③AWS密码：tkyQzap3 ④访问密钥：AKIA24EWJQMMY7IVMXML ⑤秘密访问密钥：IrmLfiGtu/v772SNjvBpqSwD0WbPGl86RNoPqMcsOcultar ⑥MFA信息：   //手动终止
2025-08-04 23:22:50 [信息] 线程3请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：CxbGEYssJ04 ③AWS密码：tkyQzap3 ④访问密钥：AKIA24EWJQMMY7IVMXML ⑤秘密访问密钥：IrmLfiGtu/v772SNjvBpqSwD0WbPGl86RNoPqMcsOcultar ⑥MFA信息：   //手动终止
2025-08-04 23:22:50 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：CxbGEYssJ04 ③AWS密码：tkyQzap3 ④访问密钥：AKIA24EWJQMMY7IVMXML ⑤秘密访问密钥：IrmLfiGtu/v772SNjvBpqSwD0WbPGl86RNoPqMcsOcultar ⑥MFA信息：   //手动终止
2025-08-04 23:22:50 [信息] 线程3剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：CxbGEYssJ04 ③AWS密码：tkyQzap3 ④访问密钥：AKIA24EWJQMMY7IVMXML ⑤秘密访问密钥：IrmLfiGtu/v772SNjvBpqSwD0WbPGl86RNoPqMcsOcultar ⑥MFA信息：   //手动终止
2025-08-04 23:22:50 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 23:22:50 [信息] 多线程状态已重置
2025-08-04 23:22:50 线程3：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-08-04 23:22:50 线程3：[信息] 临时目录已清理: C:\Users\<USER>\AppData\Local\Temp\AWS_Thread_3_20250804_231706
2025-08-04 23:22:50 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-04 23:22:50 [信息] 多线程状态已重置
2025-08-04 23:22:50 线程3：[信息] 已终止
2025-08-04 23:22:50 [信息] 线程3已终止
2025-08-04 23:22:50 [信息] 开始处理线程3终止数据，共1个数据
2025-08-04 23:22:50 [信息] 处理线程3终止数据: <EMAIL>
2025-08-04 23:22:50 [信息] 从注册数据列表中移除: <EMAIL>
2025-08-04 23:22:50 [信息] 线程3终止 - 数据已移动到终止列表: <EMAIL>
2025-08-04 23:22:50 [信息] 线程3终止数据处理完成，成功移动1个数据
2025-08-04 23:22:50 [信息] UniformGrid列数已更新为: 1
2025-08-04 23:22:50 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 1, 列数: 1
2025-08-04 23:22:50 [信息] 线程3已终止
2025-08-04 23:22:54 线程3：[信息] [信息] ⚠️ 20秒内未找到'设备名称'输入框，需要手动处理 (进度: 0%)
2025-08-04 23:22:54 线程3：[信息] [信息] ⚠️ 设备名称页面加载超时，需要手动处理 (进度: 0%)
2025-08-04 23:22:54 [信息] 设备名称页面加载超时，需要手动处理
2025-08-04 23:22:54 线程3：[信息] [信息] 页面加载完成后，手动点击继续注册 (进度: 0%)
2025-08-04 23:22:54 线程3：[信息] [信息] MFA页面需要手动处理，等待用户操作 (进度: 0%)
2025-08-04 23:22:54 线程3：[信息] 已继续
2025-08-04 23:22:54 [信息] 线程3已继续
2025-08-04 23:24:57 [信息] 多线程窗口引用已清理
2025-08-04 23:24:57 [信息] 主窗口已恢复正常状态，多线程窗口引用已清理
2025-08-04 23:24:57 [信息] 多线程管理窗口正在关闭
2025-08-04 23:25:00 [信息] 程序正在退出，开始清理工作...
2025-08-04 23:25:00 [信息] 程序退出，开始清理所有待拉黑千川手机号码
2025-08-04 23:25:00 [信息] 程序退出清理完成: 没有需要拉黑的千川手机号码
2025-08-04 23:25:00 [信息] 程序退出，开始清理所有待释放手机号码
2025-08-04 23:25:00 [信息] 程序退出清理完成: 没有需要释放的手机号码
2025-08-04 23:25:00 [信息] 程序退出清理工作完成
2025-08-04 23:25:03 [信息] AWS自动注册工具启动
2025-08-04 23:25:03 [信息] 程序版本: 1.0.0.0
2025-08-04 23:25:03 [信息] 启动时间: 2025-08-04 23:25:03
2025-08-04 23:25:03 [信息] 浏览器模式切换: 无痕 Chrome 模式
2025-08-04 23:25:03 [信息] 线程数量已选择: 1
2025-08-04 23:25:03 [信息] 线程数量选择初始化完成
2025-08-04 23:25:03 [信息] 程序初始化完成
2025-08-04 23:25:05 [按钮操作] 选择文件 -> 打开文件选择对话框
2025-08-04 23:25:07 [信息] 已选择文件: C:\Users\<USER>\Desktop\2025-08-04-智利.txt
2025-08-04 23:25:08 [按钮操作] 加载信息 -> 加载数据文件: C:\Users\<USER>\Desktop\2025-08-04-智利.txt
2025-08-04 23:25:08 [信息] 成功加载 3 条数据
2025-08-04 23:25:09 [信息] 线程数量已选择: 3
2025-08-04 23:25:11 [按钮操作] 开始注册 -> 启动注册流程
2025-08-04 23:25:11 [信息] 开始启动多线程注册，线程数量: 3
2025-08-04 23:25:11 [信息] 开始启动多线程注册，线程数量: 3，数据条数: 3
2025-08-04 23:25:11 [信息] 所有线程已停止并清理
2025-08-04 23:25:11 [信息] 正在初始化多线程服务...
2025-08-04 23:25:11 [信息] 榴莲手机API服务已初始化
2025-08-04 23:25:11 [信息] 手机号码管理器已初始化，服务商: Durian，将在第一个线程完成第二页后获取手机号码
2025-08-04 23:25:11 [信息] 多线程服务初始化完成
2025-08-04 23:25:11 [信息] 数据分配完成：共3条数据分配给3个线程
2025-08-04 23:25:11 [信息] 线程1分配到1条数据
2025-08-04 23:25:11 [信息] 线程2分配到1条数据
2025-08-04 23:25:11 [信息] 线程3分配到1条数据
2025-08-04 23:25:11 [信息] 屏幕工作区域: 1280x672
2025-08-04 23:25:11 [信息] 线程1窗口布局: 位置(0, 0), 大小(384x200), 列1行1, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-04 23:25:11 [信息] 线程1获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-04 23:25:11 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 23:25:11 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_011, WebGL=ANGLE (NVIDIA GeForce RTX 3070 Direct3D11 vs_5_0 ps_5_0), CPU=12核, RAM=6 GB
2025-08-04 23:25:11 线程1：[信息] 已创建，窗口位置: (0, 0)
2025-08-04 23:25:11 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-04 23:25:11 [信息] 线程1已创建，窗口位置: (0, 0)，指纹: 国家=CL, 时区=America/Santiago
2025-08-04 23:25:11 [信息] 屏幕工作区域: 1280x672
2025-08-04 23:25:11 [信息] 线程2窗口布局: 位置(0, 219), 大小(384x200), 列1行2, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-04 23:25:11 [信息] 线程2获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-04 23:25:11 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 23:25:11 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_008, WebGL=ANGLE (NVIDIA GeForce RTX 3080 Direct3D11 vs_5_0 ps_5_0), CPU=20核, RAM=16 GB
2025-08-04 23:25:11 线程2：[信息] 已创建，窗口位置: (0, 219)
2025-08-04 23:25:11 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-04 23:25:11 [信息] 线程2已创建，窗口位置: (0, 219)，指纹: 国家=CL, 时区=America/Santiago
2025-08-04 23:25:11 [信息] 屏幕工作区域: 1280x672
2025-08-04 23:25:11 [信息] 线程3窗口布局: 位置(0, 438), 大小(384x200), 列1行3, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-04 23:25:11 [信息] 线程3获取到数据: Email=<EMAIL>, CountryCode=CL
2025-08-04 23:25:11 [信息] 为国家代码 CL 生成智能指纹: 时区=America/Santiago, 语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 23:25:11 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_008, WebGL=ANGLE (Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0), CPU=4核, RAM=32 GB
2025-08-04 23:25:11 线程3：[信息] 已创建，窗口位置: (0, 438)
2025-08-04 23:25:11 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-04 23:25:11 [信息] 线程3已创建，窗口位置: (0, 438)，指纹: 国家=CL, 时区=America/Santiago
2025-08-04 23:25:11 [信息] 多线程注册启动成功，共3个线程
2025-08-04 23:25:11 线程1：[信息] 开始启动注册流程
2025-08-04 23:25:11 线程2：[信息] 开始启动注册流程
2025-08-04 23:25:11 线程3：[信息] 开始启动注册流程
2025-08-04 23:25:11 线程1：[信息] 开始启动浏览器: 位置(0, 0), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36
2025-08-04 23:25:11 线程2：[信息] 开始启动浏览器: 位置(0, 219), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0
2025-08-04 23:25:11 线程2：[信息] 启动无痕Chrome浏览器...
2025-08-04 23:25:11 线程1：[信息] 启动无痕Chrome浏览器...
2025-08-04 23:25:11 线程3：[信息] 开始启动浏览器: 位置(0, 438), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0
2025-08-04 23:25:11 线程3：[信息] 启动无痕Chrome浏览器...
2025-08-04 23:25:11 线程2：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-04 23:25:11 线程1：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-04 23:25:11 线程3：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-04 23:25:11 [信息] 多线程管理窗口已初始化
2025-08-04 23:25:11 [信息] UniformGrid列数已更新为: 1
2025-08-04 23:25:11 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 0, 列数: 1
2025-08-04 23:25:11 [信息] 多线程管理窗口已打开
2025-08-04 23:25:11 [信息] 多线程注册启动成功，共3个线程
2025-08-04 23:25:24 [信息] UniformGrid列数已更新为: 1
2025-08-04 23:25:24 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 1, 列数: 1
2025-08-04 23:25:24 线程2：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-04 23:25:24 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-04 23:25:24 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36
2025-08-04 23:25:24 线程2：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-04 23:25:24 [信息] UniformGrid列数已更新为: 1
2025-08-04 23:25:24 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-08-04 23:25:24 线程3：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-04 23:25:24 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-04 23:25:24 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0) Gecko/20100101 Firefox/119.0
2025-08-04 23:25:24 线程3：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-04 23:25:24 [信息] UniformGrid列数已更新为: 2
2025-08-04 23:25:24 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 3, 列数: 2
2025-08-04 23:25:24 线程1：[信息] [信息] 多线程模式根据指纹国家代码 CL 设置浏览器语言: Español (Chile) (进度: 0%)
2025-08-04 23:25:24 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=CL, 语言=Español (Chile), 参数=--lang=es-CL
2025-08-04 23:25:24 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0
2025-08-04 23:25:24 线程1：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-04 23:25:26 线程3：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-04 23:25:26 线程2：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-04 23:25:26 线程1：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-04 23:25:28 线程3：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-04 23:25:28 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-04 23:25:28 线程3：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-04 23:25:28 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 23:25:28 线程3：[信息] [信息] 多线程模式使用指纹地理位置: -29.9027, -71.2519 (进度: 0%)
2025-08-04 23:25:28 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-29.9027, 经度=-71.2519
2025-08-04 23:25:28 线程3：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_008, CPU: 4核 (进度: 0%)
2025-08-04 23:25:28 [信息] 浏览器指纹注入: Canvas=canvas_fp_008, WebGL=ANGLE (Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0), CPU=4核, RAM=32 GB
2025-08-04 23:25:28 线程2：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-04 23:25:28 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-04 23:25:28 线程2：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-04 23:25:28 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 23:25:28 线程2：[信息] [信息] 多线程模式使用指纹地理位置: -36.8201, -73.0444 (进度: 0%)
2025-08-04 23:25:28 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-36.8201, 经度=-73.0444
2025-08-04 23:25:28 线程1：[信息] [信息] 多线程模式使用指纹时区: America/Santiago (进度: 0%)
2025-08-04 23:25:28 [信息] 浏览器时区设置: 多线程模式使用指纹时区=America/Santiago
2025-08-04 23:25:28 线程1：[信息] [信息] 多线程模式使用指纹语言: es-CL,es;q=0.9,en;q=0.8 (进度: 0%)
2025-08-04 23:25:28 [信息] 浏览器语言设置: 多线程模式使用指纹语言=es-CL,es;q=0.9,en;q=0.8
2025-08-04 23:25:28 线程1：[信息] [信息] 多线程模式使用指纹地理位置: -41.4693, -72.9424 (进度: 0%)
2025-08-04 23:25:28 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=-41.4693, 经度=-72.9424
2025-08-04 23:25:29 线程1：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_011, CPU: 12核 (进度: 0%)
2025-08-04 23:25:29 [信息] 浏览器指纹注入: Canvas=canvas_fp_011, WebGL=ANGLE (NVIDIA GeForce RTX 3070 Direct3D11 vs_5_0 ps_5_0), CPU=12核, RAM=6 GB
2025-08-04 23:25:30 线程3：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-04 23:25:30 线程1：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-04 23:25:31 线程3：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 4
   • 设备内存: 32 GB
   • 平台信息: Win32
   • Do Not Track: unspecified

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (Intel)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 5A6B7C8D
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_004
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-E4F5G6H
   • MAC地址: E4-42-A6-5D-13-98
   • 屏幕分辨率: 1939x1066
   • 可用区域: 1939x1026

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: C1D2E3F4
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: satellite
   • 电池API支持: True
   • 电池电量: 0.83
   • 电池充电状态: False
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-04 23:25:31 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 4    • 设备内存: 32 GB    • 平台信息: Win32    • Do Not Track: unspecified   图形渲染信息:    • WebGL渲染器: ANGLE (Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (Intel)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 5A6B7C8D    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_004    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-E4F5G6H    • MAC地址: E4-42-A6-5D-13-98    • 屏幕分辨率: 1939x1066    • 可用区域: 1939x1026   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: C1D2E3F4    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: satellite    • 电池API支持: True    • 电池电量: 0.83    • 电池充电状态: False    • 性能API支持: True  ================================
2025-08-04 23:25:31 线程3：[信息] [信息] 已设置浏览器标题: 线程3 - AWS注册工具 (进度: 5%)
2025-08-04 23:25:31 线程3：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-04 23:25:31 线程3：[信息] 浏览器启动成功
2025-08-04 23:25:31 线程3：[信息] 获取下一个数据: <EMAIL>
2025-08-04 23:25:31 线程3：[信息] 开始处理账户: <EMAIL>
2025-08-04 23:25:31 线程3：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-04 23:25:31 线程3：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-04 23:25:31 线程3：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-04 23:25:31 线程3：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-04 23:25:31 线程3：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-04 23:25:32 线程3：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-04 23:25:32 线程3：[信息] [信息] 已重新设置浏览器标题: 线程3 - AWS注册工具 (进度: 98%)
2025-08-04 23:25:32 线程3：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-04 23:25:32 线程3：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-04 23:25:32 线程3：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0... (进度: 98%)
2025-08-04 23:25:32 线程3：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-04 23:25:32 线程1：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 12
   • 设备内存: 6 GB
   • 平台信息: Win32
   • Do Not Track: default

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 3070 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (AMD Inc.)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 3A4B5C6D
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_004
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-A1B2C3D
   • MAC地址: 56-78-9A-BC-DE-F0
   • 屏幕分辨率: 1899x1145
   • 可用区域: 1899x1105

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: E9F0A1B2
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: 3g
   • 电池API支持: True
   • 电池电量: 0.26
   • 电池充电状态: False
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-04 23:25:32 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 12    • 设备内存: 6 GB    • 平台信息: Win32    • Do Not Track: default   图形渲染信息:    • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 3070 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (AMD Inc.)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 3A4B5C6D    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_004    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-A1B2C3D    • MAC地址: 56-78-9A-BC-DE-F0    • 屏幕分辨率: 1899x1145    • 可用区域: 1899x1105   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: E9F0A1B2    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: 3g    • 电池API支持: True    • 电池电量: 0.26    • 电池充电状态: False    • 性能API支持: True  ================================
2025-08-04 23:25:32 线程1：[信息] [信息] 已设置浏览器标题: 线程1 - AWS注册工具 (进度: 5%)
2025-08-04 23:25:32 线程1：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-04 23:25:32 线程1：[信息] 浏览器启动成功
2025-08-04 23:25:32 线程1：[信息] 获取下一个数据: <EMAIL>
2025-08-04 23:25:32 线程1：[信息] 开始处理账户: <EMAIL>
2025-08-04 23:25:32 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-04 23:25:32 线程1：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-04 23:25:32 线程1：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-04 23:25:32 线程1：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-04 23:25:32 线程1：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-04 23:25:32 线程1：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-04 23:25:32 线程1：[信息] [信息] 已重新设置浏览器标题: 线程1 - AWS注册工具 (进度: 98%)
2025-08-04 23:25:32 线程1：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-04 23:25:32 线程1：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-04 23:25:32 线程1：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0... (进度: 98%)
2025-08-04 23:25:32 线程1：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-04 23:25:33 线程2：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_008, CPU: 20核 (进度: 0%)
2025-08-04 23:25:33 [信息] 浏览器指纹注入: Canvas=canvas_fp_008, WebGL=ANGLE (NVIDIA GeForce RTX 3080 Direct3D11 vs_5_0 ps_5_0), CPU=20核, RAM=16 GB
2025-08-04 23:25:34 线程2：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-04 23:25:35 线程2：[信息] [信息] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 20
   • 设备内存: 16 GB
   • 平台信息: Win32
   • Do Not Track: system

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 3080 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (Advanced Micro Devices, Inc.)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 1C2D3E4F
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_008
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-K8L9M0N
   • MAC地址: 11-22-33-44-55-66
   • 屏幕分辨率: 1893x1119
   • 可用区域: 1893x1079

🌍 地区语言信息:
   • 主语言: es-CL
   • 语言列表: es-CL,en-US
   • 时区偏移: 180分钟

🔧 高级功能信息:
   • ClientRects ID: E1F2A3B4
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: slow-2g
   • 电池API支持: True
   • 电池电量: 0.76
   • 电池充电状态: False
   • 性能API支持: True
🔍 ================================ (进度: 5%)
2025-08-04 23:25:35 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 20    • 设备内存: 16 GB    • 平台信息: Win32    • Do Not Track: system   图形渲染信息:    • WebGL渲染器: ANGLE (NVIDIA GeForce RTX 3080 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (Advanced Micro Devices, Inc.)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 1C2D3E4F    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_008    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-K8L9M0N    • MAC地址: 11-22-33-44-55-66    • 屏幕分辨率: 1893x1119    • 可用区域: 1893x1079   地区语言信息:    • 主语言: es-CL    • 语言列表: es-CL,en-US    • 时区偏移: 180分钟   高级功能信息:    • ClientRects ID: E1F2A3B4    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: slow-2g    • 电池API支持: True    • 电池电量: 0.76    • 电池充电状态: False    • 性能API支持: True  ================================
2025-08-04 23:25:35 线程2：[信息] [信息] 已设置浏览器标题: 线程2 - AWS注册工具 (进度: 5%)
2025-08-04 23:25:35 线程2：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 5%)
2025-08-04 23:25:35 线程2：[信息] 浏览器启动成功
2025-08-04 23:25:35 线程2：[信息] 获取下一个数据: <EMAIL>
2025-08-04 23:25:35 线程2：[信息] 开始处理账户: <EMAIL>
2025-08-04 23:25:35 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-04 23:25:35 线程2：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-04 23:25:35 线程2：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-04 23:25:35 线程2：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-04 23:25:35 线程2：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-04 23:25:36 线程2：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-04 23:25:36 线程2：[信息] [信息] 已重新设置浏览器标题: 线程2 - AWS注册工具 (进度: 98%)
2025-08-04 23:25:36 线程2：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-04 23:25:36 线程2：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-04 23:25:36 线程2：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-04 23:25:36 线程2：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-04 23:25:52 线程1：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程1 - AWS注册 (进度: 98%)
2025-08-04 23:25:52 线程1：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-04 23:25:52 线程1：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-04 23:25:52 线程1：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-04 23:25:52 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-04 23:25:53 线程1：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-04 23:25:56 线程1：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-04 23:25:56 线程1：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-04 23:25:56 线程1：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-04 23:25:56 线程1：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-04 23:25:56 线程1：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 23:25:56 线程1：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-04 23:25:57 线程2：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程2 - AWS注册 (进度: 98%)
2025-08-04 23:25:57 线程2：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-04 23:25:57 线程2：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-04 23:25:57 线程2：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-04 23:25:57 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-04 23:25:57 线程2：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-04 23:25:58 线程1：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-04 23:25:58 线程1：[信息] [信息] 🔍 第2次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 23:25:58 线程1：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 未发现验证码 (进度: 100%)
2025-08-04 23:25:58 线程1：[信息] [信息] ✅ 第一页未检测到图形验证码，继续流程 (进度: 100%)
2025-08-04 23:25:58 线程1：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-04 23:25:58 线程1：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-04 23:25:58 线程1：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-04 23:25:58 线程1：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-04 23:25:58 线程1：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-04 23:25:58 线程1：[信息] 账户注册流程已启动: <EMAIL>
2025-08-04 23:25:58 线程1：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-04 23:25:58 [信息] [线程1] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-04 23:25:58 [信息] [线程1] 等待2秒后开始第一次触发...
2025-08-04 23:26:00 [信息] [线程1] 第1次触发邮箱验证码获取...（最多20次）
2025-08-04 23:26:00 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 23:26:00
2025-08-04 23:26:00 线程2：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-04 23:26:01 线程2：[信息] [信息] ⚠️ 检测到错误信息，开始重试机制... (进度: 98%)
2025-08-04 23:26:01 [信息] 检测到错误信息，开始重试机制
2025-08-04 23:26:01 线程2：[信息] [信息] 🔄 第1次重试点击验证邮箱按钮... (进度: 98%)
2025-08-04 23:26:01 [信息] 第1次重试点击验证邮箱按钮
2025-08-04 23:26:03 线程2：[信息] [信息] ✅ 第1次重试：已点击验证邮箱按钮 (进度: 100%)
2025-08-04 23:26:03 [信息] 第1次重试：已点击验证邮箱按钮
2025-08-04 23:26:03 [信息] [线程1] 第2次触发邮箱验证码获取...（最多20次）
2025-08-04 23:26:03 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 23:26:03
2025-08-04 23:26:05 线程2：[信息] [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']" (进度: 100%)
2025-08-04 23:26:05 [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']"
2025-08-04 23:26:05 线程2：[信息] [信息] ✅ 第1次重试成功：已到达第二页，继续正常流程 (进度: 100%)
2025-08-04 23:26:05 [信息] 第1次重试成功：已到达第二页
2025-08-04 23:26:05 线程2：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-04 23:26:05 线程2：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-04 23:26:05 线程2：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 23:26:05 线程2：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-04 23:26:06 [信息] [线程1] 第3次触发邮箱验证码获取...（最多20次）
2025-08-04 23:26:06 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-04 23:26:06
2025-08-04 23:26:07 线程2：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-04 23:26:07 线程2：[信息] [信息] 🔍 第2次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-04 23:26:07 线程2：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 未发现验证码 (进度: 100%)
2025-08-04 23:26:07 线程2：[信息] [信息] ✅ 第一页未检测到图形验证码，继续流程 (进度: 100%)
2025-08-04 23:26:07 线程2：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-04 23:26:07 线程2：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-04 23:26:07 线程2：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-04 23:26:07 线程2：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-04 23:26:07 线程2：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-04 23:26:07 线程2：[信息] 账户注册流程已启动: <EMAIL>
2025-08-04 23:26:07 线程2：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-04 23:26:07 [信息] [线程2] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-04 23:26:07 [信息] [线程2] 等待2秒后开始第一次触发...
2025-08-04 23:26:09 [信息] [线程2] 第1次触发邮箱验证码获取...（最多20次）
2025-08-04 23:26:09 [信息] [线程1] 邮箱验证码获取成功: 949731，立即停止重复请求
2025-08-04 23:26:09 [信息] [线程1] 已清理请求文件，停止重复触发
2025-08-04 23:26:09 [信息] [线程1] 已清理响应文件
2025-08-04 23:26:09 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-04 23:26:09
2025-08-04 23:26:09 线程1：[信息] [信息] 验证码获取成功: 949731，正在自动填入... (进度: 25%)
2025-08-04 23:26:09 线程1：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 25%)
2025-08-04 23:26:10 线程1：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 25%)
2025-08-04 23:26:10 线程1：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 25%)
2025-08-04 23:26:10 [信息] 线程1完成第二页事件已处理
2025-08-04 23:26:10 [信息] 线程1完成第二页，开始批量获取手机号码...
2025-08-04 23:26:10 线程1：[信息] [信息] 线程1第二页验证完成，已通知管理器 (进度: 35%)
2025-08-04 23:26:10 [信息] 开始批量获取3个手机号码，服务商: Durian
2025-08-04 23:26:10 [信息] [手机API] 批量获取3个手机号码，URL: https://api.durianrcs.com/out/ext_api/getMobile?name=oneone&ApiKey=NnlaR01xQm9hMlAwRnJDSVB2SG1kQT09&cuy=mx&pid=0209&num=3&noblack=0&serial=2&secret_key=null&vip=null
2025-08-04 23:26:12 [信息] [线程2] 第2次触发邮箱验证码获取...（最多20次）
2025-08-04 23:26:12 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-04 23:26:12
2025-08-04 23:26:13 [信息] [手机API] 批量获取响应内容: {"code":200,"msg":"Success","data":["+526644304714","+526651497485","+522713919387"]}
2025-08-04 23:26:13 [信息] [手机API] 检测到数组格式，元素数量: 3
2025-08-04 23:26:13 [信息] [手机API] 批量获取成功，获得3个手机号码
2025-08-04 23:26:13 [信息] 线程1分配榴莲手机号码: +526644304714
2025-08-04 23:26:13 [信息] 线程2分配榴莲手机号码: +526651497485
2025-08-04 23:26:13 [信息] 线程3分配榴莲手机号码: +522713919387
2025-08-04 23:26:13 [信息] 榴莲API批量获取手机号码成功，已分配给3个线程
2025-08-04 23:26:13 [信息] 批量获取3个手机号码成功
2025-08-04 23:26:13 线程1：[信息] [信息] 等待密码设置页面加载... (进度: 38%)
2025-08-04 23:26:13 线程1：[信息] [信息] 开始填写密码信息... (进度: 38%)
2025-08-04 23:26:14 线程1：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-04 23:26:14 线程1：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-04 23:26:14 线程1：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 38%)
2025-08-04 23:26:15 线程1：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 38%)
2025-08-04 23:26:15 [信息] [线程2] 第3次触发邮箱验证码获取...（最多20次）
2025-08-04 23:26:15 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-04 23:26:15
2025-08-04 23:26:17 [信息] [线程2] 邮箱验证码获取成功: 319045，立即停止重复请求
2025-08-04 23:26:17 [信息] [线程2] 已清理请求文件，停止重复触发
2025-08-04 23:26:17 [信息] [线程2] 已清理响应文件
2025-08-04 23:26:17 线程2：[信息] [信息] 验证码获取成功: 319045，正在自动填入... (进度: 25%)
2025-08-04 23:26:17 线程2：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 25%)
2025-08-04 23:26:17 线程2：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 25%)
2025-08-04 23:26:17 线程2：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 25%)
2025-08-04 23:26:17 [信息] 线程2完成第二页事件已处理
2025-08-04 23:26:17 [信息] 线程2完成第二页，手机号码已获取，无需重复获取
2025-08-04 23:26:17 线程2：[信息] [信息] 线程2第二页验证完成，已通知管理器 (进度: 35%)
2025-08-04 23:26:18 线程1：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 38%)
2025-08-04 23:26:18 线程1：[信息] [信息] 等待账户类型确认页面加载... (进度: 38%)
2025-08-04 23:26:18 线程1：[信息] [信息] 开始处理账户类型确认... (进度: 38%)
2025-08-04 23:26:20 线程2：[信息] [信息] 等待密码设置页面加载... (进度: 38%)
2025-08-04 23:26:20 线程2：[信息] [信息] 开始填写密码信息... (进度: 38%)
2025-08-04 23:26:22 线程2：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-04 23:26:22 线程2：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-04 23:26:22 线程2：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 38%)
2025-08-04 23:26:24 线程2：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 38%)
2025-08-04 23:26:24 线程1：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 38%)
2025-08-04 23:26:24 线程1：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 38%)
2025-08-04 23:26:27 线程2：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 38%)
2025-08-04 23:26:27 线程2：[信息] [信息] 等待账户类型确认页面加载... (进度: 38%)
2025-08-04 23:26:27 线程2：[信息] [信息] 开始处理账户类型确认... (进度: 38%)
2025-08-04 23:26:31 线程2：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 38%)
2025-08-04 23:26:31 线程2：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 38%)
2025-08-04 23:26:41 线程2：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 38%)
2025-08-04 23:26:41 [信息] 线程2获取已分配的榴莲手机号码: +526651497485
2025-08-04 23:26:41 线程2：[信息] [信息] 多线程模式：使用已分配的手机号码 +526651497485 (进度: 38%)
2025-08-04 23:26:43 线程2：[信息] [信息] 数据国家代码为CL，需要选择Chile (进度: 38%)
2025-08-04 23:26:43 线程2：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 38%)
2025-08-04 23:26:43 线程1：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 38%)
2025-08-04 23:26:43 [信息] 线程1获取已分配的榴莲手机号码: +526644304714
2025-08-04 23:26:43 线程1：[信息] [信息] 多线程模式：使用已分配的手机号码 +526644304714 (进度: 38%)
2025-08-04 23:26:44 线程1：[信息] [信息] 数据国家代码为CL，需要选择Chile (进度: 38%)
2025-08-04 23:26:44 线程1：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 38%)
2025-08-04 23:26:45 线程2：[信息] [信息] 已选择国家: Chile (进度: 38%)
2025-08-04 23:26:45 线程2：[信息] [信息] 已成功选择国家: Chile (进度: 38%)
2025-08-04 23:26:45 线程2：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 38%)
2025-08-04 23:26:45 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-08-04 23:26:46 线程1：[信息] [信息] 已选择国家: Chile (进度: 38%)
2025-08-04 23:26:46 线程1：[信息] [信息] 已成功选择国家: Chile (进度: 38%)
2025-08-04 23:26:46 线程1：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 38%)
2025-08-04 23:26:46 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-08-04 23:26:51 线程3：[信息] [信息] 注册失败: Execution context was destroyed, most likely because of a navigation. (进度: 98%)
2025-08-04 23:26:51 线程3：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：ZEGQp06L3sU ③AWS密码：C9s41WbR ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息： (进度: 98%)
2025-08-04 23:26:51 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：ZEGQp06L3sU ③AWS密码：C9s41WbR ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 23:26:51 线程3：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：ZEGQp06L3sU ③AWS密码：C9s41WbR ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 23:26:51 [信息] 线程3请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：ZEGQp06L3sU ③AWS密码：C9s41WbR ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 23:26:51 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：ZEGQp06L3sU ③AWS密码：C9s41WbR ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 23:26:51 [信息] 线程3剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：ZEGQp06L3sU ③AWS密码：C9s41WbR ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-04 23:26:51 线程3：[错误] 账户注册启动失败: <EMAIL>
2025-08-04 23:26:51 线程3：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-04 23:26:56 线程2：[信息] [信息] 已选择国家代码 +52 (进度: 38%)
2025-08-04 23:26:57 线程2：[信息] [信息] 等待后台获取的手机号码结果... (进度: 38%)
2025-08-04 23:26:57 线程2：[信息] [信息] 已自动获取并填入手机号码: +526651497485 (进度: 38%)
2025-08-04 23:26:57 线程1：[信息] [信息] 已选择国家代码 +52 (进度: 38%)
2025-08-04 23:26:58 线程1：[信息] [信息] 等待后台获取的手机号码结果... (进度: 38%)
2025-08-04 23:26:58 线程1：[信息] [信息] 已自动获取并填入手机号码: +526644304714 (进度: 38%)
2025-08-04 23:26:58 线程2：[信息] [信息] 使用已获取的手机号码: +526651497485（保存本地号码: +526651497485） (进度: 38%)
2025-08-04 23:26:58 线程2：[信息] [信息] 联系信息完成，等待页面加载... (进度: 38%)
2025-08-04 23:26:59 线程1：[信息] [信息] 使用已获取的手机号码: +526644304714（保存本地号码: +526644304714） (进度: 38%)
2025-08-04 23:26:59 线程1：[信息] [信息] 联系信息完成，等待页面加载... (进度: 38%)
2025-08-04 23:27:01 线程2：[信息] [信息] 进入付款信息页面... (进度: 38%)
2025-08-04 23:27:02 线程2：[信息] [信息] 正在选择月份: September (进度: 38%)
2025-08-04 23:27:02 线程2：[信息] [信息] 已选择月份（标准选项）: September (进度: 38%)
2025-08-04 23:27:02 线程1：[信息] [信息] 进入付款信息页面... (进度: 38%)
2025-08-04 23:27:03 线程2：[信息] [信息] 正在选择年份: 2031 (进度: 38%)
2025-08-04 23:27:03 线程2：[信息] [信息] 已选择年份（标准选项）: 2031 (进度: 38%)
2025-08-04 23:27:03 线程1：[信息] [信息] 正在选择月份: May (进度: 38%)
2025-08-04 23:27:04 线程1：[信息] [信息] 已选择月份（标准选项）: May (进度: 38%)
2025-08-04 23:27:04 线程2：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 38%)
2025-08-04 23:27:04 线程2：[信息] [信息] 开始填写验证码验证页面... (进度: 38%)
2025-08-04 23:27:04 线程2：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 38%)
2025-08-04 23:27:04 线程1：[信息] [信息] 正在选择年份: 2029 (进度: 38%)
2025-08-04 23:27:05 线程1：[信息] [信息] 已选择年份（标准选项）: 2029 (进度: 38%)
2025-08-04 23:27:05 线程1：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 38%)
2025-08-04 23:27:05 线程1：[信息] [信息] 开始填写验证码验证页面... (进度: 38%)
2025-08-04 23:27:05 线程1：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 38%)
2025-08-04 23:27:09 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-08-04 23:27:10 线程2：[信息] [信息] 已选择国家代码: +52 (进度: 38%)
2025-08-04 23:27:10 线程2：[信息] [信息] 已清空并重新填写手机号码: +526651497485 (进度: 38%)
2025-08-04 23:27:11 线程2：[信息] [信息] 已点击发送验证码按钮 (进度: 38%)
2025-08-04 23:27:11 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-08-04 23:27:12 线程1：[信息] [信息] 已选择国家代码: +52 (进度: 38%)
2025-08-04 23:27:12 线程1：[信息] [信息] 已清空并重新填写手机号码: +526644304714 (进度: 38%)
2025-08-04 23:27:12 线程1：[信息] [信息] 已点击发送验证码按钮 (进度: 38%)
2025-08-04 23:27:13 线程2：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 38%)
2025-08-04 23:27:13 线程2：[信息] [信息] ⚠️ 检测到错误信息，开始重试机制... (进度: 38%)
2025-08-04 23:27:13 [信息] 检测到错误信息，开始重试机制
2025-08-04 23:27:13 线程2：[信息] [信息] 🔄 第1次重试发送验证码按钮... (进度: 38%)
2025-08-04 23:27:13 [信息] 第1次重试发送验证码按钮
2025-08-04 23:27:14 线程1：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 38%)
2025-08-04 23:27:14 线程1：[信息] [信息] ⚠️ 检测到错误信息，开始重试机制... (进度: 38%)
2025-08-04 23:27:14 [信息] 检测到错误信息，开始重试机制
2025-08-04 23:27:14 线程1：[信息] [信息] 🔄 第1次重试发送验证码按钮... (进度: 38%)
2025-08-04 23:27:14 [信息] 第1次重试发送验证码按钮
2025-08-04 23:27:16 线程2：[信息] [信息] ✅ 第1次重试：已点击发送验证码按钮 (进度: 100%)
2025-08-04 23:27:16 [信息] 第1次重试：已点击发送验证码按钮
2025-08-04 23:27:17 线程1：[信息] [信息] ✅ 第1次重试：已点击发送验证码按钮 (进度: 100%)
2025-08-04 23:27:17 [信息] 第1次重试：已点击发送验证码按钮
2025-08-04 23:27:18 线程2：[信息] [信息] ✅ 第1次重试成功：错误信息消失，继续正常流程 (进度: 100%)
2025-08-04 23:27:18 [信息] 第1次重试成功：错误信息消失
2025-08-04 23:27:18 线程2：[信息] [信息] 手机号码自动模式 + 图形验证码自动模式：开始自动处理... (进度: 100%)
2025-08-04 23:27:18 线程2：[信息] [信息] 自动模式：开始处理图形验证码... (进度: 100%)
2025-08-04 23:27:18 线程2：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-04 23:27:19 线程1：[信息] [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']" (进度: 100%)
2025-08-04 23:27:19 [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']"
2025-08-04 23:27:19 线程1：[信息] [信息] ✅ 第1次重试成功：错误信息消失，继续正常流程 (进度: 100%)
2025-08-04 23:27:19 [信息] 第1次重试成功：错误信息消失
2025-08-04 23:27:19 线程1：[信息] [信息] 手机号码自动模式 + 图形验证码自动模式：开始自动处理... (进度: 100%)
2025-08-04 23:27:19 线程1：[信息] [信息] 自动模式：开始处理图形验证码... (进度: 100%)
2025-08-04 23:27:19 线程1：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-04 23:27:21 线程2：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-04 23:27:21 线程2：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 23:27:22 线程1：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-04 23:27:22 线程1：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-04 23:27:24 线程2：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 35199 字节 (进度: 100%)
2025-08-04 23:27:24 线程2：[信息] [信息] ✅ 图片验证通过：201x70px，35199字节，复杂度符合要求 (进度: 100%)
2025-08-04 23:27:24 线程2：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 23:27:25 线程2：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"hys3r2"},"taskId":"8638abc2-7147-11f0-ae6e-62c5329370b7"} (进度: 100%)
2025-08-04 23:27:25 线程2：[信息] [信息] 第六页第1次识别结果: hys3r2 → 转换为小写: hys3r2 (进度: 100%)
2025-08-04 23:27:25 线程2：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 23:27:25 线程2：[信息] [信息] 第六页已填入验证码: hys3r2 (进度: 100%)
2025-08-04 23:27:26 线程2：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 23:27:26 线程1：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 34665 字节 (进度: 100%)
2025-08-04 23:27:26 线程1：[信息] [信息] ✅ 图片验证通过：201x70px，34665字节，复杂度符合要求 (进度: 100%)
2025-08-04 23:27:26 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-04 23:27:27 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"x46ww8"},"taskId":"8710da56-7147-11f0-9768-ba03bdd70631"} (进度: 100%)
2025-08-04 23:27:27 线程1：[信息] [信息] 第六页第1次识别结果: x46ww8 → 转换为小写: x46ww8 (进度: 100%)
2025-08-04 23:27:27 线程1：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-04 23:27:27 线程1：[信息] [信息] 第六页已填入验证码: x46ww8 (进度: 100%)
2025-08-04 23:27:27 线程1：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-04 23:27:29 线程2：[信息] [信息] 第1次图形验证码识别成功 (进度: 100%)
2025-08-04 23:27:29 线程2：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-08-04 23:27:30 线程1：[信息] [信息] 第1次图形验证码识别成功 (进度: 100%)
2025-08-04 23:27:30 线程1：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-08-04 23:27:32 线程2：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-08-04 23:27:33 线程1：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-08-04 23:27:35 线程2：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-04 23:27:35 线程2：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 23:27:35 线程2：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 23:27:35 线程2：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-08-04 23:27:36 线程1：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-04 23:27:36 线程1：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 23:27:36 线程1：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-04 23:27:36 线程1：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-08-04 23:27:40 线程2：[信息] [信息] 线程2开始独立获取验证码... (进度: 100%)
2025-08-04 23:27:40 线程2：[信息] [信息] 线程2开始获取验证码，最多尝试8次，每次间隔8秒 (进度: 100%)
2025-08-04 23:27:40 线程2：[信息] [信息] 线程2第1次尝试获取验证码...（剩余7次尝试） (进度: 100%)
2025-08-04 23:27:40 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-04 23:27:41 线程2：[信息] [信息] 线程2验证码获取成功: 5393 (进度: 100%)
2025-08-04 23:27:41 [信息] 线程2手机号码已加入释放队列: +526651497485 (原因: 获取验证码成功)
2025-08-04 23:27:41 线程2：[信息] [信息] 线程2验证码获取成功: 5393，立即填入验证码... (进度: 100%)
2025-08-04 23:27:41 线程2：[信息] [信息] 第七页：找到验证码输入框 (Verify code) (进度: 100%)
2025-08-04 23:27:41 线程1：[信息] [信息] 线程1开始独立获取验证码... (进度: 100%)
2025-08-04 23:27:41 线程1：[信息] [信息] 线程1开始获取验证码，最多尝试8次，每次间隔8秒 (进度: 100%)
2025-08-04 23:27:41 线程1：[信息] [信息] 线程1第1次尝试获取验证码...（剩余7次尝试） (进度: 100%)
2025-08-04 23:27:41 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-04 23:27:41 线程2：[信息] [信息] 线程2已自动填入手机验证码: 5393 (进度: 100%)
2025-08-04 23:27:42 线程1：[信息] [信息] 线程1验证码获取成功: 0779 (进度: 100%)
2025-08-04 23:27:42 [信息] 线程1手机号码已加入释放队列: +526644304714 (原因: 获取验证码成功)
2025-08-04 23:27:42 线程1：[信息] [信息] 线程1验证码获取成功: 0779，立即填入验证码... (进度: 100%)
2025-08-04 23:27:42 线程1：[信息] [信息] 第七页：找到验证码输入框 (Verify code) (进度: 100%)
2025-08-04 23:27:42 线程1：[信息] [信息] 线程1已自动填入手机验证码: 0779 (进度: 100%)
2025-08-04 23:27:42 线程2：[信息] [信息] 线程2正在自动点击Continue按钮... (进度: 100%)
2025-08-04 23:27:43 线程2：[信息] [信息] 线程2手机验证码验证完成，继续执行后续步骤... (进度: 100%)
2025-08-04 23:27:43 线程1：[信息] [信息] 线程1正在自动点击Continue按钮... (进度: 100%)
2025-08-04 23:27:43 线程1：[信息] [信息] 线程1手机验证码验证完成，继续执行后续步骤... (进度: 100%)
2025-08-04 23:27:46 线程2：[信息] [信息] 检测到无资格错误提示: You are not eligible for new customer credits (进度: 100%)
2025-08-04 23:27:46 线程2：[信息] [信息] 线程2检测到卡号已被关联错误，注册失败 (进度: 100%)
2025-08-04 23:27:46 线程2：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：pS50Kg77A7a ③AWS密码：aE8YgYOB ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联 (进度: 100%)
2025-08-04 23:27:46 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：pS50Kg77A7a ③AWS密码：aE8YgYOB ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-04 23:27:46 线程2：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：pS50Kg77A7a ③AWS密码：aE8YgYOB ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-04 23:27:46 [信息] 线程2请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：pS50Kg77A7a ③AWS密码：aE8YgYOB ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-04 23:27:46 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：pS50Kg77A7a ③AWS密码：aE8YgYOB ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-04 23:27:46 [信息] 线程2剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：pS50Kg77A7a ③AWS密码：aE8YgYOB ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-04 23:27:46 线程2：[信息] 收到失败数据保存请求: 卡号已被关联, 数据: <EMAIL>
2025-08-04 23:27:46 [信息] 线程2请求保存失败数据: 卡号已被关联, 数据: <EMAIL>
2025-08-04 23:27:46 [信息] 已处理失败数据: <EMAIL>, 失败原因: 卡号已被关联
2025-08-04 23:27:46 [信息] 线程2失败数据已保存: 卡号已被关联, 数据: <EMAIL>
2025-08-04 23:27:46 线程2：[信息] [信息] 已通知保存失败数据，失败原因: 卡号已被关联 (进度: 0%)
2025-08-04 23:27:46 线程2：[信息] [信息] 线程2注册失败，数据已归类，注册流程终止 (进度: 0%)
2025-08-04 23:27:46 线程1：[信息] [信息] 验证码验证完成，等待页面加载... (进度: 100%)
2025-08-04 23:27:46 线程1：[信息] [信息] 已点击完成注册按钮，正在处理... (进度: 100%)
2025-08-04 23:27:47 线程1：[信息] [信息] 自动模式验证完成，API手机号码已加入黑名单: 加入黑名单成功 (进度: 100%)
2025-08-04 23:27:47 线程1：[信息] [信息] 等待完成注册页面加载... (进度: 100%)
2025-08-04 23:27:50 线程1：[信息] [信息] 完成注册页面加载完成 (进度: 100%)
2025-08-04 23:27:50 线程1：[信息] [信息] 🔍 正在查找'Go to the AWS Management Console'链接... (进度: 100%)
2025-08-04 23:27:54 线程1：[信息] [信息] 🖱️ 已点击跳转管理控制台链接 (进度: 100%)
2025-08-04 23:27:54 线程1：[信息] [信息] 🔗 通过导航进入IAM安全凭证页面... (进度: 100%)
2025-08-04 23:27:54 线程1：[信息] [信息] ⏳ 等待管理控制台页面加载，智能检测'更多'按钮（超时20秒）... (进度: 100%)
2025-08-04 23:28:08 线程1：[信息] [信息] ✅ 找到'更多'按钮，页面加载完成 (进度: 100%)
2025-08-04 23:28:08 线程1：[信息] [信息] 🖱️ 正在点击更多按钮... (进度: 100%)
2025-08-04 23:28:11 [信息] 定时检查发现2个待释放手机号码，开始批量释放
2025-08-04 23:28:11 [信息] 开始释放2个手机号码
2025-08-04 23:28:11 [信息] [手机API] 开始批量释放2个手机号码
2025-08-04 23:28:11 [信息] [手机API] 释放手机号码: +526651497485
2025-08-04 23:28:13 [信息] [手机API] 手机号码释放成功: +526651497485
2025-08-04 23:28:13 线程1：[信息] [信息] ✅ 成功点击更多按钮 (进度: 100%)
2025-08-04 23:28:13 [信息] 成功点击更多按钮
2025-08-04 23:28:14 [信息] [手机API] 释放手机号码: +526644304714
2025-08-04 23:28:15 [信息] [手机API] 手机号码释放成功: +526644304714
2025-08-04 23:28:15 线程1：[信息] [信息] 🖱️ 正在点击账户信息按钮... (进度: 100%)
2025-08-04 23:28:16 [信息] [手机API] 批量释放完成: 成功2个, 失败0个
2025-08-04 23:28:16 [信息] 定时批量释放完成: 批量释放完成: 成功2个, 失败0个
2025-08-04 23:28:16 线程1：[信息] [信息] ✅ 成功点击账户信息按钮 (进度: 100%)
2025-08-04 23:28:16 [信息] 成功点击账户信息按钮
2025-08-04 23:28:17 线程1：[信息] [信息] 🖱️ 正在点击'安全凭证'菜单项... (进度: 100%)
2025-08-04 23:28:17 线程1：[信息] [信息] 🔍 正在定位'安全凭证'链接... (进度: 100%)
2025-08-04 23:28:17 线程1：[信息] [信息] ✅ 成功定位到'安全凭证'链接 (进度: 100%)
2025-08-04 23:28:17 [信息] 成功定位到'安全凭证'链接
2025-08-04 23:28:22 线程1：[信息] [信息] ✅ 成功点击'安全凭证'菜单项 (进度: 100%)
2025-08-04 23:28:22 [信息] 成功点击'安全凭证'链接
2025-08-04 23:28:22 线程1：[信息] [信息] ⏳ 等待IAM安全凭证页面加载（20秒超时，循环检测账单问题和创建密钥按钮）... (进度: 100%)
2025-08-04 23:28:38 线程1：[信息] [信息] ✅ 找到'创建访问密钥'按钮，页面加载完成 (进度: 100%)
2025-08-04 23:28:38 线程1：[信息] [信息] ✅ 页面状态正常，找到创建密钥按钮，开始密钥提取流程 (进度: 100%)
2025-08-04 23:28:38 线程1：[信息] [信息] 🔍 设置页面缩放为50%... (进度: 100%)
2025-08-04 23:28:39 线程1：[信息] [信息] ✅ 页面缩放设置完成 (进度: 100%)
2025-08-04 23:28:39 [信息] 页面缩放设置为50%完成
2025-08-04 23:28:39 线程1：[信息] [信息] 🔍 正在查找并点击'下一步'按钮关闭向导... (进度: 100%)
2025-08-04 23:28:39 [信息] 开始查找并点击'下一步'按钮关闭向导
2025-08-04 23:28:39 线程1：[信息] [信息] ℹ️ 未检测到向导元素，向导可能已自动关闭 (进度: 100%)
2025-08-04 23:28:39 [信息] 未检测到向导元素，向导可能已自动关闭
2025-08-04 23:28:39 线程1：[信息] [信息] 🔑 第四阶段：开始创建和复制访问密钥 (进度: 100%)
2025-08-04 23:28:39 [信息] 开始创建和复制访问密钥
2025-08-04 23:28:39 线程1：[信息] [信息] 🔑 正在点击'创建访问密钥'按钮... (进度: 100%)
2025-08-04 23:28:39 线程1：[信息] [信息] 🔍 正在定位'创建访问密钥'按钮... (进度: 100%)
2025-08-04 23:28:39 线程1：[信息] [信息] ✅ 成功定位到'创建访问密钥'按钮 (进度: 100%)
2025-08-04 23:28:39 [信息] 成功定位到'创建访问密钥'按钮
2025-08-04 23:28:40 线程1：[信息] [信息] ✅ 成功点击'创建访问密钥'按钮 (进度: 100%)
2025-08-04 23:28:40 [信息] 成功点击'创建访问密钥'按钮
2025-08-04 23:28:42 线程1：[信息] [信息] ☑️ 正在勾选确认复选框... (进度: 100%)
2025-08-04 23:28:44 线程1：[信息] [信息] ✅ 使用id属性定位到确认复选框 (进度: 100%)
2025-08-04 23:28:44 [信息] 使用id属性定位到确认复选框
2025-08-04 23:28:44 线程1：[信息] [信息] ✅ 成功勾选确认复选框 (进度: 100%)
2025-08-04 23:28:44 [信息] 成功勾选确认复选框
2025-08-04 23:28:45 线程1：[信息] [信息] 🔑 正在点击'创建访问密钥'确认按钮... (进度: 100%)
2025-08-04 23:28:45 线程1：[信息] [信息] ✅ 成功点击'创建访问密钥'确认按钮 (进度: 100%)
2025-08-04 23:28:45 [信息] 成功点击'创建访问密钥'确认按钮
2025-08-04 23:28:48 线程1：[信息] [信息] 📋 开始复制访问密钥... (进度: 100%)
2025-08-04 23:28:48 [信息] 开始复制访问密钥
2025-08-04 23:28:50 线程1：[信息] [信息] 🔍 方法2找到 2 个单元格 (进度: 100%)
2025-08-04 23:28:50 [信息] 方法2找到 2 个单元格
2025-08-04 23:28:51 线程1：[信息] [信息] 🔍 总共找到 2 个单元格，开始分析... (进度: 100%)
2025-08-04 23:28:51 [信息] 总共找到 2 个单元格，开始分析
2025-08-04 23:28:51 [信息] 单元格[0]: 'AKIAUHY6YTMXZMXSL2HB'
2025-08-04 23:28:51 [信息] 单元格[1]: '***************Mostrar'
2025-08-04 23:28:51 线程1：[信息] [信息] 📋 正在查找并复制访问密钥... (进度: 100%)
2025-08-04 23:28:51 线程1：[信息] [信息] ✅ 找到访问密钥: AKIAUHY6YTMXZMXSL2HB (进度: 100%)
2025-08-04 23:28:51 [信息] 找到访问密钥: AKIAUHY6YTMXZMXSL2HB
2025-08-04 23:28:59 线程1：[信息] [信息] ✅ 方法1成功点击访问密钥复制按钮 (进度: 100%)
2025-08-04 23:28:59 [信息] 方法1成功点击访问密钥复制按钮
2025-08-04 23:29:00 线程1：[信息] [信息] 📋 正在查找并复制秘密访问密钥... (进度: 100%)
2025-08-04 23:29:00 线程1：[信息] [信息] ✅ 找到秘密访问密钥: ***************Mostrar (进度: 100%)
2025-08-04 23:29:00 [信息] 找到秘密访问密钥: ***************Mostrar
2025-08-04 23:29:00 线程1：[信息] [信息] 🔍 正在寻找向导阻挡... (进度: 100%)
2025-08-04 23:29:00 线程1：[信息] [信息] 🔍 检测到隐藏的秘密访问密钥，尝试点击显示按钮... (进度: 100%)
2025-08-04 23:29:00 [信息] 检测到隐藏的秘密访问密钥，尝试点击显示按钮
2025-08-04 23:29:00 线程1：[信息] [信息] ✅ 使用TestId定位到显示按钮 (进度: 100%)
2025-08-04 23:29:00 [信息] 使用TestId定位到显示按钮
2025-08-04 23:29:01 线程1：[信息] [信息] ✅ 显示按钮点击成功，新文本: RsQA6wzZs/uUAsuzq11yQ9jxawCh8gjc+fm6MAbnOcultar (进度: 100%)
2025-08-04 23:29:01 [信息] 显示按钮点击成功，新文本: RsQA6wzZs/uUAsuzq11yQ9jxawCh8gjc+fm6MAbnOcultar
2025-08-04 23:29:01 线程1：[信息] [信息] ✅ 直接从显示文本提取秘密访问密钥: RsQA6wzZs/uUAsuzq11yQ9jxawCh8gjc+fm6MAbnOcultar (进度: 100%)
2025-08-04 23:29:01 [信息] 直接从显示文本提取秘密访问密钥: RsQA6wzZs/uUAsuzq11yQ9jxawCh8gjc+fm6MAbnOcultar
2025-08-04 23:29:01 线程1：[信息] [信息] ✅ 访问密钥复制完成 (进度: 100%)
2025-08-04 23:29:01 [信息] 访问密钥复制完成 - AccessKey: AKIAUHY6YTMXZMXSL2HB, SecretKey: RsQA6wzZs/uUAsuzq11yQ9jxawCh8gjc+fm6MAbnOcultar
2025-08-04 23:29:02 线程1：[信息] [信息] 🖱️ 正在点击'已完成'按钮... (进度: 100%)
2025-08-04 23:29:03 线程1：[信息] [信息] ✅ 成功点击'已完成'按钮 (进度: 100%)
2025-08-04 23:29:03 [信息] 成功点击'已完成'按钮
2025-08-04 23:29:03 线程1：[信息] [信息] ✅ 密钥已保存到数据对象 - AccessKey: AKIAUHY6YTMXZMXSL2HB, SecretKey: RsQA6wzZs/uUAsuzq11yQ9jxawCh8gjc+fm6MAbnOcultar (进度: 100%)
2025-08-04 23:29:03 [信息] 密钥已保存到数据对象 - AccessKey: AKIAUHY6YTMXZMXSL2HB, SecretKey: RsQA6wzZs/uUAsuzq11yQ9jxawCh8gjc+fm6MAbnOcultar
2025-08-04 23:29:04 线程1：[信息] [信息] 🖱️ 正在点击'继续'按钮... (进度: 100%)
2025-08-04 23:29:07 线程1：[信息] [信息] ❌ 点击'继续'按钮失败: Timeout 3000ms exceeded.
Call log:
  - waiting for Locator("button[data-awsui-analytics*='navigate']") to be visible (进度: 100%)
2025-08-04 23:29:07 [信息] 点击'继续'按钮失败: Timeout 3000ms exceeded.
Call log:
  - waiting for Locator("button[data-awsui-analytics*='navigate']") to be visible
2025-08-04 23:29:07 线程1：[信息] [信息] 🔐 开始设置MFA设备... (进度: 100%)
2025-08-04 23:29:07 [信息] 开始设置MFA设备
2025-08-04 23:29:07 线程1：[信息] [信息] 🖱️ 正在点击'分配 MFA 设备'按钮... (进度: 100%)
2025-08-04 23:29:07 线程1：[信息] [信息] ✅ 成功点击'分配 MFA 设备'按钮 (进度: 100%)
2025-08-04 23:29:07 [信息] 成功点击'分配 MFA 设备'按钮
2025-08-04 23:29:07 线程1：[信息] [信息] ⏳ 等待MFA设备设置页面加载（20秒超时）... (进度: 100%)
2025-08-04 23:29:28 线程1：[信息] [信息] ⚠️ 20秒内未找到'设备名称'输入框，需要手动处理 (进度: 100%)
2025-08-04 23:29:28 线程1：[信息] [信息] ⚠️ 设备名称页面加载超时，需要手动处理 (进度: 100%)
2025-08-04 23:29:28 [信息] 设备名称页面加载超时，需要手动处理
2025-08-04 23:29:28 线程1：[信息] [信息] 页面加载完成后，手动点击继续注册 (进度: 100%)
2025-08-04 23:29:28 线程1：[信息] [信息] MFA页面需要手动处理，等待用户操作 (进度: 100%)
2025-08-04 23:29:47 [信息] 获取线程1当前数据: <EMAIL>
2025-08-04 23:29:47 线程1：[信息] 终止时正在处理的数据: <EMAIL>
2025-08-04 23:29:47 线程1：[信息] 数据详情: <EMAIL>|TWaz6Wt9|Escobar Lesly|Colbun|Av vicua mackenna 881 1302 b|Region Metropolitana|Santiago|8320000|4757749017467456|05|29|490|Escobar Lesly|1Vr3Z1tm023L|CL
2025-08-04 23:29:47 线程1：[信息] 多线程终止 - 复制密钥信息到剪贴板
2025-08-04 23:29:47 线程1：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：1Vr3Z1tm023L ③AWS密码：TWaz6Wt9 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止 (进度: 100%)
2025-08-04 23:29:47 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：1Vr3Z1tm023L ③AWS密码：TWaz6Wt9 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 23:29:47 线程1：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：1Vr3Z1tm023L ③AWS密码：TWaz6Wt9 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 23:29:47 [信息] 线程1请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：1Vr3Z1tm023L ③AWS密码：TWaz6Wt9 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 23:29:47 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：1Vr3Z1tm023L ③AWS密码：TWaz6Wt9 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 23:29:47 [信息] 线程1剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：1Vr3Z1tm023L ③AWS密码：TWaz6Wt9 ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-04 23:29:47 线程1：[信息] [信息] 注册已终止 (进度: 100%)
2025-08-04 23:29:47 线程1：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-08-04 23:29:47 线程1：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：1Vr3Z1tm023L ③AWS密码：TWaz6Wt9 ④访问密钥：AKIAUHY6YTMXZMXSL2HB ⑤秘密访问密钥：RsQA6wzZs/uUAsuzq11yQ9jxawCh8gjc+fm6MAbnOcultar ⑥MFA信息：   //手动终止 (进度: 100%)
2025-08-04 23:29:47 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：1Vr3Z1tm023L ③AWS密码：TWaz6Wt9 ④访问密钥：AKIAUHY6YTMXZMXSL2HB ⑤秘密访问密钥：RsQA6wzZs/uUAsuzq11yQ9jxawCh8gjc+fm6MAbnOcultar ⑥MFA信息：   //手动终止
2025-08-04 23:29:47 线程1：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：1Vr3Z1tm023L ③AWS密码：TWaz6Wt9 ④访问密钥：AKIAUHY6YTMXZMXSL2HB ⑤秘密访问密钥：RsQA6wzZs/uUAsuzq11yQ9jxawCh8gjc+fm6MAbnOcultar ⑥MFA信息：   //手动终止
2025-08-04 23:29:47 [信息] 线程1请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：1Vr3Z1tm023L ③AWS密码：TWaz6Wt9 ④访问密钥：AKIAUHY6YTMXZMXSL2HB ⑤秘密访问密钥：RsQA6wzZs/uUAsuzq11yQ9jxawCh8gjc+fm6MAbnOcultar ⑥MFA信息：   //手动终止
2025-08-04 23:29:47 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：1Vr3Z1tm023L ③AWS密码：TWaz6Wt9 ④访问密钥：AKIAUHY6YTMXZMXSL2HB ⑤秘密访问密钥：RsQA6wzZs/uUAsuzq11yQ9jxawCh8gjc+fm6MAbnOcultar ⑥MFA信息：   //手动终止
2025-08-04 23:29:47 [信息] 线程1剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：1Vr3Z1tm023L ③AWS密码：TWaz6Wt9 ④访问密钥：AKIAUHY6YTMXZMXSL2HB ⑤秘密访问密钥：RsQA6wzZs/uUAsuzq11yQ9jxawCh8gjc+fm6MAbnOcultar ⑥MFA信息：   //手动终止
2025-08-04 23:29:47 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-08-04 23:29:47 线程1：[信息] 临时目录已清理: C:\Users\<USER>\AppData\Local\Temp\AWS_Thread_1_20250804_232511
2025-08-04 23:29:47 线程1：[信息] 已终止
2025-08-04 23:29:47 [信息] 线程1已终止
2025-08-04 23:29:47 [信息] 开始处理线程1终止数据，共1个数据
2025-08-04 23:29:47 [信息] 处理线程1终止数据: <EMAIL>
2025-08-04 23:29:47 [信息] 从注册数据列表中移除: <EMAIL>
2025-08-04 23:29:47 [信息] 线程1终止 - 数据已移动到终止列表: <EMAIL>
2025-08-04 23:29:47 [信息] 线程1终止数据处理完成，成功移动1个数据
2025-08-04 23:29:47 [信息] UniformGrid列数已更新为: 1
2025-08-04 23:29:47 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-08-04 23:29:47 [信息] 线程1已终止
2025-08-04 23:30:22 [信息] 多线程窗口引用已清理
2025-08-04 23:30:22 [信息] 主窗口已恢复正常状态，多线程窗口引用已清理
2025-08-04 23:30:22 [信息] 多线程管理窗口正在关闭
2025-08-04 23:30:25 [信息] 程序正在退出，开始清理工作...
2025-08-04 23:30:25 [信息] 程序退出，开始清理所有待拉黑千川手机号码
2025-08-04 23:30:25 [信息] 程序退出清理完成: 没有需要拉黑的千川手机号码
2025-08-04 23:30:25 [信息] 程序退出，开始清理所有待释放手机号码
2025-08-04 23:30:25 [信息] 程序退出清理完成: 没有需要释放的手机号码
2025-08-04 23:30:25 [信息] 程序退出清理工作完成
2025-08-04 23:30:29 [信息] AWS自动注册工具启动
2025-08-04 23:30:29 [信息] 程序版本: 1.0.0.0
2025-08-04 23:30:29 [信息] 启动时间: 2025-08-04 23:30:29
2025-08-04 23:30:29 [信息] 浏览器模式切换: 无痕 Chrome 模式
2025-08-04 23:30:29 [信息] 线程数量已选择: 1
2025-08-04 23:30:29 [信息] 线程数量选择初始化完成
2025-08-04 23:30:29 [信息] 程序初始化完成
2025-08-04 23:30:31 [按钮操作] 选择文件 -> 打开文件选择对话框
2025-08-04 23:30:33 [信息] 已选择文件: C:\Users\<USER>\Desktop\2025-08-04-智利.txt
2025-08-04 23:30:34 [按钮操作] 加载信息 -> 加载数据文件: C:\Users\<USER>\Desktop\2025-08-04-智利.txt
2025-08-04 23:30:34 [信息] 成功加载 1 条数据
2025-08-04 23:30:36 [按钮操作] 开始注册 -> 启动注册流程
2025-08-04 23:30:36 [系统状态] 正在检测Chrome浏览器(无痕模式)...
2025-08-04 23:30:37 [系统状态] 使用默认浏览器语言: English (United States)
2025-08-04 23:30:37 [信息] 浏览器语言设置: 使用默认语言 en-US
2025-08-04 23:30:37 [信息] 单线程模式窗口布局: 使用固定位置(0, 0), 大小(600x400)
2025-08-04 23:30:37 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36
2025-08-04 23:30:37 [系统状态] 尝试启动系统Chrome(无痕模式)...
2025-08-04 23:30:38 [系统状态] 创建无痕模式上下文...
2025-08-04 23:30:40 [系统状态] 使用默认时区: America/New_York
2025-08-04 23:30:40 [信息] 浏览器时区设置: 使用默认时区 America/New_York
2025-08-04 23:30:40 [系统状态] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_008, CPU: 20核
2025-08-04 23:30:40 [信息] 浏览器指纹注入: Canvas=canvas_fp_008, WebGL=ANGLE (AMD Radeon RX 7600 Direct3D11 vs_5_0 ps_5_0), CPU=20核, RAM=24 GB
2025-08-04 23:30:41 [系统状态] 已创建新的无痕模式上下文和页面
2025-08-04 23:30:43 [系统状态] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 20
   • 设备内存: 24 GB
   • 平台信息: Win32
   • Do Not Track: user

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (AMD Radeon RX 7600 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (Intel Corporation)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 1E2F3A4B
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_004
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-M0N1O2P
   • MAC地址: 56-78-9A-BC-DE-F0
   • 屏幕分辨率: 1758x986
   • 可用区域: 1758x946

🌍 地区语言信息:
   • 主语言: zh-CN
   • 语言列表: zh-CN,en-US
   • 时区偏移: 300分钟

🔧 高级功能信息:
   • ClientRects ID: C1D2E3F4
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: satellite
   • 电池API支持: True
   • 电池电量: 0.33
   • 电池充电状态: False
   • 性能API支持: True
🔍 ================================
2025-08-04 23:30:43 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 20    • 设备内存: 24 GB    • 平台信息: Win32    • Do Not Track: user   图形渲染信息:    • WebGL渲染器: ANGLE (AMD Radeon RX 7600 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (Intel Corporation)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 1E2F3A4B    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_004    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-M0N1O2P    • MAC地址: 56-78-9A-BC-DE-F0    • 屏幕分辨率: 1758x986    • 可用区域: 1758x946   地区语言信息:    • 主语言: zh-CN    • 语言列表: zh-CN,en-US    • 时区偏移: 300分钟   高级功能信息:    • ClientRects ID: C1D2E3F4    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: satellite    • 电池API支持: True    • 电池电量: 0.33    • 电池充电状态: False    • 性能API支持: True  ================================
2025-08-04 23:30:43 [系统状态] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用

2025-08-04 23:30:43 [注册开始] 邮箱: <EMAIL>, 索引: 1/1
2025-08-04 23:30:43 [系统状态] 已清空AWS密钥变量和MFA变量
2025-08-04 23:30:43 [系统状态] 开始新的注册: <EMAIL>，已清空之前的号码状态
2025-08-04 23:30:43 [系统状态] 在现有窗口中新建标签页...
2025-08-04 23:30:43 [系统状态] 使用现有的浏览器上下文
2025-08-04 23:30:43 [系统状态] 正在新建标签页...
2025-08-04 23:30:43 [系统状态] 已设置页面视口大小为600x400
2025-08-04 23:30:43 [系统状态] 验证无痕Chrome模式状态...
2025-08-04 23:30:43 [系统状态] 无痕Chrome检测: ✓ 已启用
2025-08-04 23:30:43 [系统状态] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb...
2025-08-04 23:30:43 [系统状态] 正在打开AWS注册页面...
2025-08-04 23:31:13 [系统状态] 注册失败: Timeout 30000ms exceeded.
Call log:
  - navigating to "https://portal.aws.amazon.com/gp/aws/developer/registration/index.html?refid=em_127222&p=free&c=hp&z=1", waiting until "load"
2025-08-04 23:31:13 [错误] 注册过程出现错误 - 邮箱: <EMAIL>
2025-08-04 23:31:13 [系统状态] 第一页相关失败，数据保持不动
2025-08-04 23:31:13 [信息] 第一页相关失败，数据保持不动
2025-08-04 23:31:22 [信息] 程序正在退出，开始清理工作...
2025-08-04 23:31:22 [信息] 程序退出，开始清理所有待拉黑千川手机号码
2025-08-04 23:31:22 [信息] 程序退出清理完成: 没有需要拉黑的千川手机号码
2025-08-04 23:31:22 [信息] 程序退出，开始清理所有待释放手机号码
2025-08-04 23:31:22 [信息] 程序退出清理完成: 没有需要释放的手机号码
2025-08-04 23:31:22 [信息] 程序退出清理工作完成
2025-08-04 23:31:26 [信息] AWS自动注册工具启动
2025-08-04 23:31:26 [信息] 程序版本: 1.0.0.0
2025-08-04 23:31:26 [信息] 启动时间: 2025-08-04 23:31:26
2025-08-04 23:31:26 [信息] 浏览器模式切换: 无痕 Chrome 模式
2025-08-04 23:31:26 [信息] 线程数量已选择: 1
2025-08-04 23:31:26 [信息] 线程数量选择初始化完成
2025-08-04 23:31:26 [信息] 程序初始化完成
2025-08-04 23:31:27 [按钮操作] 选择文件 -> 打开文件选择对话框
2025-08-04 23:31:29 [信息] 已选择文件: C:\Users\<USER>\Desktop\2025-08-04-智利.txt
2025-08-04 23:31:30 [按钮操作] 加载信息 -> 加载数据文件: C:\Users\<USER>\Desktop\2025-08-04-智利.txt
2025-08-04 23:31:30 [信息] 成功加载 1 条数据
2025-08-04 23:31:33 [按钮操作] 开始注册 -> 启动注册流程
2025-08-04 23:31:33 [系统状态] 正在检测Chrome浏览器(无痕模式)...
2025-08-04 23:31:34 [系统状态] 使用默认浏览器语言: English (United States)
2025-08-04 23:31:34 [信息] 浏览器语言设置: 使用默认语言 en-US
2025-08-04 23:31:34 [信息] 单线程模式窗口布局: 使用固定位置(0, 0), 大小(600x400)
2025-08-04 23:31:34 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0
2025-08-04 23:31:34 [系统状态] 尝试启动系统Chrome(无痕模式)...
2025-08-04 23:31:35 [系统状态] 创建无痕模式上下文...
2025-08-04 23:31:37 [系统状态] 使用默认时区: America/New_York
2025-08-04 23:31:37 [信息] 浏览器时区设置: 使用默认时区 America/New_York
2025-08-04 23:31:38 [系统状态] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_005, CPU: 32核
2025-08-04 23:31:38 [信息] 浏览器指纹注入: Canvas=canvas_fp_005, WebGL=ANGLE (NVIDIA GeForce GTX 1070 Direct3D11 vs_5_0 ps_5_0), CPU=32核, RAM=16 GB
2025-08-04 23:31:38 [系统状态] 已创建新的无痕模式上下文和页面
2025-08-04 23:31:40 [系统状态] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 32
   • 设备内存: 16 GB
   • 平台信息: Win32
   • Do Not Track: unspecified

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (NVIDIA GeForce GTX 1070 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (AMD Inc.)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 0426959D
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_012
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-87EL3RX
   • MAC地址: A1-B2-C3-D4-E5-F6
   • 屏幕分辨率: 1819x1089
   • 可用区域: 1819x1049

🌍 地区语言信息:
   • 主语言: zh-CN
   • 语言列表: zh-CN,en-US
   • 时区偏移: 300分钟

🔧 高级功能信息:
   • ClientRects ID: C9D0E1F2
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: bluetooth
   • 电池API支持: True
   • 电池电量: 0.89
   • 电池充电状态: False
   • 性能API支持: True
🔍 ================================
2025-08-04 23:31:40 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 32    • 设备内存: 16 GB    • 平台信息: Win32    • Do Not Track: unspecified   图形渲染信息:    • WebGL渲染器: ANGLE (NVIDIA GeForce GTX 1070 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (AMD Inc.)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 0426959D    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_012    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-87EL3RX    • MAC地址: A1-B2-C3-D4-E5-F6    • 屏幕分辨率: 1819x1089    • 可用区域: 1819x1049   地区语言信息:    • 主语言: zh-CN    • 语言列表: zh-CN,en-US    • 时区偏移: 300分钟   高级功能信息:    • ClientRects ID: C9D0E1F2    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: bluetooth    • 电池API支持: True    • 电池电量: 0.89    • 电池充电状态: False    • 性能API支持: True  ================================
2025-08-04 23:31:40 [系统状态] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用

2025-08-04 23:31:40 [注册开始] 邮箱: <EMAIL>, 索引: 1/1
2025-08-04 23:31:40 [系统状态] 已清空AWS密钥变量和MFA变量
2025-08-04 23:31:40 [系统状态] 开始新的注册: <EMAIL>，已清空之前的号码状态
2025-08-04 23:31:40 [系统状态] 在现有窗口中新建标签页...
2025-08-04 23:31:40 [系统状态] 使用现有的浏览器上下文
2025-08-04 23:31:40 [系统状态] 正在新建标签页...
2025-08-04 23:31:40 [系统状态] 已设置页面视口大小为600x400
2025-08-04 23:31:40 [系统状态] 验证无痕Chrome模式状态...
2025-08-04 23:31:40 [系统状态] 无痕Chrome检测: ✓ 已启用
2025-08-04 23:31:40 [系统状态] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0...
2025-08-04 23:31:40 [系统状态] 正在打开AWS注册页面...
2025-08-04 23:32:07 [信息] 程序正在退出，开始清理工作...
2025-08-04 23:32:07 [信息] 程序退出，开始清理所有待拉黑千川手机号码
2025-08-04 23:32:07 [信息] 程序退出清理完成: 没有需要拉黑的千川手机号码
2025-08-04 23:32:07 [信息] 程序退出，开始清理所有待释放手机号码
2025-08-04 23:32:07 [信息] 程序退出清理完成: 没有需要释放的手机号码
2025-08-04 23:32:07 [信息] 程序退出清理工作完成
2025-08-04 23:32:22 [信息] AWS自动注册工具启动
2025-08-04 23:32:22 [信息] 程序版本: 1.0.0.0
2025-08-04 23:32:22 [信息] 启动时间: 2025-08-04 23:32:22
2025-08-04 23:32:22 [信息] 浏览器模式切换: 无痕 Chrome 模式
2025-08-04 23:32:22 [信息] 线程数量已选择: 1
2025-08-04 23:32:22 [信息] 线程数量选择初始化完成
2025-08-04 23:32:22 [信息] 程序初始化完成
2025-08-04 23:32:24 [按钮操作] 选择文件 -> 打开文件选择对话框
2025-08-04 23:32:26 [信息] 已选择文件: C:\Users\<USER>\Desktop\2025-08-04-智利.txt
2025-08-04 23:32:26 [按钮操作] 加载信息 -> 加载数据文件: C:\Users\<USER>\Desktop\2025-08-04-智利.txt
2025-08-04 23:32:27 [信息] 成功加载 1 条数据
2025-08-04 23:32:27 [按钮操作] 加载信息 -> 加载数据文件: C:\Users\<USER>\Desktop\2025-08-04-智利.txt
2025-08-04 23:32:27 [信息] 成功加载 1 条数据
2025-08-04 23:32:28 [按钮操作] 开始注册 -> 启动注册流程
2025-08-04 23:32:28 [系统状态] 正在检测Chrome浏览器(无痕模式)...
2025-08-04 23:32:29 [系统状态] 使用默认浏览器语言: English (United States)
2025-08-04 23:32:29 [信息] 浏览器语言设置: 使用默认语言 en-US
2025-08-04 23:32:29 [信息] 单线程模式窗口布局: 使用固定位置(0, 0), 大小(600x400)
2025-08-04 23:32:29 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-08-04 23:32:29 [系统状态] 尝试启动系统Chrome(无痕模式)...
2025-08-04 23:32:30 [系统状态] 创建无痕模式上下文...
2025-08-04 23:32:32 [系统状态] 使用默认时区: America/New_York
2025-08-04 23:32:32 [信息] 浏览器时区设置: 使用默认时区 America/New_York
2025-08-04 23:32:32 [系统状态] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_008, CPU: 18核
2025-08-04 23:32:32 [信息] 浏览器指纹注入: Canvas=canvas_fp_008, WebGL=ANGLE (AMD Radeon RX 7600 Direct3D11 vs_5_0 ps_5_0), CPU=18核, RAM=12 GB
2025-08-04 23:32:33 [系统状态] 已创建新的无痕模式上下文和页面
2025-08-04 23:32:35 [系统状态] 
🔍 ===== 浏览器指纹详细信息 =====
📊 基础硬件信息:
   • CPU核心数: 18
   • 设备内存: 12 GB
   • 平台信息: Win32
   • Do Not Track: manual

🎨 图形渲染信息:
   • WebGL渲染器: ANGLE (AMD Radeon RX 7600 Direct3D11 vs_5_0 ps_5_0)
   • WebGL厂商: Google Inc. (Intel Corp.)
   • Canvas指纹: ...1QAAAABJRUcanvas_f==
   • Canvas自定义ID: True

🔊 音频媒体信息:
   • AudioContext ID: 1A2B3C4D
   • 媒体设备支持: True
   • 媒体设备数量: 3
   • 语音合成ID: noise_011
   • 语音数量: 3

📱 设备环境信息:
   • 设备名称: DESKTOP-87EL3RX
   • MAC地址: 78-9A-BC-DE-F0-12
   • 屏幕分辨率: 1816x1047
   • 可用区域: 1816x1007

🌍 地区语言信息:
   • 主语言: zh-CN
   • 语言列表: zh-CN,en-US
   • 时区偏移: 300分钟

🔧 高级功能信息:
   • ClientRects ID: E7F8A9B0
   • WebGPU支持: True
   • WebGPU适配器: True
   • 插件数量: 0
   • MIME类型数量: 0
   • 网络连接类型: wimax
   • 电池API支持: True
   • 电池电量: 0.82
   • 电池充电状态: False
   • 性能API支持: True
🔍 ================================
2025-08-04 23:32:35 [信息]   ===== 浏览器指纹详细信息 =====  基础硬件信息:    • CPU核心数: 18    • 设备内存: 12 GB    • 平台信息: Win32    • Do Not Track: manual   图形渲染信息:    • WebGL渲染器: ANGLE (AMD Radeon RX 7600 Direct3D11 vs_5_0 ps_5_0)    • WebGL厂商: Google Inc. (Intel Corp.)    • Canvas指纹: ...1QAAAABJRUcanvas_f==    • Canvas自定义ID: True   音频媒体信息:    • AudioContext ID: 1A2B3C4D    • 媒体设备支持: True    • 媒体设备数量: 3    • 语音合成ID: noise_011    • 语音数量: 3   设备环境信息:    • 设备名称: DESKTOP-87EL3RX    • MAC地址: 78-9A-BC-DE-F0-12    • 屏幕分辨率: 1816x1047    • 可用区域: 1816x1007   地区语言信息:    • 主语言: zh-CN    • 语言列表: zh-CN,en-US    • 时区偏移: 300分钟   高级功能信息:    • ClientRects ID: E7F8A9B0    • WebGPU支持: True    • WebGPU适配器: True    • 插件数量: 0    • MIME类型数量: 0    • 网络连接类型: wimax    • 电池API支持: True    • 电池电量: 0.82    • 电池充电状态: False    • 性能API支持: True  ================================
2025-08-04 23:32:35 [系统状态] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用

2025-08-04 23:32:35 [注册开始] 邮箱: <EMAIL>, 索引: 1/1
2025-08-04 23:32:35 [系统状态] 已清空AWS密钥变量和MFA变量
2025-08-04 23:32:35 [系统状态] 开始新的注册: <EMAIL>，已清空之前的号码状态
2025-08-04 23:32:35 [系统状态] 在现有窗口中新建标签页...
2025-08-04 23:32:35 [系统状态] 使用现有的浏览器上下文
2025-08-04 23:32:35 [系统状态] 正在新建标签页...
2025-08-04 23:32:35 [系统状态] 已设置页面视口大小为600x400
2025-08-04 23:32:35 [系统状态] 验证无痕Chrome模式状态...
2025-08-04 23:32:35 [系统状态] 无痕Chrome检测: ✓ 已启用
2025-08-04 23:32:35 [系统状态] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb...
2025-08-04 23:32:35 [系统状态] 正在打开AWS注册页面...
2025-08-04 23:33:01 [系统状态] 正在执行第一页注册...
2025-08-04 23:33:01 [系统状态] 🔍 等待第一页加载完成...
2025-08-04 23:33:01 [系统状态] ✅ 第一页加载完成，找到验证邮箱按钮
2025-08-04 23:33:01 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-04 23:33:01 [系统状态] 📋 第一页基本信息填写完成，检查页面响应...
2025-08-04 23:33:04 [系统状态] 🔍 检查是否出现IP异常错误...
2025-08-04 23:33:04 [系统状态] ⚠️ 检测到错误信息，开始重试机制...
2025-08-04 23:33:04 [信息] 检测到错误信息，开始重试机制
2025-08-04 23:33:04 [系统状态] 🔄 第1次重试点击验证邮箱按钮...
2025-08-04 23:33:04 [信息] 第1次重试点击验证邮箱按钮
2025-08-04 23:33:06 [系统状态] ✅ 第1次重试：已点击验证邮箱按钮
2025-08-04 23:33:06 [信息] 第1次重试：已点击验证邮箱按钮
2025-08-04 23:33:09 [系统状态] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']"
2025-08-04 23:33:09 [信息] 检查图形验证码时出错: Failed to find frame for selector "iframe >> internal:control=enter-frame  >> img[alt*='captcha']"
2025-08-04 23:33:09 [系统状态] ✅ 第1次重试成功：已到达第二页，继续正常流程
2025-08-04 23:33:09 [信息] 第1次重试成功：已到达第二页
2025-08-04 23:33:09 [系统状态] 🔍 开始检查第一页是否有图形验证码（2次检测）...
2025-08-04 23:33:09 [系统状态] 🔍 第1次检测图形验证码...
2025-08-04 23:33:09 [系统状态] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个
2025-08-04 23:33:09 [系统状态] ⏳ 第1次未发现验证码，等待2秒后继续检测...
2025-08-04 23:33:11 [系统状态] 🔍 第2次检测图形验证码...
2025-08-04 23:33:11 [系统状态] 🔍 第2次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个
2025-08-04 23:33:11 [系统状态] 🔍 第一页图形验证码最终检测结果: 未发现验证码
2025-08-04 23:33:11 [系统状态] ✅ 第一页未检测到图形验证码，继续流程
2025-08-04 23:33:11 [系统状态] 📋 第一页图形验证码检查完成
2025-08-04 23:33:11 [系统状态] 第一页完成，等待验证码页面...
2025-08-04 23:33:11 [系统状态] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True
2025-08-04 23:33:11 [系统状态] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码...
2025-08-04 23:33:11 [系统状态] 正在后台自动获取验证码，继续注册按钮已禁用...
2025-08-04 23:33:43 [系统状态] 验证码获取成功: 196712，正在自动填入...
2025-08-04 23:33:43 [系统状态] 邮箱验证码获取成功，已取消获取线程
2025-08-04 23:33:43 [系统状态] 验证码已自动填入，正在自动点击验证按钮...
2025-08-04 23:33:43 [系统状态] 邮箱验证完成，等待页面跳转...
2025-08-04 23:33:46 [系统状态] 等待密码设置页面加载...
2025-08-04 23:33:46 [系统状态] 开始填写密码信息...
2025-08-04 23:33:47 [系统状态] 第一个密码输入框已清空并重新填写完成
2025-08-04 23:33:47 [系统状态] 确认密码输入框已清空并重新填写完成
2025-08-04 23:33:47 [系统状态] 密码填写完成，点击继续按钮...
2025-08-04 23:33:48 [系统状态] 密码设置完成，等待页面跳转...
2025-08-04 23:33:51 [系统状态] 第三页完成，进入第3.5页（账户类型确认页面）...
2025-08-04 23:33:51 [系统状态] 等待账户类型确认页面加载...
2025-08-04 23:33:51 [系统状态] 开始处理账户类型确认...
2025-08-04 23:34:09 [系统状态] 第3.5页执行失败: Timeout 15000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Button, new() { Name = "Choose paid plan" }) to be visible
2025-08-04 23:34:26 [按钮操作] 继续注册 -> 继续执行注册流程
2025-08-04 23:34:26 [系统状态]  继续注册被调用，当前状态: Paused，当前步骤: 3
2025-08-04 23:34:26 [系统状态]  进行智能页面检测...
2025-08-04 23:34:26 [系统状态] 🔍 开始智能页面检测（按钮优先策略）...
2025-08-04 23:34:26 [系统状态] 📋 获取页面所有按钮和链接元素...
2025-08-04 23:34:26 [系统状态] 🎯 找到匹配按钮: 'Choose paid plan' → 第3页
2025-08-04 23:34:26 [系统状态] 检测到第3.5页（账户类型确认页面），直接点击Choose paid plan按钮...
2025-08-04 23:34:26 [系统状态] 账户类型确认完成，进入第4页（联系信息页面）...
2025-08-04 23:34:29 [系统状态] 检测到注册已暂停或终止，停止后续操作
2025-08-04 23:34:29 [系统状态] ⚠️ 智能检测失败，启用详细页面分析...
2025-08-04 23:34:29 [系统状态] 🔬 执行详细页面分析...
2025-08-04 23:34:29 [系统状态] 📄 页面URL: https://portal.aws.amazon.com/billing/signup?type=register#/accountplan
2025-08-04 23:34:29 [系统状态] 📋 页面标题: AWS Console - Signup
2025-08-04 23:34:31 [系统状态] 📊 分析结果: 未识别的页面
2025-08-04 23:34:31 [系统状态] ❌ 详细分析也无法识别页面
2025-08-04 23:34:31 [系统状态] 智能检测到当前在第3页，开始智能处理...
2025-08-04 23:34:37 [系统状态] 等待密码设置页面加载...
2025-08-04 23:34:54 [系统状态] 开始填写密码信息...
2025-08-04 23:35:03 [系统状态] 第三页执行失败: Timeout 3000ms exceeded.
Call log:
  - waiting for Locator("input[name*='password']") to be visible
2025-08-04 23:35:47 [按钮操作] 继续注册 -> 继续执行注册流程
2025-08-04 23:35:47 [系统状态]  继续注册被调用，当前状态: Paused，当前步骤: 3
2025-08-04 23:35:47 [系统状态]  进行智能页面检测...
2025-08-04 23:35:47 [系统状态] 🔍 开始智能页面检测（按钮优先策略）...
2025-08-04 23:35:47 [系统状态] 📋 获取页面所有按钮和链接元素...
2025-08-04 23:35:47 [系统状态] 🎯 找到匹配按钮: 'Agree and Continue (step 2 of 5)' → 第4页
2025-08-04 23:35:47 [系统状态] ✅ 直接确认为第4页
2025-08-04 23:35:47 [系统状态]  智能检测到当前在第4页
2025-08-04 23:35:47 [系统状态] 智能检测到当前在第4页，开始智能处理...
2025-08-04 23:35:47 [系统状态] 开始后台获取手机号码，同时填写其他信息...
2025-08-04 23:35:47 [信息] [榴莲API] 获取手机号码，尝试 1/4
2025-08-04 23:35:48 [系统状态] 数据国家代码为CL，需要选择Chile
2025-08-04 23:35:48 [系统状态] 已点击国家/地区选择器，正在展开列表...
2025-08-04 23:35:49 [系统状态] 后台获取榴莲手机号码成功: +525633503198，已保存到注册数据
2025-08-04 23:35:50 [系统状态] 已选择国家: Chile
2025-08-04 23:35:50 [系统状态] 已成功选择国家: Chile
2025-08-04 23:35:50 [系统状态] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)...
2025-08-04 23:35:50 [系统状态] 已点击国家代码按钮，正在展开列表...
2025-08-04 23:35:53 [系统状态] 已选择国家代码 +52
2025-08-04 23:35:54 [系统状态] 等待后台获取的手机号码结果...
2025-08-04 23:35:54 [系统状态] 已自动获取并填入手机号码: +525633503198
2025-08-04 23:35:55 [系统状态] 使用已获取的手机号码: +525633503198（保存本地号码: 5633503198）
2025-08-04 23:35:55 [系统状态] 联系信息完成，等待页面加载...
2025-08-04 23:35:58 [系统状态] 进入付款信息页面...
2025-08-04 23:36:00 [系统状态] 正在选择月份: January
2025-08-04 23:36:00 [系统状态] 已选择月份（标准选项）: January
2025-08-04 23:36:01 [系统状态] 正在选择年份: 2029
2025-08-04 23:36:01 [系统状态] 已选择年份（标准选项）: 2029
2025-08-04 23:36:01 [系统状态] 付款信息完成，进入验证码验证页面...
2025-08-04 23:36:01 [系统状态] 开始填写验证码验证页面...
2025-08-04 23:36:01 [系统状态] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)...
2025-08-04 23:36:07 [系统状态] 已点击国家代码按钮，正在展开列表...
2025-08-04 23:36:08 [系统状态] 已选择国家代码: +52
2025-08-04 23:36:08 [系统状态] 已清空并重新填写手机号码: 5633503198
2025-08-04 23:36:09 [系统状态] 已点击发送验证码按钮
2025-08-04 23:36:11 [系统状态] 🔍 检查是否出现验证手机区号错误...
2025-08-04 23:36:11 [系统状态] ✅ 未检测到验证手机区号错误，继续执行
2025-08-04 23:36:11 [系统状态] 手机号码自动模式 + 图形验证码自动模式：开始自动处理...
2025-08-04 23:36:11 [系统状态] 自动模式：开始处理图形验证码...
2025-08-04 23:36:11 [系统状态] 第六页点击发送验证码后，等待图形验证码出现...
2025-08-04 23:36:14 [系统状态] 第六页图形验证码自动识别模式，开始处理...
2025-08-04 23:36:14 [系统状态] 第六页第1次尝试自动识别图形验证码...
2025-08-04 23:36:20 [系统状态] 第六页已从iframe截取验证码图片，大小: 35460 字节
2025-08-04 23:36:20 [系统状态] ✅ 图片验证通过：201x71px，35460字节，复杂度符合要求
2025-08-04 23:36:20 [系统状态] 正在调用Yes打码API识别验证码...
2025-08-04 23:36:22 [系统状态] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"ym87n7"},"taskId":"c5d62984-7148-11f0-ae6e-62c5329370b7"}
2025-08-04 23:36:22 [系统状态] 第六页第1次识别结果: ym87n7 → 转换为小写: ym87n7
2025-08-04 23:36:22 [系统状态] 第六页使用iframe内GetByLabel选择器
2025-08-04 23:36:22 [系统状态] 第六页已填入验证码: ym87n7
2025-08-04 23:36:22 [系统状态] 第六页已点击iframe内Submit按钮
2025-08-04 23:36:26 [系统状态] 第1次图形验证码识别结果错误，等待新验证码
2025-08-04 23:36:26 [系统状态] 第六页第1次识别异常: 验证码错误
2025-08-04 23:36:28 [系统状态] 第六页第2次尝试自动识别图形验证码...
2025-08-04 23:36:31 [系统状态] 第六页已从iframe截取验证码图片，大小: 35844 字节
2025-08-04 23:36:31 [系统状态] ✅ 图片验证通过：201x71px，35844字节，复杂度符合要求
2025-08-04 23:36:31 [系统状态] 正在调用Yes打码API识别验证码...
2025-08-04 23:36:33 [系统状态] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"sm7rmt"},"taskId":"cc772b76-7148-11f0-9768-ba03bdd70631"}
2025-08-04 23:36:33 [系统状态] 第六页第2次识别结果: sm7rmt → 转换为小写: sm7rmt
2025-08-04 23:36:33 [系统状态] 第六页使用iframe内GetByLabel选择器
2025-08-04 23:36:33 [系统状态] 第六页已填入验证码: sm7rmt
2025-08-04 23:36:33 [系统状态] 第六页已点击iframe内Submit按钮
2025-08-04 23:36:37 [系统状态] 第2次图形验证码识别结果错误，等待新验证码
2025-08-04 23:36:37 [系统状态] 第六页第2次识别异常: 验证码错误
2025-08-04 23:36:39 [系统状态] 第六页第3次尝试自动识别图形验证码...
2025-08-04 23:36:43 [系统状态] 第六页已从iframe截取验证码图片，大小: 35085 字节
2025-08-04 23:36:43 [系统状态] ✅ 图片验证通过：201x71px，35085字节，复杂度符合要求
2025-08-04 23:36:43 [系统状态] 正在调用Yes打码API识别验证码...
2025-08-04 23:36:45 [系统状态] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"d3btby"},"taskId":"d3c77dea-7148-11f0-9768-ba03bdd70631"}
2025-08-04 23:36:45 [系统状态] 第六页第3次识别结果: d3btby → 转换为小写: d3btby
2025-08-04 23:36:45 [系统状态] 第六页使用iframe内GetByLabel选择器
2025-08-04 23:36:45 [系统状态] 第六页已填入验证码: d3btby
2025-08-04 23:36:45 [系统状态] 第六页已点击iframe内Submit按钮
2025-08-04 23:36:48 [系统状态] 第3次图形验证码识别成功
2025-08-04 23:36:48 [系统状态] 第六页图形验证码自动完成，检查验证结果...
2025-08-04 23:36:51 [系统状态] 第六页图形验证码验证成功，进入第七页
2025-08-04 23:36:54 [系统状态] 开始处理第七页 - Continue (step 4 of 5) 页面
2025-08-04 23:36:54 [系统状态] 第七页自动模式：开始自动获取手机验证码...
2025-08-04 23:36:54 [系统状态] 第七页自动模式：开始自动获取手机验证码...
2025-08-04 23:36:54 [系统状态] 第七页自动模式：等待5秒后开始获取验证码...
2025-08-04 23:36:59 [系统状态] 开始自动获取验证码，2分钟超时...
2025-08-04 23:36:59 [系统状态] 第1次尝试获取验证码...（剩余4次尝试）
2025-08-04 23:36:59 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-04 23:37:00 [系统状态] 成功获取验证码: 6197，立即填入验证码...
2025-08-04 23:37:00 [系统状态] 第七页：找到验证码输入框 (Verify code)
2025-08-04 23:37:00 [系统状态] 已自动填入手机验证码: 6197
2025-08-04 23:37:00 [系统状态] 榴莲手机号码已加入黑名单
2025-08-04 23:37:01 [系统状态] 正在自动点击Continue按钮...
2025-08-04 23:37:01 [系统状态] 手机验证码验证完成，继续执行后续步骤...
2025-08-04 23:37:04 [系统状态] 检测到无资格错误提示: You are not eligible for new customer credits
2025-08-04 23:37:04 [系统状态] 检测到卡号已被关联错误，注册失败
2025-08-04 23:37:04 [系统状态] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：ZEGQp06L3sU ③AWS密码：C9s41WbR ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-04 23:37:04 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：ZEGQp06L3sU ③AWS密码：C9s41WbR ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-04 23:37:04 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：ZEGQp06L3sU ③AWS密码：C9s41WbR ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-04 23:37:04 [信息] 检测到所有数据处理完成，已完全重置按钮状态
2025-08-04 23:37:04 [信息] 已处理失败数据: <EMAIL>, 失败原因: 卡号已被关联
2025-08-04 23:37:04 [系统状态] 已通知保存失败数据，失败原因: 卡号已被关联
2025-08-04 23:37:07 [系统状态] 注册失败，数据已归类，注册流程终止
2025-08-04 23:37:09 [信息] 程序正在退出，开始清理工作...
2025-08-04 23:37:09 [信息] 程序退出，开始清理所有待拉黑千川手机号码
2025-08-04 23:37:09 [信息] 程序退出清理完成: 没有需要拉黑的千川手机号码
2025-08-04 23:37:09 [信息] 程序退出，开始清理所有待释放手机号码
2025-08-04 23:37:09 [信息] 程序退出清理完成: 没有需要释放的手机号码
2025-08-04 23:37:09 [信息] 程序退出清理工作完成
